﻿#include "stdafx.h"
#include <kcomctl/kcommandfactory.h>
#include <kxshare/kxtaskpane.h>
#include <kxshare/kxsubwindow.h>
#include <public_header/security/corex.h>
#include <security/utils.h>
#include <kxshare/kxtasktabbar.h>
#include "kxetcommonheander.h"
#include "kxtptablestylecommand.h"
#include "kxonlinereshelper.h"
#include "kxonlinetablestyledefine.h"
#include "utilities/path/module/kcurrentmod.h"
#include "kdocertoolkit/docerfeature/kxdocertpfeatureutil.h"
#include "kdocertoolkit/kdocerutils.h"
#include "inserttable/kinsertonlinetablecmd.h"
#include "kso/wps_any.h"
#include <typeinfo>
#include "kso/l10n/kso/kso.h"
#include <kxshare/kxtpformatting.h>

using namespace KxOnlineTableResHelper;

static void getSelectionShapeRange(KsoShapeRange** ppv)
{
	if (!kxApp || !kxApp->coreApplication() || !ppv)
		return;
	IKView* pActView = kxApp->coreApplication()->GetActiveView();
	if (!pActView)
		return;
	IKSelection* pselection = pActView->GetSelection();
	if (!pselection)
		return;

	VARIANT_BOOL hasChildRange = VARIANT_FALSE;
	HRESULT hr = pselection->HasLeafChildShapeRange(&hasChildRange);
	if (FAILED(hr))
		return;
	ks_stdptr<KsoShapeRange> spShapeRange;
	if (hasChildRange == VARIANT_TRUE)
		hr = pselection->GetLeafChildShapeRange(&spShapeRange);
	else
		hr = pselection->GetLeafShapeRange(&spShapeRange);
	if (SUCCEEDED(hr) && spShapeRange)
		*ppv = spShapeRange.detach();
}

KxDocerTpTableStyleCommand::KxDocerTpTableStyleCommand(KxMainWindow* host, QObject* parent)
	: KCommand(host, parent)
	, m_host(host)
	, m_bOnceFlag(true)
{
	KxOnlineTableResHelper::installTranslator();
	m_previewTr = new KTransPreview(this);
}

KxDocerTpTableStyleCommand::~KxDocerTpTableStyleCommand()
{
	KxOnlineTableResHelper::uninstallTranslator();
}

void KxDocerTpTableStyleCommand::update()
{
	if (!KDocerUtils::isCoreAppMatch(this))
		return;

	KScopedExecGuard execGuard(GC_SHELL_COMMAND_UPDATE, __X("KxDocerTpTableStyleCommand"));

	PanelEnableStatus oldStatus = m_panelStatus;
	if (KDocerUtils::getCoreApplication(this))
	{
		IKDocument* pActDoc = KDocerUtils::getCoreApplication(this)->GetActiveDocument();
		if (pActDoc && pActDoc->IsCompatibilityMode())
		{
			m_panelStatus = PanelEnableStatus::CompatibilityMode;
			this->setEnabled(false);
			checkStatusChange(oldStatus);
			return;
		}
	}

	m_panelStatus = PanelEnableStatus::Enable;
	do 
	{
		QMdiSubWindow* qSubWin = m_host->getMdiArea()->currentSubWindow();
		if (!qSubWin)
		{
			m_panelStatus = PanelEnableStatus::OtherErrorStatus;
			break;
		}
		if (!qobject_cast<KxSubWindow*>(qSubWin->widget()))
		{
			m_panelStatus = PanelEnableStatus::OtherErrorStatus;
			break;
		}
		if (IsSecurityDocReadOnly(KDocerUtils::getCoreApplication(this)))
		{
			m_panelStatus = PanelEnableStatus::SecureDocReadOnly;
			break;
		}
		m_panelStatus = judgeEnableInsertTable();
	} while (false);
	this->setEnabled(m_panelStatus == PanelEnableStatus::Enable);
	checkStatusChange(oldStatus);
	updateCurtableTableIndex();
}

PanelEnableStatus KxDocerTpTableStyleCommand::judgeEnableInsertTable()
{
	return PanelEnableStatus::Enable;
}

OperatorErrorCode KxDocerTpTableStyleCommand::applyTableByTableInfo(const TableInfo& tableInfo)
{
	return InsertOpNotImple;
}

bool KxDocerTpTableStyleCommand::F4Repeat()
{
	if (!canRepeat())
		return false;
	auto* pCopyManager = KDocerUtils::getCoreApplication(this)->GetF4CopyManager();
	if (!pCopyManager)
		return false;

	const std::vector<wps_any::Any>& copyData = pCopyManager->doGetCopyData();
	START_FMT_TRANS(KDocerUtils::getCoreApplication(this), _kso_F4Repeat);
	auto resourceInfo = wps_any::any_cast<KOTSResourceInfo>(copyData[0]);
	auto filePath = wps_any::any_cast<QString>(copyData[1]);
	QFileInfo fileInfo(filePath);
	if (!fileInfo.exists())
		return false;

	QFile file(fileInfo.absoluteFilePath());
	if (!file.open(QIODevice::ReadOnly))
		return false;

	OperatorErrorCode errorCode = applyTableStyleDocument(resourceInfo, file.readAll(), false);
	if (errorCode != OperatorErrorCode::Success)
		return false;
	COMMIT_FMT_TRANS();
	return true;
}

void KxDocerTpTableStyleCommand::applySystemTableStyle(int resourceIndex, bool hover)
{
	OperatorErrorCode errorCode = Success;

	errorCode = applySystemTableStyleDocument(resourceIndex, hover);

	if (errorCode == Success && hover)
		emit hoverSystemResourceSuccess();

	if (!hover)
		emit sendSystemInsertInfo(resourceIndex, errorCode, getErrorDesc(errorCode));
	if ((KDocerUtils::isWpsApp(this) || KDocerUtils::isWppApp(this))
		&& errorCode == InsertOpNoActiveTable)
	{
		QString strInfo = tr("To use the docer table style, insert the table first, and then use the docer table style after selecting the number of rows and columns to be inserted in Insert-Table.");
		kxApp->messageBox(strInfo, MB_ICONEXCLAMATION | MB_OK);
	}
}

void KxDocerTpTableStyleCommand::onLeaveHoverPreview()
{
	if (m_previewTr->isInPreview())
	{
		m_previewTr->endPreview();
		kxApp->ForceIdle();
	}
}

void KxDocerTpTableStyleCommand::applyTableStyle(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, const QString& filePath, bool hover)
{
	OperatorErrorCode errorCode = Success;
	qint64 applyStartTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

	do
	{
		QFileInfo fileInfo(filePath);
		if (!fileInfo.exists()){
			errorCode = StyleFileDamage;
			break;
		}

		QFile file(fileInfo.absoluteFilePath());
		if(!file.open(QIODevice::ReadOnly)){
			errorCode = StyleFileDamage;
			break;
		}

		if (hover)
		{
			// 如果是悬浮预览，需要记录标脏情况，并事后恢复
			bool bDirty = getActiveDocDirty();
			errorCode = applyTableStyleDocument(resourceInfo, file.readAll(), hover);
			if (!bDirty)
				setActiveDocDirty(FALSE);

			if (errorCode == Success)
				emit hoverOnlineResourceSuccess();
		}
		else
		{
			errorCode = applyTableStyleDocument(resourceInfo, file.readAll(), hover);

			if (errorCode == OperatorErrorCode::Success)
				recordRepeatInfo(resourceInfo, filePath);
		}
	} while (false);

	if (!hover || resourceInfo.bHoverReturnResult)
	{
		ReportInfo _reportInfo = reportInfo;
		m_tableApplyInfo.beautifyTime = QDateTime::currentDateTime().toMSecsSinceEpoch() - applyStartTime;
		m_tableApplyInfo.totalTime = QDateTime::currentDateTime().toMSecsSinceEpoch() - _reportInfo.tableApplyInfo.totalStartTime;
		_reportInfo.tableApplyInfo = m_tableApplyInfo;
		emit sendInsertInfo(resourceInfo, _reportInfo, errorCode, getErrorDesc(errorCode));
	}

	if ((KDocerUtils::isWpsApp(this) || KDocerUtils::isWppApp(this))
		&& errorCode == InsertOpNoActiveTable)
	{
		QString strInfo = tr("To use the docer table style, insert the table first, and then use the docer table style after selecting the number of rows and columns to be inserted in Insert-Table.");
		kxApp->messageBox(strInfo, MB_ICONEXCLAMATION | MB_OK);
	}
}

OperatorErrorCode KxDocerTpTableStyleCommand::applyTableStyleDocument(const KOTSResourceInfo& resourceInfo, const QByteArray& content, bool hover)
{
	return InsertOpNotImple;
}

OperatorErrorCode KxDocerTpTableStyleCommand::applySystemTableStyleDocument(int resourceIndex, bool hover)
{
	return InsertOpNotImple;
}

void KxDocerTpTableStyleCommand::checkStatusChange(PanelEnableStatus oldStatus)
{
	//status change
	if (m_bOnceFlag || (oldStatus != m_panelStatus))
	{
		emit pannelEnable(m_panelStatus);

		m_bOnceFlag = false;
	}
}

bool KxDocerTpTableStyleCommand::insertTableByCommand()
{
	KxMainWindow* mw = qobject_cast<KxMainWindow*>(host());
	if (!mw)
		mw = kxApp->currentMainWindow();
	KCommand* insertTableCmd = KDocerUtils::findCommand(mw, "InsertStyleTable");
	if (KProxyCommand* proxyCmd = qobject_cast<KProxyCommand*>(insertTableCmd))
	{
		if (!proxyCmd->targetCommand())
		{
			proxyCmd->setDelayLoadLib(false);
			proxyCmd->loadOnlyLocal();
		}
		insertTableCmd = proxyCmd->targetCommand();

		KInsertOnlineTableStyleCommand* galleryCommand = qobject_cast<KInsertOnlineTableStyleCommand*>(insertTableCmd);
		if (galleryCommand)
		{
			return galleryCommand->InsertTable() == Success;
		}
	}

	return false;
}

void KxDocerTpTableStyleCommand::recordRepeatInfo(const KOTSResourceInfo& resourceInfo, const QString& filePath)
{
	if (!KDocerUtils::getCoreApplication(this))
		return;
	auto* pCopyManager = KDocerUtils::getCoreApplication(this)->GetF4CopyManager();
	if (!pCopyManager || pCopyManager->IsExecuting() || !pCopyManager->actionIsRepeatSupport(F4RepeatEventType::FormAction))
		return;

	pCopyManager->DoRecord(new KF4CopyDirectCommand<KxDocerTpTableStyleCommand>(this), krt::utf16(GetRedoRepeatInfo()), kxApp->findRelativeMainWindowX(this));
	std::vector<wps_any::Any> copyData{ resourceInfo, filePath };
	pCopyManager->doSetCopyData(copyData);
}

bool KxDocerTpTableStyleCommand::canRepeat()
{
	if (!KDocerUtils::getCoreApplication(this) || !KDocerUtils::getCoreApplication(this)->GetF4CopyManager())
		return false;

	//重做区分插入和设置样式两种行为
	IKF4CopyManager* pCopyManager = KDocerUtils::getCoreApplication(this)->GetF4CopyManager();
	const std::vector<wps_any::Any>& copyData = pCopyManager->doGetCopyData();
	if (copyData.size() == 2 && copyData[0].has_value() && copyData[0].is_type(typeid(KOTSResourceInfo))
		&& copyData[1].has_value() && copyData[1].is_type(typeid(QString)))
	{
		auto resourceInfo = wps_any::any_cast<KOTSResourceInfo>(copyData[0]);
		if (resourceInfo.bInsertTable)
		{
			//插入判断
			if (judgeEnableInsertTable() != PanelEnableStatus::Enable)
				return false;
		}
		else
		{
			//样式设置判断，需要选中单个表格
			ks_stdptr<KsoShapeRange> spShapeRange;
			getSelectionShapeRange(&spShapeRange);
			if (!spShapeRange)
				return false;

			int nCount = 0;
			HRESULT hr = spShapeRange->get_Count(&nCount);
			if (FAILED(hr) || nCount != 1)
				return false;
			ks_stdptr<KsoShape> spShape;
			hr = spShapeRange->_Item(1, &spShape);
			if (FAILED(hr) || !spShape)
				return false;
			KsoShapeType type;
			hr = spShape->get__Type(&type);
			if (FAILED(hr) || type != oldapi::ksoTable)
				return false;
		}
	}
	else
	{
		ASSERT(!"FuncRepeat parameter number or type incorrect.");
		return false;
	}

	return true;
}

void KxDocerTpTableStyleCommand::updateCurtableTableIndex()
{

}


DECLARE_COMMAND_FACTORY(KxDocerTpTableStyleCommand, KxMainWindow);
