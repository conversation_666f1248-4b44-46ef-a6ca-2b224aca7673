﻿#pragma once

#ifndef SIMPIMPL
#define <PERSON><PERSON><PERSON><PERSON><PERSON>															\
	{ return S_OK; }
#endif

#define NOTIMPL_(value)														\
{ REPORT_ONCEA("Not Implement!!!"); return value; }						\
	friend class __unused_class

#ifndef NOTIMPL
#define NOTIMPL																\
{ REPORT_ONCEA("Not Implement!!!"); return E_NOTIMPL; }
#endif

#define __SHELL_MODULE__
#include <kfc.h>
#include <atl/atlbase.h>

#ifdef X_OS_LINUX
typedef long WPARAM;
typedef long LPARAM;
#endif

#define KX_DECLARE_COUNT(counter)																		\
	ULONG STDMETHODCALLTYPE AddRef(){return ++counter;}														\
	ULONG STDMETHODCALLTYPE Release(){--counter; if (counter == 0){delete this; return 0;} return counter;}


#undef DocumentProperties
#include <kso/api/ksoapi_old.h>
using namespace oldapi;

#include <kso/shell.h>
#include <kso/shell/shell_tlb_old.h>

#include <kxshare/kxshare.h>

#include <QtCore/QtCore>
#include <QtGui/QtGui>
#include <QtWidgets/QtWidgets>
#include <QDomElement>
#include <kxshare/kxcommands.h>

#define KPluginName "konlinetablestyle"
