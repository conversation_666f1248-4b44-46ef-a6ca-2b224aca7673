﻿#ifndef __KXWPPTABLESTYLEPARSER_H__
#define __KXWPPTABLESTYLEPARSER_H__
#include <kso/framework/api/wppapi_old_enum.h>
#include "kxonlinetablestyledefine.h"

class KxCommonTableStyleParser;
namespace KxCommonTableStyleInfo
{
	enum PartStyleType :int;
}

class KxWppTableStyleHelper : public QObject
{
	Q_OBJECT
public:
	static QSharedPointer<KxWppTableStyleHelper>& instance()
	{
		if (m_pInstance.isNull())
		{
			QMutexLocker mutexLocker(&m_Mutex);
			if (m_pInstance.isNull())
				m_pInstance = QSharedPointer<KxWppTableStyleHelper>(new KxWppTableStyleHelper());
		}
		return m_pInstance;
	}

	KxWppTableStyleHelper()
	{

	}

	bool applyStyle(const KOTSResourceInfo& resourceInfo, const QByteArray& data);
	bool applyStyleBySystemIndex(int index);
	WpViewType getWppViewType();
private:
	bool addStyle(const KxCommonTableStyleParser& parser, QString& outGuid);
	bool applyStyle(const QString& strGuid, const QList<KxCommonTableStyleInfo::PartStyleType> optionList);
private:
	static QMutex m_Mutex;
	static QSharedPointer<KxWppTableStyleHelper> m_pInstance;
private:
	QMap<int, QByteArray> m_systemStyleByteArray;
};

class KxWppTableStylePopupHelper : public QObject
{
	Q_OBJECT

public:
	static KxWppTableStylePopupHelper* instance()
	{
		static KxWppTableStylePopupHelper ins;
		return &ins;
	}

	~KxWppTableStylePopupHelper()
	{

	}

	void setNotAutoPopupWithinSevenDays(const QString& group, const QString& action, bool value);
	bool isNotAutoPopupWithinSevenDays(const QString& group, const QString& action);
	bool canAutoPopupQhSinceLastTime(const QString& group, const QString& action);
	void setAutoPopupFrequencyOneDay(const QString& group, int frequency);
	void updateLastAutoPopupInfo(const QString& group, const QString& action);
	void tabIndexChanged(QObject* sender, int index, const QString& targetId);
	int getInitTabIndex() const;
	QString getInitTabTargetId() const;
	void setOpenType(const QString& openType);
	bool shouldAutoPopup() const;

private:
	struct LastAutoPopupInfo;

	KxWppTableStylePopupHelper();

	QString getDefaultQhInfoDirPath();
	void setLastAutoPopupTime(const QString& group, const QString& action);
	void increaseAutoPopupCounts(const QString& group, const QString& action);
	void resetAutoPopupCountsIfNeeded(const QString& group, const QString& action);
	LastAutoPopupInfo getLastAutoPopupInfo(const QString& group, const QString& action);

	int m_initTabIndex = 0;
	QString m_initTargetId = "processTableStyleBeautify";
	QString m_openType = "normal";
	bool m_bShouldAutoPopup = false;
};

#endif