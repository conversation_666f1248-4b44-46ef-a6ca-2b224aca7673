﻿// ------------------------------------------------------------
// Desc : 处理信息收集
// ------------------------------------------------------------

#include "stdafx.h"
#include "ktablestyleinfocollect.h"
#include "kdocertoolkitlite/account/kdoceraccount.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include "kxonlinereshelper.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
void KTableStyleInfoCollect::reportData(const QString& event, QHash<QString, QString>& args, bool bPostLoginState, bool bPostEventTime, bool bInsertTable)
{
	if (!event.isEmpty() && !args.isEmpty())
	{
		args.insert("entry_id", KDocerUtils::generatePayKey());
		args.insert("func_version", bInsertTable ? QLatin1String("2024.08") : QLatin1String("2024.06"));
		args.insert("pre_klm", "");
		args.insert("component", kxApp->applicationName());

		if (bPostLoginState)
			args.insert("is_login", KDocerAccount::isLogined() ? "true" : "false");
		if (bPostEventTime)
			args.insert("event_time", QDateTime::currentDateTime().toStringEx("yyyy-MM-dd hh:mm:ss"));
		KDocerUtils::postGeneralEvent(event, args);
	}
}

void KTableStyleInfoCollect::postDispalyInfo(QHash<QString, QString>& args)
{
	args.insert("element_type", "page");
	args.insert("klm", "docer_beautify.table_dropdown_page.0.0[page]");
	reportData("docer_beautify_display", args, false, false, false);
}

void KTableStyleInfoCollect::postStayInfo(QHash<QString, QString>& args, DWORD duration)
{
	args.insert("element_type", "page");
	args.insert("duration", QString::number(duration));
	args.insert("klm", "docer_beautify.table_dropdown_page.0.0[page]");
	reportData("docer_beautify_stay", args, false, false, false);
}

void KTableStyleInfoCollect::postLoadInfo(QHash<QString, QString>& args)
{
	args.insert("element_type", "page");
	args.insert("klm", "docer_beautify.table_dropdown_page.0.0[page]");
	reportData("docer_beautify_load", args, false, false, false);
}

void KTableStyleInfoCollect::postMoreBtnClicked(QHash<QString, QString>& args)
{
	args.insert("element_name", "more");
	args.insert("element_type", "button");
	args.insert("page_name", "table_dropdown_page");
	args.insert("klm", "docer_beautify.table_dropdown_page.resource_list.more[button]");
	reportData("docer_beautify_click", args, false, true, false);
}

void KTableStyleInfoCollect::postResourcePageDisplayInfo(QHash<QString, QString>& args, int resourceCnt)
{
	args.insert("element_type", "module");
	args.insert("resource_count", QString::number(resourceCnt));
	args.insert("klm", "docer_beautify.table_dropdown_page.resource_list.0[module]");
	reportData("docer_beautify_display", args, true, true, false);
}

void KTableStyleInfoCollect::postResourceDisplayInfo(QHash<QString, QString>& args)
{
	reportData("docer_beautify_display", args, true, true, false);
}

void KTableStyleInfoCollect::postResourceHoveredInfo(QHash<QString, QString>& args)
{
	reportData("docer_beautify_hover", args, true, true, false);
}

void KTableStyleInfoCollect::psotResourceClicked(QHash<QString, QString>& args)
{
	reportData("docer_beautify_click", args, true, true, false);
}

void KTableStyleInfoCollect::postKLMEvent(const QString& eventName, const KTableReportInfo& info, bool bPostLogin, bool bPostEventTime, bool bInsert)
{
	QString func = info.func.isEmpty() ? "docer_beautify" : info.func;
	QHash<QString, QString> postArgs = info.args;
	postArgs["func"] = func;
	postArgs["page_name"] = info.pageName;
	postArgs["module_name"] = info.moduleName;
	postArgs["element_name"] = info.elementName;
	postArgs["element_type"] = info.elementType;
	postArgs["first_entry"] = info.firstEntry;
	if(!info.secondEntry.isEmpty())
		postArgs["second_entry"] = info.secondEntry;
	if(!bInsert)
		postArgs["second_func"] = "table";
	QString klm = func %
		QLatin1String(".") % info.pageName %
		QLatin1String(".") % info.moduleName %
		QLatin1String(".") % info.elementName %
		QLatin1String("[") % info.elementType % QLatin1String("]");
	if (info.idx != -1)
	{
		postArgs["element_position"] = QString::number(info.idx);
		klm.append(QString("(%1)").arg(info.idx));
	}
	postArgs["klm"] = klm;

	reportData(eventName, postArgs, bPostLogin, bPostEventTime, bInsert);
}

void KTableStyleInfoCollect::postInsertDisplayEvent(const KTableReportInfo& info)
{
	postKLMEvent("docer_inserttable_display", info, true, true, true);
}

void KTableStyleInfoCollect::postInsertButtonClick(const KTableReportInfo& info)
{
	postKLMEvent("docer_inserttable_click", info, true, true, true);
}

void KTableStyleInfoCollect::postInsertButtonHover(const KTableReportInfo& info)
{
	postKLMEvent("docer_inserttable_hover", info, true, true, true);
}

void KTableStyleInfoCollect::postInsertCustomColor(const KTableReportInfo& info, const QString& type, const QColor& color)
{
	auto postInfo = info;
	postInfo.args["customize_type"] = type;
	postInfo.args["color_hex"] = KxOnlineTableResHelper::qColorToHex(color);
	postKLMEvent("docer_inserttable_result", postInfo, true, true, true);
}
