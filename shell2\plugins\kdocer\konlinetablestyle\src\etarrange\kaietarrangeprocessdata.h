﻿#pragma once

#include "kaietrecognizedata.h"

interface IStopProcess;

namespace KArrangeProcessHelper
{
	float viewWidth2CharsWidth(int viewWidth, IKWorkbook* pWorkbook,bool bNeedDeviation = true);
}

enum AdaptScreenType
{
	AdaptScreenNormal,//自适应
	AdaptScreenRowFirst,//行适应
	AdaptScreenColFirst,//列适应
};

enum SpacingType
{
	RowSpacing = 0,//行留白间距
	ColSpacing,//列留白间距
};

enum WholeEffectType
{
	DefaultEffect = -1,
	AdaptEffect = 0,
	CompactEffect,
	LooseEffect,
	AdaptScreenEffect,
};

enum MergeCellType
{
	HorMergeCell = 0,//横向合并
	VerMergeCell,//纵向合并
	HorAndVerMergeCell,//多维合并

};

class WholeEffectBase
{
public:
	WholeEffectBase();
	virtual ~WholeEffectBase();
	virtual double getColSpacingWithChar(double colWidthWidthChar) = 0;
	virtual double getColMaxWidthWithChar();
	virtual double getImgCellColMinWidth();
	virtual double getImgCellRowMinHeight();

	virtual double getRowSpaingWithChar();

	virtual single getFontSizeByZoneType(kaietrecognize::ZoneType zoneType);
	virtual single getTitleRangeFontSize();
	virtual single getHeadRangeFontSize();
	virtual single getContentRangeFontSize();
	virtual single getSubTitleRangeFontSize();
	virtual single getOtherRangeFontSize();
	virtual single getInfoRangeFontSize();
public:
	//字号
	int m_TitleRangeFontSize;
	int m_SubTitleRangeFontSize;
	int m_HeadRangeFontSize;
	int m_ContentRangeFontSize;
	int m_OtherRangeFontSize;
	int m_InfoRangeFontSize;
	//间距
	int m_RowHeightSpacing;
	//列宽最大值
	double m_ColMaxWidth;
	//嵌入图片单元格的行高列宽最小值
	double m_ImgCellColMinWidth;
	double m_ImgCellRowMinHeight;
};

class AdaptWholeEffect :public WholeEffectBase
{
public:
	AdaptWholeEffect();
	virtual ~AdaptWholeEffect();
	double getColSpacingWithChar(double colWidthWidthChar) override;
};

class CompactWholeEffect :public WholeEffectBase
{
public:
	CompactWholeEffect();
	virtual ~CompactWholeEffect();
	double getColSpacingWithChar(double colWidthWidthChar) override;
};

class LooseWholeEffect :public WholeEffectBase
{
public:
	LooseWholeEffect();
	virtual ~LooseWholeEffect();
	double getColSpacingWithChar(double colWidthWidthChar) override;
};

class AdaptScreenWholeEffect :public WholeEffectBase
{
public:
	AdaptScreenWholeEffect();
	virtual ~AdaptScreenWholeEffect();
	double getColSpacingWithChar(double colWidthWidthChar) override;
};

class TableApplyParam
{
public:
	explicit TableApplyParam(WholeEffectType effectType = AdaptEffect);
	~TableApplyParam();
	void setEffectType(WholeEffectType effectType);
	WholeEffectType getEffectType();
	WholeEffectBase* getWholeEffectInfo();
	bool isNeedProcessBlank();
	QString getEffectTypeName() const;

public:
	WholeEffectType m_effectType;
	WholeEffectBase* m_pWholeEffect;
	bool bProcessBlank;//是否处理空格
};

struct AtomicRange
{
public:
	AtomicRange();
	AtomicRange(const AtomicRange& rg);
	AtomicRange(etoldapi::Range* pRange);
	AtomicRange(const ES_CUBE& cube);
	AtomicRange(long nT, long nB, long nL, long nR);
	virtual ~AtomicRange();
	bool IsOneCell() const;
	bool IsInvalid() const;
	bool IsInRange(long iRow, long iCol) const;
	bool IsEqualRange(AtomicRange* pAtomicRange);

	bool IsOneRowCell() const;
	bool isOneColCell() const;

	int getRowCnt();
	int getColCnt();

	void ResetRange(etoldapi::Range* pRange);

public:
	long iTop;
	long iBottom;
	long iLeft;
	long iRight;
};

struct AtomicCells : public AtomicRange
{
public:
	AtomicCells(etoldapi::Range* pRange) :AtomicRange(pRange)
	{
		initDefaultProp();
	}
	AtomicCells(const AtomicRange& rg) :AtomicRange(rg)
	{
		initDefaultProp();
	}
	AtomicCells& operator=(const AtomicCells& rs)
	{
		iTop = rs.iTop;
		iBottom = rs.iBottom;
		iLeft = rs.iLeft;
		iRight = rs.iRight;

		m_strContent = rs.m_strContent;
		m_zoneType = rs.m_zoneType;
		m_contentWidthWithChar = rs.m_contentWidthWithChar;
		m_eachParaTextWidthVec = rs.m_eachParaTextWidthVec;
		m_emptyLineCnt = rs.m_emptyLineCnt;

		m_dCharHeight = rs.m_dCharHeight;
		m_bTextOverflow = rs.m_bTextOverflow;
		m_bLineBreak = rs.m_bLineBreak;
		m_bHasFormula = rs.m_bHasFormula;
		m_bImgFmlaCell = rs.m_bImgFmlaCell;

		m_fFontSize = rs.m_fFontSize;
		m_indentLevel = rs.m_indentLevel;
		m_bBold = rs.m_bBold;
		m_fontName = rs.m_fontName;

		m_bWrapText = rs.m_bWrapText;
		m_hAlign = rs.m_hAlign;
		m_VAlign = rs.m_VAlign;
		m_bModifyText = rs.m_bModifyText;

		return *this;
	}
	void BuildCells(etoldapi::Range* pCell, kaietrecognize::ZoneType zoneType);
	void InitCellsProp(float fontSize, bool bAllowProcessBlank);

	////
	double estimatedCellHeight(double dCurWidth, bool bTextOverflow = false);

public:
	bool isNeedSpecialApplyProp();
	float getEstWidthWithChar();
	QVector<int>& getEachParaTextWidthVec();
	double getCellCharHeight();
	double getCellEstimatedHeight(double dCurWidth);
	void setTextOverflow(bool bTextOverflow);
	bool isContainLineBreak();
	bool isModifiedText();
	ks_wstring getModifiedText();
	bool isImgFmlaCell();

	kaietrecognize::ZoneType getZoneType();

	////设置属性
	void setWrapText(bool bWrapText);
	bool getWrapText();

	void setHAlign(ETHAlign hAlign);
	ETHAlign getHAlign();
	void setVAlign(ETVAlign vAlign);
	ETVAlign getVAlign();

	//字体相关属性
	float getFontSize();
	int getIndentLevel();
	bool isFontBold();
	QString getFontName();

private:
	void initDefaultProp();
	void initCharHeight();
	int estTextWidth(ks_wstring text);
	void doEstWidth();
	void processCellBlank();
private:
	ks_wstring m_strContent;
	kaietrecognize::ZoneType m_zoneType;
	float m_contentWidthWithChar;//单位：字符
	QVector<int> m_eachParaTextWidthVec; //单位：磅
	int m_emptyLineCnt;

private:
	double m_dCharHeight;
	bool m_bTextOverflow;
	bool m_bLineBreak;
	bool m_bHasFormula;
	bool m_bImgFmlaCell;

private:
	float m_fFontSize;
	int m_indentLevel;
	bool m_bBold;
	QString m_fontName;

	bool m_bWrapText;
	ETHAlign m_hAlign;
	ETVAlign m_VAlign;
	bool m_bModifyText;//标记文本是否发生了改变
};


struct AtomicRow
{
public:
	std::vector<AtomicCells*> vecCells;
	AtomicRow(long i) : m_iRow(i), m_dOriginHeight(0), m_dResultHeight(0), m_bHidden(false)
	{}
	void setHidden(bool bHidden)
	{
		m_bHidden = bHidden;
	}
	bool isHidden()
	{
		return m_bHidden;
	}
	int getRowIdx();
	void setRowResultHeight(double dHeight);
	double getRowResultHeight();
private:
	int m_iRow;
	double m_dOriginHeight;
	double m_dResultHeight;
	bool m_bHidden;
};

typedef std::map<int, AtomicRow*> AtomicRowMap;

struct AtomicCol
{
public:
	std::vector<AtomicCells*> vecCells;
	AtomicCol(long i) : m_iCol(i), m_bNeedAlignLeft(false), m_dOriginWidth(0), m_dResultWidth(0), m_dMaxWidth(50), m_bHidden(false)
	{}
	void setHidden(bool bHidden)
	{
		m_bHidden = bHidden;
	}
	bool isHidden()
	{
		return m_bHidden;
	}
	int getColIdx();
	void calcColResultWidth(WholeEffectBase* pWholeEffect);
	void setColResultWidth(double dWidth);
	double getColResultWidth();

	void setColOriginWidth(double dWidth);
	void setColContentAlignLeft();
private:
	void calcColResultTextWidth();
public:
	long m_iCol;
	bool m_bNeedAlignLeft;
	double m_dOriginWidth;
	double m_dResultWidth;
	double m_dMaxWidth;
	bool m_bHidden;
};

typedef std::map<int, AtomicCol*> AtomicColMap;

struct MergeCellsList : public std::vector<AtomicCells*>
{
	AtomicCells* FindCellInMergeCells(long iRow, long iCol);
};

struct ImgFmlaCellsList : public std::vector<AtomicCells*>
{
	AtomicCells* FindCellInImgFmlaCells(AtomicCells* pAtomicRange);
};

struct AtomicTable : public AtomicRange, public QObject
{
public:
	AtomicTable(const AtomicRange& rg, IKWorksheet* pWorkSheet)
		:AtomicRange(rg),
		m_pWorkSheet(pWorkSheet)
	{}
	~AtomicTable();
	HRESULT BuildTable(etoldapi::Range* pRange, const kaietrecognize::TableRangeInfo& tblInfo, const QSharedPointer<TableApplyParam>& pTableApplyParam, IStopProcess* stopProcess);
	AtomicCells* NewCells(etoldapi::Range* pCellRange);
	AtomicCells* getMergeCell(long iRow, long iCol);
	int getFirstMergeColIdx(kaietrecognize::TableRangeInfo& targetTable);
	int getFirstHeaderRowIdx(kaietrecognize::TableRangeInfo& targetTable);

	//Todo:从process类搬来的代码，后续考虑写一个子类把这些新增方法放进去，而不全部放基类这里
	void checkAndExpandMergeCellHeight(AtomicCells* pEachAtomicCells, bool bEnableAlignLeft);
	// 获取单元格的预设宽度
	double getCellWidth(AtomicRange * pEachAtomicRange);
	double getMergeCellHeight(AtomicRange* pEachAtomicRange);
	void expandMergeCellHeight(AtomicRange* pEachAtomicRange, double dMoreRowHeight);
	double getPresetColWidth(int iCol);
	double getPresetRowHeight(int iRow);
	// 收集所有行的行高
	void presetRowHeight(int iRow, double dHeight);

	//拿到当前表格的高度和宽度
	void getTableSizeAndAve(double &w,double &h,double &wAve,double &hAve);
	

public:
	MergeCellsList m_mergeCellsList;
	ImgFmlaCellsList m_imgFmlaCellsList;
	AtomicRowMap m_rowList;
	AtomicColMap m_colList;

protected:
	std::vector<AtomicRange*> m_vecTableCells;	// 用于管理内存
	IKWorksheet* m_pWorkSheet;
	kaietrecognize::TableRangeInfo m_tblInfo;
	QSharedPointer<TableApplyParam> m_pTableApplyParam;
};

class TblRowProcessResult
{
public:
	TblRowProcessResult(int iRow);
	virtual ~TblRowProcessResult();
	void setRowHeight(double h)
	{
		m_dHeight = h;
	}
	double getRowHeight()
	{
		return m_dHeight;
	}
private:
	double m_dHeight;
	int m_iRow;
};

class TblCellProcessResult
{
public:
	TblCellProcessResult(int iRow, int iCol);
	//标记不调整列宽，允许排版溢出该单元格
	void setTextOverflow(bool bAllowTextOverflow)
	{
		m_bAllowTextOverflow = bAllowTextOverflow;
	}
	bool getTextOverflow()
	{
		return m_bAllowTextOverflow;
	}
	int getRowIdx() const
	{
		return m_iRow;
	}
	int getColIdx() const
	{
		return  m_iCol;
	}
private:
	int m_iRow;
	int m_iCol;
	bool m_bAllowTextOverflow;
};

//表示调整后的结果
//Todo:1后续这个类得考虑根据索引查找到某一个单元格调整后的结果；2.把调整后单元格其他信息给补充上
class TblProcessResult
{
public:
	TblProcessResult();
	void insertCellProResult(TblCellProcessResult* pCellProResult);
	TblCellProcessResult* findCellProResult(int iRow, int iCol);
private:
	QVector<TblCellProcessResult*> m_CellProResult;
};

//适应屏幕

struct ViewSize 
{
public:
	ViewSize():m_ViewWidth(0),m_ViewHeight(0)
	{}
	void setW(int w)
	{
		m_ViewWidth = w;
	}
	void setH(int h)
	{
		m_ViewHeight = h;
	}
	int m_ViewWidth;
	int m_ViewHeight;
};

class AdaptScreenInfo
{
public:
	AdaptScreenInfo();
	virtual ~AdaptScreenInfo();
	void setViewSize(double w, double h);
	void setTableW(double w);
	void setTableH(double h);
	void setTableWidthAve(double ave);
	void setTableHeightAve(double ave);

	double getTableW()
	{
		return m_wTableTotal;
	}
	double getTableH()
	{
		return m_hTableTotal;
	}
	double getViewW()
	{
		return m_wViewWithChar;
	}
	double getViewH()
	{
		return m_hViewWithPound;
	}
	double getWidthAve()
	{
		return m_wTableAve;
	}
	double getHeightAve()
	{
		return m_hTableAve;
	}

	AdaptScreenType getAdaptScreenType();

private:
	AdaptScreenType m_Type;//类型
	//视图大小
	double m_wViewWithChar;//单位：列宽单位，字符数
	double m_hViewWithPound;//单位：行高单位，磅
	//表的大小
	double m_wTableTotal; //单位：列宽单位，字符数
	double m_hTableTotal;//单位：行高单位，磅
	//表的平均列宽行高
	double m_wTableAve;
	double m_hTableAve;
};

//使用策略模式
class adaptScreenStrategy
{
public:
	adaptScreenStrategy(AtomicTable* pTable, AdaptScreenInfo* pAdaptInfo);
	virtual ~adaptScreenStrategy();
	virtual void doAdaptScreen() = 0;
	void printfDebugInfo();

protected:
	double adaptColWidth(double wTable, double W);
	double adaptRowHeight(double hTable, double H);

	void updateTableTotalWidth();
	void updateTableTotalHeight();

protected:
	AtomicTable* m_pAtomicTable;
	AdaptScreenInfo* m_pAdaptInfo;
};

class adaptScreenNormalStrategy :public adaptScreenStrategy
{
public:
	adaptScreenNormalStrategy(AtomicTable* pTable, AdaptScreenInfo* pAdaptInfo);
	virtual ~adaptScreenNormalStrategy();
	virtual void doAdaptScreen() override;
};

class adaptScreenColFirstStrategy :public adaptScreenStrategy
{
public:
	adaptScreenColFirstStrategy(AtomicTable* pTable, AdaptScreenInfo* pAdaptInfo);
	virtual ~adaptScreenColFirstStrategy();
	virtual void doAdaptScreen() override;

private:
	//折行处理
	void adaptCellsInCol(AtomicCol* pAtomicCol, double newWidth);
	//行高优化
	void adaptRowHeightToViewH();
};

class adaptScreenRowFirstStrategy :public adaptScreenStrategy
{
public:
	adaptScreenRowFirstStrategy(AtomicTable* pTable, AdaptScreenInfo* pAdaptInfo);
	virtual ~adaptScreenRowFirstStrategy();
	virtual void doAdaptScreen() override;
};

//运用“代理模式”，外层直接通过这个代理类去调用适应屏幕调整，而不用去区分要调用哪一种“策略”
class adaptScreenPrcessProxy
{
public:
	adaptScreenPrcessProxy(AtomicTable* pTable, const ViewSize &viewSz);
	~adaptScreenPrcessProxy();
	void doAdaptScreen();
	void init(AtomicTable* pTable, const ViewSize& viewSz);
private:
	et_sptr<adaptScreenStrategy> m_spStrategy;
	AdaptScreenInfo* m_pAdaptInfo;
};
