set(system_style
	wpp_system_style/system_0.tablestyle
	wpp_system_style/system_1.tablestyle
	wpp_system_style/system_2.tablestyle
	)

wps_package(konlinewpptablestyle SHARED BUILD_DEST_PATH office6/addons/konlinewpptablestyle)
	wps_add_definitions(
		WPP_PROJECT
	)

	wps_include_directories(
		.
		..
		../src
		../..
		../../..
		../../../../
		../../../../../
		Coding/core_bundle/include
		Coding/core_bundle/framework
		Coding/core_bundle/framework/ksolite
		Coding/core_bundle/office/include
		Coding/core_bundle/office/wpp/include
		../../../include
		Coding/shell_bundle/shell2/include
		Coding/plugin_bundle/shell2/plugins/docer
		Coding/plugin_bundle/shell2/plugins/docer/include
		Coding/core_bundle/office/et
		Coding/core_bundle/office/et/include
		Coding/misc_bundle/3rdparty
		Coding/io_bundle/io
		Coding/io_bundle/io/ooxml
		Coding/io_bundle/io/include
		Coding/io_bundle/io/share
		Coding/core_bundle/office/wpp/objtable
		Coding/core_bundle
		Coding/shell_bundle
		Coding/shell_bundle/shell2
	)

	wps_use_packages(
		curl
		jsoncpp
		Qt5Widgets
		Qt5Xml
		Qt5Svg
		Qt5Network
		LINUX(libsafec)
		WIN(zip-utils)
		cryptopp
	)
	wps_add_sources(
		PCH stdafx.h stdafx.cpp
		QT_AUTOMOC

		../src/ktablebeautify.cpp
		../src/ktablebeautify.h
		../src/kxonlinereshelper.cpp
		../src/kxonlinereshelper.h
		../src/kxonlinetablestylejsapi.cpp
		../src/kxonlinetablestylejsapi.h
		../src/kxonlinetablestyledefine.h
		../src/kxonlinetablestyledefine.cpp
		../src/kxqhtablestylecommand.cpp
		../src/kxqhtablestylecommand.h

		../src/kxtablestylegallerycommand.cpp
		../src/kxtablestylegallerycommand.h
		../src/kxtponlinetablestylewidget.cpp
		../src/kxtponlinetablestylewidget.h
		../src/kxtptablestylecommand.cpp
		../src/kxtptablestylecommand.h
		../src/kxtpwpptablestylecommand.h
		../src/kxtpwpptablestylecommand.cpp
		../src/kxwpsonlinereshelper.cpp
		../src/kxwpponlinereshelper.cpp
		../src/kxtablestylefeaturecommand.cpp
		../src/kxtablestylefeaturecommand.h

		../src/kxwpptablestylehelper.h
		../src/kxwpptablestylehelper.cpp
		../src/kxonlinewpptablestylejsapi.cpp
		../src/kxwpptablestylerecommendhelper.h
		../src/kxwpptablestylerecommendhelper.cpp
		../src/main.cpp

		../src/ktablestyleinfocollect.cpp
		../src/ktablestyleinfocollect.h

		../src/custom/kxdocertablestylewidget.cpp
		../src/custom/kxdocertablestylewidget.h
		../src/custom/kxdocertablestylegalleryitem.cpp
		../src/custom/kxdocertablestylegalleryitem.h

		../../common/toolkit/algorithm/kxdocershapenotify.cpp
		../../common/toolkit/algorithm/kxwppdocershapenotify.cpp
		../../common/toolkit/algorithm/kxwpsdocershapenotify.cpp
		../../common/toolkit/algorithm/kxetdocershapenotify.cpp
		../../common/toolkit/algorithm/kxdocershapenotify.h
		../../common/toolkit/algorithm/kxetdocerdataevent.cpp
		../../common/toolkit/algorithm/kxetdocerdataevent.h
		../../common/base/algorithm/kdocerdschartrule.cpp
		../../common/base/algorithm/kdocerdschartrule.h

		../src/tablestyleparser/kxcommontablestyleparser.cpp
		../src/tablestyleparser/kxcommontablestyleparser.h
		../src/tablestyleparser/kxtablestyleparserbase.cpp
		../src/tablestyleparser/kxtablestyleparserbase.h

		../src/resmgr/konlinetableresmgr.cpp
		../src/resmgr/konlinetableresmgr.h

		../src/custom/kxdocertablestylegalleryitem.cpp
		../src/custom/kxdocertablestylegalleryitem.h

		../src/inserttable/kinsertonlinetablewidget.cpp
		../src/inserttable/kinsertonlinetablewidget.h
		../src/inserttable/kinsertonlinetablecmd.cpp
		../src/inserttable/kinsertonlinetablecmd.h
		../src/inserttable/kinsertonlinetablemodel.cpp
		../src/inserttable/kinsertonlinetablemodel.h
	)

	wps_add_resources(
		cfg.ini
		LOCATION ${CMAKE_SOURCE_DIR}/plugin_bundle/shell2/plugins/kdocer/konlinetablestyle
		BUILD_DEST_PATH office6/addons/konlinewpptablestyle/
	)

	wps_link_packages(
		kso
		kshell
		krt
		ksolite
		algdiag
		kdownload
		ksqlite3
		wppcore
		wppmain
		kdocerrequest
		
		kdocerresourcelib
		
		kdocertoolkit
		kdocerjsapi
		docermodulelib
		kuserinfo
		
		kdocerwidget
	)

	wps_custom_compile(lrelease
		INPUT ../mui/zh_CN/ts/konlinetablestyle.ts
		OUTPUT office6/addons/konlinewpptablestyle/mui/zh_CN/konlinetablestyle.qm
	)

	wps_custom_compile(qrc
		INPUT ../mui/default/res/icons.qrc
		OUTPUT office6/addons/konlinewpptablestyle/mui/default/icons.data
	)

	wps_add_resources(
		${system_style}
		BUILD_DEST_PATH office6/addons/konlinewpptablestyle/
	)
	# konlinewpptablestyle前端资源
	wps_add_frontend_resource(
		kwebdocer_table
		BUILD_DEST_PATH office6/addons/konlinewpptablestyle/res/
	)
	wps_add_frontend_resource(
		version
		LOCATION ${WPS_FRONTEND_DIRECTORY}/kwebdocer_table
		BUILD_DEST_PATH office6/addons/konlinewpptablestyle/res/	
	)
wps_end_package()
