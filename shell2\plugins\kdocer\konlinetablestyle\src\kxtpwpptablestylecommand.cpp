﻿#include "stdafx.h"
#include "kcomctl/kcommandfactory.h"
#include <kso/l10n/wpp/wppuil.h>
#include <wpp/wppdata/wppdata_i.h>
#include <kxshare/kxsubwindow.h>
#include <kxshare/kxtaskpane.h>
#include <kxshare/formatting_helper.h>
#include <security/utils.h>
#include "kxtptablestylecommand.h"
#include "kxwpptablestylehelper.h"
#include "ktablebeautify.h"
#include "kxtpwpptablestylecommand.h"
#include <kdocertoolkit/kdocerutils.h>
#include "kdocercorehelper.h"
#include "kxonlinereshelper.h"

KxTpWppOnlineTableStyleCommand::KxTpWppOnlineTableStyleCommand(KxMainWindow* host, QObject* parent)
	: KxDocerTpTableStyleCommand(host, parent)
{

}

PanelEnableStatus KxTpWppOnlineTableStyleCommand::judgeEnableInsertTable()
{
	ks_stdptr<IKApplication> spCoreApp = kxApp->coreApplication();
	ks_stdptr<IKDocument> spDoc = spCoreApp->GetActiveDocument();
	if (!spDoc)
		return PanelEnableStatus::OtherErrorStatus;

	if (!spDoc || IsSecurityDocReadOnly(spDoc) || KDocerUtils::isReadOnlyMode(false))
		return  PanelEnableStatus::SecureDocReadOnly;
	if (IKView* pView = spDoc->GetActiveView())
	{
		ks_stdptr<IKPaneInfo> spPaneInfo = pView;
		if (spPaneInfo && spPaneInfo->GetPaneId() == PANEID_NORMAL_THUMBNAILS)
		{
			if (ICaret* pCaret = spPaneInfo->GetFakeCaret())
			{
				BOOL b = FALSE;
				pCaret->Get_Visible(&b);
				if (b)
					return PanelEnableStatus::OtherErrorStatus;
			}
		}

		//浏览视图禁止快捷键重做
		IKF4CopyManager* pCopyManager = kxApp->coreApplication()->GetF4CopyManager();
		if (pCopyManager && pCopyManager->IsExecuting() && spPaneInfo && spPaneInfo->GetPaneId() == PANEID_SLIDESORTER_SLIDESORTER)
			return PanelEnableStatus::OtherErrorStatus;
	}
	return PanelEnableStatus::Enable;
}

OperatorErrorCode KxTpWppOnlineTableStyleCommand::applyTableStyleDocument(const KOTSResourceInfo& resourceInfo,
	const QByteArray& content, bool hover)
{
	if (!KxOnlineTableResHelper::wppHasActiveTable() && !resourceInfo.bInsertTable)
		return InsertOpNoActiveTable;
	if (!hover)
		onLeaveHoverPreview();
	if (!m_previewTr->isInPreview())
		m_previewTr->beginPreview(resourceInfo.bInsertTable ? WpINSERTTABLE : WpTABLESTYLE);

	bool bSuccess = true;
	if (resourceInfo.bInsertTable)
		bSuccess = insertTableByCommand();
	
	if (bSuccess)
	{
		bSuccess = KxWppTableStyleHelper::instance()->applyStyle(resourceInfo, content);
		if(bSuccess)
		{
			if (auto ikDocerCore = getIKDocerCore())
			{
				if (auto record = ikDocerCore->getDocerResourceKeyRecord())
				{
					record->setDocResourceRecord(KDocerUtils::getActiveDocument(), DocerResourceKey::TableStyle, resourceInfo.id);
				}
			}
			KTableBeautify::instance()->setEmphasizeColor(resourceInfo.color);
		}
	}

	if(!hover)
		m_previewTr->endPreview(bSuccess);
	if (!bSuccess)
		return InsertOpError;

	qApp->postEvent(m_host, new KIdleEvent(0, 0), Qt::LowEventPriority);
	return Success;
}

OperatorErrorCode KxTpWppOnlineTableStyleCommand::applySystemTableStyleDocument(int resourceIndex, bool hover)
{
	if (!KxOnlineTableResHelper::wppHasActiveTable())
		return InsertOpNoActiveTable;
	if (KxWppTableStyleHelper::instance()->applyStyleBySystemIndex(resourceIndex))
	{
		qApp->postEvent(m_host, new KIdleEvent(0, 0), Qt::LowEventPriority);
		return Success;
	}
	else
		return InsertOpError;
}