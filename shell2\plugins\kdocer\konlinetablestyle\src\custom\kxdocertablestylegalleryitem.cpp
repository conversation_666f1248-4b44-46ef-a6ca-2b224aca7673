﻿#include "stdafx.h"
#include "kxdocertablestylegalleryitem.h"
#include "kxonlinereshelper.h"
#include "ktablestyleinfocollect.h"
#include "kxtablestylefeaturecommand.h"
#include <kcomctl/tik.h>
#include <QtSvg/QSvgRenderer>
#include <public_header/kliteui/koperatorlib.h>
#include <public_header/drawing/view/theme_agent.h>
#include "resmgr/konlinetableresmgr.h"
#include <kxshare/formatting_helper.h>
#include "kdocertoolkit/kdocerutils.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "kdocerfunctional.h"
#include "kxshare/kxquickhelpbarcontainer.h"
#include "common/request/helper/kdocerdownload.h"
#include "common/request/network/knetworkrequest.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include "kcomctl/src/formatting/kcolorcombobox.h"
#include <kcomctl/kgalleryviewitem.h>
using namespace kdocerresnetwork;
#define LOGIN_SRC "docer_et_infeature"
#define RETRY_ICON_SIZE 16
#define RETRY_BUTTON_SIZE 28
#define ICON_PADDING 6
#define VIP_RECT_RADIUS 4
#define VIP_RECT_HEIGHT 24

namespace {
	KDocerButtonStyle getButtonStyle(const QStringList& privileges = QStringList())
	{
		return KOnlineTableResManager::getInstance().getButtonStyle(privileges);
	}
	
	void fillRect(QPainter& dc, const QRect& rc, const QBrush& color, int radius, int round)
	{
		dc.save();
		dc.setPen(Qt::NoPen);
		dc.setBrush(color);
		dc.setRenderHint(QPainter::Antialiasing, true);
		dc.drawPath(KDrawHelpFunc::getBoundPath(rc, radius, round));
		dc.restore();
	}

	void drawIconText(QPainter& dc, const QRect& rc, int padding, int spacing, const QIcon& icon,
		const QSize& iconSize, const QString& text, const QColor& textColor, const QFont& font)
	{
		QRect iconRect(rc.left() + padding, rc.top(), iconSize.width(), rc.height());
		dc.drawPixmap(kuiopt::calcCenterRect(iconRect, iconSize), icon.pixmap(iconSize));

		int textWidth = QFontMetrics(font).width(text);
		int iconWidth = icon.isNull() ? 0 : iconSize.width() + spacing;
		QRect textRect(rc.left() + padding + iconWidth, rc.top(), textWidth, rc.height());
		KDrawHelpFunc::drawColorText(dc, textColor, textRect, Qt::AlignLeft | Qt::AlignVCenter, text, font);
	}

	QRect getCornerRect(const QRect& rc, int radius, int height, const QIcon& icon, const QString& text, Qt::Corner corner, bool bBold,
							int& spacing, int& padding, QSize& iconSize, QFont& font)
	{
		font = QApplication::font();
		font.setWeight(bBold ? QFont::Black : QFont::Normal);
		font.setPixelSize(KLiteStyle::dpiScaled(12));
		iconSize = KLiteStyle::dpiScaledSize(16, 16);
		spacing = KLiteStyle::dpiScaled(2);
		padding = KLiteStyle::dpiScaled(6);
		const int margin = KLiteStyle::dpiScaled(4);
		const int textWidth = QFontMetrics(font).width(text);
		const int iconWidth = !icon.isNull() ? iconSize.width() + spacing : 0;

		int top = 0, left = 0;
		int width = padding + iconWidth + textWidth + padding;
		switch (corner)
		{
		case Qt::TopLeftCorner:
			top = rc.top() + margin;
			left = rc.left() + margin;
			break;
		case Qt::TopRightCorner:
			top = rc.top() + margin;
			left = rc.right() - margin - width;
			break;
		case Qt::BottomLeftCorner:
			top = rc.bottom() - margin - height;
			left = rc.left() + margin;
			break;
		case Qt::BottomRightCorner:
			top = rc.bottom() - margin - height;
			left = rc.right() - margin - width;
			break;
		default: assert(0);
		};

		return QRect(left, top, width, height);
	}

	void drawCornerIconText(QPainter& dc, const QRect& rc, int radius, int height, const QIcon& icon, const QString& text,
		const QColor& textColor, const QColor& backgroundColor, Qt::Corner corner = Qt::BottomLeftCorner, bool isBold = false)
	{
		if (text.isEmpty() || !textColor.isValid() || !backgroundColor.isValid())
			return;

		QFont font;
		QSize iconSize;
		int spacing;
		int padding;
		QRect rect = getCornerRect(rc, radius, height, icon, text, corner, isBold, spacing, padding, iconSize, font);
		fillRect(dc, rect, backgroundColor, radius, ROUND_ALL);
		drawIconText(dc, rect, padding, spacing, icon, iconSize, text, textColor, font);
	}
};

KxDocerTableStyleGalleryView::KxDocerTableStyleGalleryView(KGalleryAbstractModel* model, QWidget* parent, KGalleryView::ViewType viewType /*= KGalleryView::COMPLETE*/)
	: KGalleryView(model, viewType, parent)
{
	connect(model, &KGalleryAbstractModel::indexHovered, this, &KxDocerTableStyleGalleryView::onIndexHovered);

	m_leaveHoverTimer.setSingleShot(true);
	m_leaveHoverTimer.setInterval(50);
	connect(&m_leaveHoverTimer, &QTimer::timeout, this, &KxDocerTableStyleGalleryView::onLeaveHoverTimerOut);
}

void KxDocerTableStyleGalleryView::onIndexHovered(int idx)
{
	m_leaveHoverTimer.stop();
	if (idx == -1)
		m_leaveHoverTimer.start();

	setCursor(idx == -1 ? Qt::ArrowCursor : Qt::PointingHandCursor);
	KxDocerTableStyleGalleryItem* item = qobject_cast<KxDocerTableStyleGalleryItem*>(m_model->element(idx));
	if (item)
		item->hover();
}

void KxDocerTableStyleGalleryView::leaveEvent(QEvent* e)
{
	KGalleryView::leaveEvent(e);
	if (m_leaveHoverTimer.isActive())
	{
		m_leaveHoverTimer.stop();
		onLeaveHoverTimerOut();
	}
}

void KxDocerTableStyleGalleryView::mousePressEvent(QMouseEvent* e)
{
	KGalleryView::mousePressEvent(e);
	QPoint pos = e->pos();
	emit sigItemPress(hitTest(pos));
}

void KxDocerTableStyleGalleryView::mouseReleaseEvent(QMouseEvent* e)
{
	KGalleryView::mouseReleaseEvent(e);
	QPoint pos = e->pos();
	emit sigItemClick(hitTest(pos));
}

int KxDocerTableStyleGalleryView::hitTest(const QPoint& viewPos)
{
	QPoint pos = viewPos;
	if (m_justShowLine)
		pos.setX(viewPos.x() + m_viewPort);
	else
		pos.setY(viewPos.y() + m_viewPort);

	for (int i = 0; i < m_items.count(); ++i)
	{
		const KGalleryViewItem* const item = m_items.at(i);
		if (item->isVisible(m_viewType) && item->gemetry().contains(pos))
		{
			m_model->setModelMousePoint(pos);
			const_cast<KGalleryModelAbstractItem*>(item->model())->isMouseMoving();
			return i;
		}
	}
	return -1;
}

void KxDocerTableStyleGalleryView::onLeaveHoverTimerOut()
{
	emit leaveOnlineResource();
}
/////////////////////////////////////////////////////////////////////////
KxDocerTableStyleGalleryModel::KxDocerTableStyleGalleryModel(
	IKApplication* coreApp,
	QObject* parent,
	KCommand* cmd,
	int activeGalleryItemCnt,
	const QString& cmdName,
	bool bInsert)
	:KGalleryAbstractModel(coreApp, parent),
	m_cmd(cmd),
	m_activeGalleryItemCnt(activeGalleryItemCnt),
	m_cmdName(cmdName),
	m_bInsert(bInsert)
{
	KOnlineTableResManager::getInstance().initStyleConfig();
}

bool KxDocerTableStyleGalleryModel::prepareItems()
{
	if (m_bRequesting)
		return true;

	setState(State::Loading);
	m_bRequesting = true;

	if (KDocerUtils::isWpsApp(this) && m_headerIdx < 0)
	{
		addSpacing(KLiteStyle::dpiScaled(11));
		m_headerIdx = addHeader(tr("Other Styles"));
		KGalleryModelTitleItem* headerItem = qobject_cast<KGalleryModelTitleItem*>(element(m_headerIdx));
		if (headerItem)
		{
			headerItem->setDrawBackground(false);
			QFont font;
			font.setWeight(QFont::DemiBold);
			font.setPixelSize(14);
			headerItem->setFont(font);
			headerItem->setInlineMode(true);
			headerItem->setVSpace(KLiteStyle::dpiScaled(9));
			headerItem->setLeftPadding(KLiteStyle::dpiScaled(6));
			headerItem->setProperty("qtspyItemFlag", "OtherStyles");
		}
	}

	QString position = "PCETBGGJXLMB1001";
	if (KDocerUtils::isWpsApp(this))
		position = "PCWZBGYSXLMB1001";

	KxOnlineTableResHelper::requestPublishResource(position,
		bindContext(this, [=](const QList<KTableStyleResourceInfo>& list) {
			if (!KDocerUtils::isCoreAppMatch(this))
			{
				m_bRequesting = false;
				return;
			}

			initGalleryItem(list);
			m_bRequesting = false;
		}),
		bindContext(this, [=](const DownloadFailInfo& info) {
			if (!KDocerUtils::isCoreAppMatch(this))
			{
				m_bRequesting = false;
				return;
			}

			setState(info.errorCode == 4 ? InternetTimeout : NoInternet);
			m_bRequesting = false;
		}));
	return true;
}

void KxDocerTableStyleGalleryModel::setState(State state)
{
	if (m_state != state)
	{
		m_state = state;
		emit stateChanged(state);
	}
}

void KxDocerTableStyleGalleryModel::initGalleryItem(const QList<KTableStyleResourceInfo>& resourceList)
{
	int activeGalleryItemCnt = MIN(resourceList.count(), m_activeGalleryItemCnt);
	if (activeGalleryItemCnt == 0)
	{
		setState(State::NoResource);
		m_bRequesting = false;
		return;
	}

	QHash<QString, QString> args;
	args.insert("first_entry", "restab");
	args.insert("second_entry", KxOnlineTableResHelper::getSecondEntry(m_cmdName));
	KTableStyleInfoCollect::postResourcePageDisplayInfo(args, activeGalleryItemCnt);

	KCommand* tableStyleCmd = KDocerUtils::findCommand(m_cmdName);
	KProxyCommand* pProxyCmd = qobject_cast<KProxyCommand*>(tableStyleCmd);
	if (pProxyCmd)
		tableStyleCmd = pProxyCmd->targetCommand();
	drawing::Color color;
	if (tableStyleCmd)
	{
		KGalleryCommand* galleryCmd = qobject_cast<KGalleryCommand*>(tableStyleCmd);
		if (galleryCmd)
			color = galleryCmd->getInitThemeColorPaneColor();
	}
	for (auto it = resourceList.cbegin(); it != resourceList.cend(); it++)
	{
		int itemCount = count();
		if (m_headerIdx >= 0)
			itemCount -= m_headerIdx + 1;
		if (itemCount == activeGalleryItemCnt)
			break;

		KxDocerTableStyleGalleryItem* item = new KxDocerTableStyleGalleryItem(this, *it, color, m_cmd, m_cmdName, m_bInsert, ThumbType::Small);
		item->setProperty("qtspyItemFlag", QString("OnlineTablestyle_%1").arg(itemCount));
		addElement(item);
		item->reportDisplayInfo();

		connect(item, SIGNAL(insertOnlineResource(const KOTSResourceInfo&, const ReportInfo&, const QString&, bool))
			, m_cmd, SLOT(applyTableStyle(const KOTSResourceInfo&, const ReportInfo&, const QString&, bool)));
		connect(item, SIGNAL(sigLeavePreview()), m_cmd, SIGNAL(sigLeaverHover()));
		connect(parent(), SIGNAL(sigInsertFinished(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&))
			, item, SLOT(onInsertFinished(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)));

#ifdef Q_OS_MACOS
		connect(tableStyleCmd, SIGNAL(colorChange(const ksodrawing::Color&)), item, SLOT(changeIconColor(const ksodrawing::Color&)), Qt::UniqueConnection);
#else
		connect(tableStyleCmd, SIGNAL(colorChange(const drawing::Color&)), item, SLOT(changeIconColor(const drawing::Color&)), Qt::UniqueConnection);
#endif
		connect(item, &KxDocerTableStyleGalleryItem::sigDownloadError, this, &KxDocerTableStyleGalleryModel::sigDownloadError);
	}
	setState(State::GalleryItem);
}

/////////////////////////////////////////////////////////////////////////
KxDocerTableStyleGalleryItem::KxDocerTableStyleGalleryItem(
	KGalleryAbstractModel* model,
	KTableStyleResourceInfo resourceInfo, 
	const drawing::Color& color, 
	KCommand* tableStyleCmd, 
	const QString& cmdName, 
	bool bInsert,
	ThumbType thumbType)
	:KGalleryModelAbstractItem(model),
	m_model(model),
	m_cmdName(cmdName),
	m_resourceInfo(resourceInfo),
	m_currentColor(color),
	m_featureCmd(tableStyleCmd),
	m_bInsert(bInsert),
	m_thumbType(thumbType)
{
	setHSpace(KLiteStyle::dpiScaled(6));
	setVSpace(KLiteStyle::dpiScaled(2));

	m_resourceInfo.downloadKey = KDocerUtils::generatePayKey();
	QString thumbPath = KxOnlineTableResHelper::getThumbCachePath(m_resourceInfo.previewUrlMap[m_thumbType], m_resourceInfo.id);
	if (!QFile::exists(thumbPath))
		downloadResource();
	else
		changeIconColor(m_currentColor);
}

KxDocerTableStyleGalleryItem::~KxDocerTableStyleGalleryItem()
{
}

void KxDocerTableStyleGalleryItem::setLoadState(LoadState state)
{
	m_loadState = state;
}

QSize KxDocerTableStyleGalleryItem::sizeHint() const
{
	return KLiteStyle::dpiScaledSize(96, 60);
}

bool KxDocerTableStyleGalleryItem::isTriggerable() const
{
	if(m_loadState == IconLoading || m_loadState == IconReloading)
		return false;

	return true;
}

bool KxDocerTableStyleGalleryItem::isExpanding(KGalleryView::ViewType type) const
{
	return false;
}

void KxDocerTableStyleGalleryItem::timerEvent(QTimerEvent* e)
{
	static const int StepAngle = 20;
	m_loadingRotation = (m_loadingRotation - StepAngle) % 360;
	emit statusChanged();
}

bool KxDocerTableStyleGalleryItem::hitTestSubIcon(const QPoint& itemPos, const QPoint& mousePos)
{
	QRect itemRect = QRect(itemPos, sizeHint());
	if (m_loadState == IconLoadError)
	{
		QRect retryRect = kuiopt::calcCenterRect(itemRect, KLiteStyle::dpiScaledSize(RETRY_BUTTON_SIZE, RETRY_BUTTON_SIZE));
		if (retryRect.contains(mousePos))
		{
			m_bRetryHover = true;
			m_bVipIconHover = false;
			emit statusChanged();
			return true;
		}
	}
	else if (isHovered() && !m_icon.isNull() && m_loadState != ResourceLoading)
	{
		int radius = KLiteStyle::dpiScaled(VIP_RECT_RADIUS);
		int height = KLiteStyle::dpiScaled(VIP_RECT_HEIGHT);

		auto style = getButtonStyle(m_resourceInfo.bFree ? QStringList() : m_resourceInfo.privileges);
		int spacing;
		int padding;
		QRect drawRect = getDrawRect(itemRect);
		QSize size = QSize();
		QFont font = QFont();
		QRect buttonRect = getCornerRect(drawRect, radius, height, style.icon, style.text, Qt::BottomLeftCorner, false, spacing, padding, size, font);
		if (buttonRect.contains(mousePos))
		{
			m_bRetryHover = false;
			m_bVipIconHover = true;
			emit statusChanged();
			return true;
		}
	}
	m_bVipIconHover = false;
	m_bRetryHover = false;
	emit statusChanged();
	return false;
}

void KxDocerTableStyleGalleryItem::drawItem(QPainter& dc, const QRect& rc, KGalleryView::ViewType type) const
{
	drawItem(dc, getDrawRect(rc));
}

void KxDocerTableStyleGalleryItem::drawItem(QPainter& dc, const QRect& rc) const
{
	drawPreview(dc, rc);

	if (m_loadState == IconLoadError)
	{
		drawRetry(dc, rc);
	}
	else if ((isChecked() && m_loadState == ResourceLoading) || m_loadState == IconReloading)
	{
		drawLoading(dc, rc);
	}
	else if (isHovered() && !m_icon.isNull())
	{
		drawHoverBackground(dc, rc);
		drawVipIcon(dc, rc);
	}	
}

void KxDocerTableStyleGalleryItem::drawPreview(QPainter& dc, const QRect& rc) const
{
	dc.save();
	dc.setRenderHints(QPainter::Antialiasing | QPainter::SmoothPixmapTransform, true);

	if (!m_icon.isNull())
	{
		QPixmap pixmap = m_icon.pixmap(rc.size() * krt::devicePixelRatio());
		dc.drawPixmap(kuiopt::calcCenterRect(rc, pixmap.size() / krt::devicePixelRatio()), pixmap);
	}
	else
	{
#ifdef  Q_OS_MACOS
		fillRect(dc, rc, KDocerUtils::getCommonColor("kd-color-white"), 0, ROUND_ALL);
#else
		fillRect(dc, rc, KDocerUtils::getCommonThemeColor("kd-color-background-plate"), 0, ROUND_ALL);
#endif //  Q_OS_MACOS
	}
	dc.restore();
}

void KxDocerTableStyleGalleryItem::drawHoverBackground(QPainter& dc, const QRect& rc) const
{
	QColor color = KDocerUtils::getCommonThemeColor("kd-color-state-hover");

	dc.save();
	dc.setPen(Qt::NoPen);
	dc.setBrush(color);
	dc.setRenderHint(QPainter::Antialiasing);

	QPainterPath path;
	QRect backgroundRect(
		rc.left() - KLiteStyle::dpiScaled(6),
		rc.top() - KLiteStyle::dpiScaled(6),
		rc.width() + KLiteStyle::dpiScaled(12),
		rc.height() + KLiteStyle::dpiScaled(12));
	path.addRoundedRect(backgroundRect, KLiteStyle::dpiScaled(4), KLiteStyle::dpiScaled(4));
	dc.drawPath(path);
	dc.restore();
}

void KxDocerTableStyleGalleryItem::drawLoading(QPainter& dc, const QRect& rc) const
{
	int width = KLiteStyle::dpiScaled(3);
	QSize size = KLiteStyle::dpiScaledSize(20, 20);
	QRect rect = kuiopt::calcCenterRect(rc, size);

	int startAngle = m_loadingRotation + 90;
	QConicalGradient gradient(rect.center(), startAngle);
	gradient.setColorAt(0, QColor(10, 108, 255, 255));
	gradient.setColorAt(0.5, QColor(10, 108, 255, 122));
	gradient.setColorAt(1, QColor(10, 108, 255, 0));

	dc.setRenderHint(QPainter::Antialiasing);

	if(m_loadState == ResourceLoading)
	{
		QPainterPath path;
		path.addEllipse(rect.center(), size.width() / 2, size.height() / 2);
		dc.fillPath(path, QBrush(QColor(0xff, 0xff, 0xff, 0xcc)));
	}

	dc.setPen(QPen(QBrush(gradient), width, Qt::SolidLine, Qt::RoundCap));
	dc.drawArc(rect, (startAngle + 30) * 16, 330 * 16);

	dc.setPen(QPen(QColor(10, 108, 255), width, Qt::SolidLine, Qt::RoundCap));
	dc.drawArc(rect, startAngle * 16, 30 * 16);
}

void KxDocerTableStyleGalleryItem::drawRetry(QPainter& dc, const QRect& rc) const
{
	dc.save();

	if (m_bRetryHover)
	{
		QRect retryBgRect = kuiopt::calcCenterRect(rc, KLiteStyle::dpiScaledSize(RETRY_BUTTON_SIZE, RETRY_BUTTON_SIZE));
		fillRect(dc, retryBgRect, KDocerUtils::getCommonThemeColor("kd-color-state-hover"), KLiteStyle::dpiScaled(6), ROUND_ALL);
	}
	QRect retryRect = kuiopt::calcCenterRect(rc, KLiteStyle::dpiScaledSize(RETRY_ICON_SIZE, RETRY_ICON_SIZE));
	QIcon retryIcon = KDocerUtils::isDarkSkin() ? QIcon(QLatin1String(":/icons/custom_retry_dark.svg")) : QIcon(QLatin1String(":/icons/custom_retry.svg"));
	dc.drawPixmap(retryRect, retryIcon.pixmap(retryRect.size() * krt::devicePixelRatio()));
	dc.restore();
}

void KxDocerTableStyleGalleryItem::drawVipIcon(QPainter& dc, const QRect& rc) const
{
	if (!isHovered())
		return;

	int radius = KLiteStyle::dpiScaled(VIP_RECT_RADIUS);
	int height = KLiteStyle::dpiScaled(VIP_RECT_HEIGHT);

	auto style = getButtonStyle(m_resourceInfo.bFree ? QStringList() : m_resourceInfo.privileges);
	QIcon icon = style.icon;
	QString text = style.text;
	QColor textColor = style.textColor;
	QColor backgroundColor = style.backgroundColor;
	if (m_bVipIconHover)
	{
		textColor = style.textColorHover;
		backgroundColor = style.backgroundColorHover;
	}

	drawCornerIconText(dc, rc, radius, height, icon, text, textColor, backgroundColor);
}

void KxDocerTableStyleGalleryItem::hover()
{
	if (m_loadState == IconLoadError || m_bInsert)
		return;

	QHash<QString, QString> args;
	getCustomResourceReportInfo(args);
	args.insert("sub_table", getHasMultiChildTable() ? "true" : "false");
	KTableStyleInfoCollect::postResourceHoveredInfo(args);
	KOnlineTableResManager::getInstance().downloadResFile(getTableDownloadInfo(), false,
		bindContext(this, [=](DownloadResult result, const QString& savePath) {
			if (!KDocerUtils::isCoreAppMatch(this))
			{
				emit sigDownloadError(getResourceInfo(false));
				return;
			}

			if (result != DownloadResult::Success)
			{
				emit sigDownloadError(getResourceInfo(false));
				return;
			}

			if (!m_bHovered)
				return;

			emit insertOnlineResource(getResourceInfo(true), getReportInfo(false), savePath, true);
		}));
}

void KxDocerTableStyleGalleryItem::setLoading(bool bLoading)
{
	if (m_loadingTimerId != -1)
		killTimer(m_loadingTimerId);
	m_loadingTimerId = bLoading ? startTimer(40) : -1;
	emit statusChanged();
}

void KxDocerTableStyleGalleryItem::reportDisplayInfo()
{
	QHash<QString, QString> args;
	getCustomResourceReportInfo(args);
	KTableStyleInfoCollect::postResourceDisplayInfo(args);
}

ReportInfo KxDocerTableStyleGalleryItem::getReportInfo(bool bClicked)
{
	ReportInfo reportInfo;
	reportInfo.click = bClicked;
	reportInfo.tablestyleId = m_resourceInfo.id;
	reportInfo.from = "";
	reportInfo.belongPage = "";
	reportInfo.userStatusId = "";
	reportInfo.sourceStatusId = QString::number(3);
	reportInfo.downloadKey = m_resourceInfo.downloadKey;
	return reportInfo;
}

KOTSResourceInfo KxDocerTableStyleGalleryItem::getResourceInfo(bool bHover)
{
	KOTSResourceInfo resourceInfo;
	resourceInfo.id = m_resourceInfo.id;
	resourceInfo.name = m_resourceInfo.name;
	resourceInfo.area = "editrec";
	resourceInfo.bHover = bHover;
	resourceInfo.bApplyAll = false;
	resourceInfo.bInsertTable = m_bInsert;

	drawing::Color::Type type = m_currentColor.type();
	if (type != drawing::Color::Scheme)
	{
		QString colorHex = KxOnlineTableResHelper::qColorToHex(m_currentColor.toRgb());
		resourceInfo.customColor = colorHex;
		resourceInfo.name = resourceInfo.name % QLatin1String("_") % colorHex;
	}
	else
	{
		resourceInfo.themeColorKey = m_currentColor.getScheme() - drawing::Color::SchemeColor::Accent1 + 1;
		resourceInfo.name = m_resourceInfo.name % "_" % QString::number(resourceInfo.themeColorKey);
	}
	return resourceInfo;
}

TableDownloadReportInfo KxDocerTableStyleGalleryItem::getTableDownloadInfo()
{
	TableDownloadReportInfo downloadInfo;
	downloadInfo.id = m_resourceInfo.id;
	downloadInfo.downloadKey = m_resourceInfo.downloadKey;
	downloadInfo.md5 = m_resourceInfo.md5;
	downloadInfo.channel = "newest_0_resourcebtn";
	downloadInfo.subChannel = KOnlineTableResManager::getInstance().getPayCsourcePrefix() % "tbtype";
	downloadInfo.clientType = "table_style";
	downloadInfo.component = m_cmdName.endsWith("HomeTab") ? "et_menu_start_tabletools" : "et_menu_tabletools-otherstyle";
	return downloadInfo;
}

void KxDocerTableStyleGalleryItem::getCustomResourceReportInfo(QHash<QString, QString>& args)
{
	args.insert("resource_id", m_resourceInfo.id);
	args.insert("resource_type", "tablestyle");
	args.insert("resource_name", m_resourceInfo.name);
	args.insert("resource_uuid", m_resourceInfo.uuid);
	args.insert("policy", "op");
	args.insert("recommend_id", KDocerUtils::generatePayKey());

	int index = 0;
	if (KDocerUtils::isWpsApp(this))
		index = m_model->indexOf(this) - 1;
	else
	{
		index = m_model->indexOf(this) + 1;
		if (index == 0)
			index = m_model->count() + 1;
	}
	args.insert("element_name", "resource");
	args.insert("element_position", QString::number(index));
	args.insert("element_type", "resource");
	args.insert("func", "docer_beautify");
	args.insert("klm", "docer_beautify.table_dropdown_page.resource_list.resource[resource](" % QString::number(index) % ")");
	args.insert("resource_type", "tablestyle");
	args.insert("module_name", "konlinetablestyle");
	args.insert("rid", m_resourceInfo.rid);
	args.insert("resource_key", m_resourceInfo.resourceKey);
	args.insert("page_name", "table_dropdown_page");
	args.insert("color_hex", QString());

	const drawing::IThemeAgent* pTheme = GetTheme(KMainWindow::getCoreApplication(this));
	if (pTheme != nullptr)
	{
		drawing::SchemeColorInterpreter interpreter = pTheme->getColorSchemeInterpreter();
		args["color_hex"] = KxOnlineTableResHelper::qColorToHex(m_currentColor.toRgb(&interpreter));
	}

	QString colorType = "dark1";
	drawing::Color::Type type = m_currentColor.type();
	if (type == drawing::Color::Scheme)
		colorType = "color" % QString::number(m_currentColor.getScheme() - drawing::Color::SchemeColor::Accent1 + 1);
	args.insert("color_type", colorType);

	args.insert("first_entry", "restab");
	args.insert("second_entry", KxOnlineTableResHelper::getSecondEntry(m_cmdName));
}

bool KxDocerTableStyleGalleryItem::getHasMultiChildTable()
{
	if (KxTableStyleFeatureCommand* cmd = qobject_cast<KxTableStyleFeatureCommand*>(m_featureCmd))
	{
		bool bAnalyzed = false;
		bool bHasMultiChildTable = false;
		cmd->getHasMultiChildTable(false, bAnalyzed, bHasMultiChildTable);
		return bAnalyzed && bHasMultiChildTable;
	}
	return false;
}

QRect KxDocerTableStyleGalleryItem::getDrawRect(const QRect& rct) const
{
	return rct.adjusted(
		KLiteStyle::dpiScaled(ICON_PADDING),
		KLiteStyle::dpiScaled(ICON_PADDING),
		KLiteStyle::dpiScaled((ICON_PADDING - 1) * -1),
		KLiteStyle::dpiScaled(ICON_PADDING * -1));
}

bool KxDocerTableStyleGalleryItem::isClickable() const
{
	return true;
}

void KxDocerTableStyleGalleryItem::click()
{
	if (m_loadState == IconLoadError)
	{
		if (m_bRetryHover)
			downloadResource();
		return;
	}

	QHash<QString, QString> args;
	getCustomResourceReportInfo(args);

	QString filePath = KxOnlineTableResHelper::getFileSavePath(m_resourceInfo.id, m_resourceInfo.md5);
	args.insert("act", QFile::exists(filePath) ? "use" : "download");
	QString payKey = KDocerUtils::generatePayKey();
	args.insert("pay_key", payKey);
	args.insert("download_key", m_resourceInfo.downloadKey);

	auto style = getButtonStyle(m_resourceInfo.bFree ? QStringList() : m_resourceInfo.privileges);
	QString skuType = style.skuKey;
	args.insert("pay_type", skuType.isEmpty() ? "open_vip_pro" : ("open_" + skuType));

	args.insert("download_type", m_resourceInfo.bFree ? "free_download" : "vip_download");
	args.insert("sub_table", getHasMultiChildTable() ? "true" : "false");

	if (!KxOnlineTableResHelper::checkLogin(LOGIN_SRC))
	{
		emit sigLeavePreview();
		args["act"] = "login";
		args["pay_key"] = "";
		args["download_type"] = "";
		KTableStyleInfoCollect::psotResourceClicked(args);
		return;
	}

	m_loadState = ResourceLoading;
	setLoading(true);
	KOnlineTableResManager::getInstance().downloadResFile(getTableDownloadInfo(), true,
		bindContext(this, [=](DownloadResult result, const QString& savePath) {
			if (!KDocerUtils::isCoreAppMatch(this))
			{
				emit sigDownloadError(getResourceInfo(false));
				return;
			}

			QHash<QString, QString> content = args;
			if (result != DownloadResult::Success)
			{
				if (result == DownloadResult::PrivilegeError)
				{
					emit sigLeavePreview();
					content["act"] = "pay";
					KTableStyleInfoCollect::psotResourceClicked(content);
					KxOnlineTableResHelper::openDocerUnifyPayDialog(getTableDownloadInfo(), payKey);

					m_loadState = ResourceLoadFinish;
					setLoading(false);
				}
				else
					emit sigDownloadError(getResourceInfo(false));
				return;
			}

			content["pay_key"] = "";
			KTableStyleInfoCollect::psotResourceClicked(content);
			emit insertOnlineResource(getResourceInfo(false), getReportInfo(true), savePath, false);
		}));
}

void KxDocerTableStyleGalleryItem::downloadResource()
{
	QString previewUrl = m_resourceInfo.previewUrlMap[m_thumbType];
	kdocerresnetwork::DownloadArgs args;
	args.saveArgs.saveFilePath = KxOnlineTableResHelper::getThumbCachePath(previewUrl, m_resourceInfo.id);
	args.resourceKey = "tablestyle";
	args.resourceArgs.downloadKey = m_resourceInfo.downloadKey;
	args.resourceArgs.id = m_resourceInfo.id;
	args.resourceArgs.urls = QStringList() << previewUrl;

	if (m_loadState == IconLoadError)
	{
		setLoading(true);
		m_loadState = IconReloading;
	}
	else
		m_loadState = IconLoading;
	docer::download(args,
		bindContext(this, [=](const QByteArray&) {
			if (!KDocerUtils::isCoreAppMatch(this))
				return;

			changeIconColor(m_currentColor);
			setLoading(false);
			if (m_bRetryHover)
				hover();
		}), 
		bindContext(this, [=](DownloadFailInfo) {
			if (!KDocerUtils::isCoreAppMatch(this))
				return;

			m_loadState = IconLoadError;
			setLoading(false);
			emit statusChanged();
		}));
}

void KxDocerTableStyleGalleryItem::onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc)
{
	if (resourceInfo.id != m_resourceInfo.id)
		return;

	m_loadState = ResourceLoadFinish;
	setLoading(false);
}

void KxDocerTableStyleGalleryItem::changeIconColor(const drawing::Color& color)
{
	QString thumbPath = KxOnlineTableResHelper::getThumbCachePath(m_resourceInfo.previewUrlMap[m_thumbType], m_resourceInfo.id);
	if (!QFile::exists(thumbPath))
		return;

	m_currentColor = color;
	const drawing::IThemeAgent* pTheme = GetTheme(KMainWindow::getCoreApplication(this));
	if (pTheme != nullptr)
	{
		drawing::SchemeColorInterpreter interpreter = pTheme->getColorSchemeInterpreter();
		m_resourceInfo.colorInfo["replace_rgb"] = KxOnlineTableResHelper::qColorToHex(color.toRgb(&interpreter));
	}

	QByteArray array;
	if (!KxOnlineTableResHelper::changeSVGColor(thumbPath, m_resourceInfo.colorInfo, array))
	{
		m_loadState = IconLoadError;
		return;
	}

	QSvgRenderer renderer(array);
	QSize iconSize = QSize(sizeHint().width() - KLiteStyle::dpiScaled(ICON_PADDING * 2), sizeHint().height() - KLiteStyle::dpiScaled(ICON_PADDING * 2));
	
	QPixmap pixmap(iconSize * krt::devicePixelRatio());
	pixmap.fill(Qt::transparent);

	QPainter painter(&pixmap);
	renderer.render(&painter);

	m_icon = QIcon(pixmap);
	m_loadState = IconLoadFinish;
	emit statusChanged();
}
