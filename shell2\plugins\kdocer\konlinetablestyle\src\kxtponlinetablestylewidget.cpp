﻿#include "stdafx.h"
#include <kcomctl/kappidlesvr.h>
#include <kcomctl/tik.h>
#include "kxtponlinetablestylewidget.h"
#include "kxonlinetablestylejsapi.h"
#include "kxonlinereshelper.h"
#include <kxshare/kxcommonwebapi.h>
#include <kcomctl/kpluginloadwidget.h>
#include "kdocerbasehelper.h"
#include "kdocercommondef.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include <qnetworkproxy.h>
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "kxshare/kxquickhelpbarcontainer.h"
#include "kcomctl/kcommandfactory.h"
#include "kxshare/kxtaskpanecommand.h"
#include "common/widget/kdocerwidgetutil.h"
#include <kdocertoolkit/kdocerutils.h>

using namespace std;
using namespace KxOnlineTableResHelper;

#define PocketWidgetWidth 512

namespace
{
	QString formatResult(const QVariantMap& result)
	{
		QVariantMap resultMap;
		resultMap["result"] = result;
		return ConvertJsonHelper::variantMapSerialize(resultMap);
	}
}

////////////////////////////////////////////////////////////////////////////////////////////
KxOnlineTableStyleWidget::KxOnlineTableStyleWidget(QWidget* parent, 
	KCommand* cmd, bool bIsInTaskPane,
	bool bIsQuickHelpWidget /*= false*/, bool bTopStyleShow /* = false */
	, docer::base::KDocerWidgetType widgetType/* = KDocerWidgetType_Default*/)
	: KDocerCommonWebWidget(parent, "konlinetablestyle", widgetType)
	, m_cmd(cmd)
	, m_bIsTaskPaneWidget(bIsInTaskPane)
	, m_ancesor(nullptr)
	, m_dwLastTick(0)
	, m_bSystemHovered(false)
	, m_bQuickHelpWidget(bIsQuickHelpWidget)
	, m_bLoadFinish(false)
	, m_bTopStyleShow(bTopStyleShow)
{
	if (m_cmd)
		setProperty("qtspyName", m_cmd->commandName().toString().append("_OnlineTableStyleWidget"));

	if (IDocerUserInfo* userInfo = getDocerUserInfo())
	{
		KUserInfoQuery query;
		query.moduleName = "konlinewpptablestyle";
		query.appId = "konlinewpptablestyle";
		userInfo->registerQueryInfo(query);
	}

	kxApp->idleSvr()->registerItem(this, true);
}

KxOnlineTableStyleWidget::~KxOnlineTableStyleWidget()
{
	if (IDocerUserInfo* userInfo = getDocerUserInfo())
		userInfo->unregisterQueryInfo("konlinewpptablestyle");
}

void KxOnlineTableStyleWidget::openTaskPane(const QString& param)
{
	if (m_ancesor)
		m_ancesor->close();

	QString p4;
	if (m_bQuickHelpWidget)
		p4 = "quickbar_tablestyle_more_btn";
	else if (!m_bIsTaskPaneWidget)
		p4 = "menubar_tablestyle_page_more_button";

	if (m_cmd)
	{
		m_cmd->setProperty("entrance", property("p4"));
		m_cmd->setProperty("p4", p4);
		m_cmd->setProperty("url", param);

		m_cmd->trigger();

		m_cmd->setProperty("url", "");
		m_cmd->setProperty("p4", "");
		m_cmd->setProperty("entrance", "");
	}
}

bool KxOnlineTableStyleWidget::event(QEvent* e)
{
	if (m_cmd && e->type() == KEvent::Idle)
	{
		m_cmd->update();

		if (KDocerUtils::isEtApp(this))
		{
			if (!m_bIsTaskPaneWidget && !m_cmd->isEnabled())
			{
				if (isVisible() && m_ancesor)
				{
					m_ancesor->close();
				}
			}
		}
	}
	return KDocerCommonWebWidget::event(e);
}

bool KxOnlineTableStyleWidget::isFromWorkBench()
{
	return property("p4").toString() == "menubar_tablestyle_page_more_button";
}

bool KxOnlineTableStyleWidget::isFromQuickHelp()
{
	return property("p4").toString() == "quickbar_tablestyle_more_btn";
}

bool KxOnlineTableStyleWidget::isFromBeautify()
{
	return property("p4").toString() == "rclick_tablebeauty";
}

void KxOnlineTableStyleWidget::notifyDisplay(bool show)
{
	callOnLoadFinish([&]{

		KxOnlineTableStyleJsApi* jsApi = qobject_cast<KxOnlineTableStyleJsApi*>(m_jsApi);
		if (jsApi)
		{
			if (show)
			{
				QString entrance = property("p1").toString();
				QString detailEntrance = property("p4").toString();
				QString paySource = this->getPaySource();

				jsApi->notifyDisplay(entrance, detailEntrance, paySource);
			}else{
				jsApi->callbackToJS("hideWindow", "");
			}
		}
	});
}

void KxOnlineTableStyleWidget::notifyUrlChanged(const QString& param)
{
	QVariantMap result;
	result["param"] = param;
	callbackToJS("notifyUrlChanged", formatResult(result));
}

bool KxOnlineTableStyleWidget::eventFilter(QObject*o, QEvent*e)
{
	//开发状态下屏蔽回车键(方便前端调试)
	if (isUseLocalResource())
	{
		if (e->type() == QEvent::KeyRelease || e->type() == QEvent::KeyPress)
		{
			QKeyEvent* kevent = (QKeyEvent*)e;
			if (kevent->key() == Qt::Key_Space ||
				kevent->key() == Qt::Key_Return ||
				kevent->key() == Qt::Key_Enter)
			{
				return true;
			}
		}
	}

	return KDocerCommonWebWidget::eventFilter(o, e);
}

void KxOnlineTableStyleWidget::hideEvent(QHideEvent* e)
{
	if (m_dwLastTick == 0)
		return;

	notifyDisplay(false);

	if (!KDocerUtils::isWppApp(this))
	{
		sendStayInfo();
	}

	if (m_bIsTaskPaneWidget)
	{
		setProperty("p4", "");
		setProperty("entrance", "");
	}

	m_dwLastTick = 0;
}

void KxOnlineTableStyleWidget::closeWindow(CloseReason reason)
{
	if (!m_bIsTaskPaneWidget)
	{
		KEvent event(KEvent::Completed);
		sendPropagatedEvent(parent(), &event);
		emit aboutToClose();
	}
}

bool KxOnlineTableStyleWidget::webViewEventFilter(QEvent* event)
{
	if (event->type() == QEvent::Wheel)
	{
		QWheelEvent* wheelEvent = dynamic_cast<QWheelEvent*>(event);
		if(wheelEvent)
		{
			QWheelEvent* e = new QWheelEvent(*wheelEvent);
			QCoreApplication::postEvent(parent(), e);
		}
	}

	return KDocerCommonWebWidget::webViewEventFilter(event);
}

void KxOnlineTableStyleWidget::callOnLoadFinish(Closure closure)
{
	if (m_bLoadFinish)
	{
		if (closure)
			closure();
	}else
	{
		m_delayClosures.push(closure);
	}
}

void KxOnlineTableStyleWidget::showEvent(QShowEvent* event)
{
	KDocerCommonWebWidget::showEvent(event);
	notifyDisplay(true);

	if (!KDocerUtils::isWppApp(this))
	{
		sendDisplayInfo();
	}
	m_dwLastTick = QDateTime::currentMSecsSinceEpoch();
}

ksolite::KxCommonJsApi* KxOnlineTableStyleWidget::initJsObject()
{
	auto jsapi = new KxOnlineTableStyleJsApi(m_webView, this);
	jsapi->setProperty("quickBar", property("quickBar"));
	return jsapi;
}

void KxOnlineTableStyleWidget::initWebView()
{
	KDocerCommonWebWidget::initWebView();

	KCommand* cmd = m_cmd;
	KxMainWindow* mw = kxApp->findRelativeMainWindowX(this);
	if (mw && m_cmd)
	{
		const QString targetCmd = m_cmd->property("targetCmd").toString();
		if (!targetCmd.isEmpty())
		{
			cmd = KCommandFactory::createCommand(targetCmd, mw, m_cmd);
			if (cmd == NULL)
				cmd = m_cmd;
		}
	}

	connect(m_jsApi, SIGNAL(showView()), this, SLOT(onDrawingFinish()));
	connect(cmd, SIGNAL(pannelEnable(PanelEnableStatus)), this, SLOT(pannelEnable(PanelEnableStatus)));

	connect(m_jsApi, SIGNAL(leaveOnlineResource()), cmd, SIGNAL(sigLeaverHover()));
	connect(cmd, SIGNAL(updateTableInfo(const TableInfo&)), this, SLOT(onUpdateTableInfo(const TableInfo&)));
	connect(m_jsApi, SIGNAL(insertOnlineResource(const KOTSResourceInfo&, const ReportInfo&, const QString&, bool))
		, cmd, SLOT(applyTableStyle(const KOTSResourceInfo&, const ReportInfo&, const QString&, bool)));
	connect(cmd, SIGNAL(sendInsertInfo(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&))
		, m_jsApi, SLOT(onInsertFinished(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)));

	connect(cmd, SIGNAL(sendSystemInsertInfo(int, OperatorErrorCode, const QString&))
		, m_jsApi, SLOT(onSystemInsertFinished(int, OperatorErrorCode, const QString&)));

	if(KDocerUtils::isEtApp(this))
	{
		connect(cmd, SIGNAL(sigGridLineStatuesChange(bool)), this, SLOT(onGridLineChecked(bool)));
		connect(cmd, SIGNAL(sigIndentifyStatus(TableRecoStaus, TableAnalyzeFrom)), this, SLOT(onIndentifyStatusChange(TableRecoStaus, TableAnalyzeFrom)));
		connect(cmd, SIGNAL(sigArrangeApplyResult(bool, TableArrangeResult, qint64, TableApplyInfo)), this, SLOT(onArrangeApplyResult(bool, TableArrangeResult, qint64, TableApplyInfo)));
		connect(cmd, SIGNAL(sigGetHasMultiChildTableResult(bool, bool)), m_jsApi, SLOT(onGetHasMultiChildTable(bool, bool)));
		connect(m_jsApi, SIGNAL(sigApplyArrange(const QString&, bool)), cmd, SIGNAL(sigApplyArrange(const QString&, bool)));
		connect(m_jsApi, SIGNAL(sigAnalyzeTable(TableAnalyzeFrom)), cmd, SIGNAL(sigAnalyzeTable(TableAnalyzeFrom)));
		connect(m_jsApi, SIGNAL(sigCancelAnalyze()), cmd, SIGNAL(sigCancelAnalyze()));
		connect(m_jsApi, SIGNAL(sigUpdateTableView()), cmd, SIGNAL(sigUpdateTableView()));
		connect(m_jsApi, SIGNAL(sigGetHasMultiChildTable(bool)), cmd, SIGNAL(sigGetHasMultiChildTable(bool)));
	}

	//本地资源应用信号槽
	connect(m_jsApi, SIGNAL(insertSystemResource(int, bool))
		, cmd, SLOT(applySystemTableStyle(int, bool)));
	connect(cmd, SIGNAL(hoverSystemResourceSuccess()), this, SLOT(onHoverSystemResource()));
	connect(m_jsApi, SIGNAL(leaveSystemResource()), this, SLOT(onLeaveSystemResource()));

	//信息上报
	connect(m_jsApi, SIGNAL(sendDownloadInfo(const QString&, const ReportInfo&, int, const QString&))
		, this, SLOT(sendDownloadInfo(const QString&, const ReportInfo&, int, const QString&)));
	connect(cmd, SIGNAL(sendInsertInfo(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&))
		, this, SLOT(sendInsertInfo(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)));

	connect(m_jsApi, SIGNAL(sigTabIndexChanged(int)), this, SIGNAL(sigTabIndexChanged(int)));
}


KxWebViewImplementationType KxOnlineTableStyleWidget::webviewType()
{
	return KXWEBVIEW_IMPL_TYPE_CEF;
}

void KxOnlineTableStyleWidget::onParentReady()
{
	QWidget* parent = parentWidget();
	if (!m_bIsTaskPaneWidget && parent)
	{
		parent->installEventFilter(this);

		//下拉框水平方向不可拖动
		KGallery* gallery = KTik::findParentByType<KGallery>(parent);
		if (gallery)
			gallery->setFixedWidth(KWPSStyle::dpiScaled(PocketWidgetWidth));

		if (m_ancesor = KTik::findParentByType<KPopupWidget>(parent))
		{
			m_ancesor->removeEventFilter(parent);
			m_ancesor->installEventFilter(this);
		}
	}
}

QWidget* KxOnlineTableStyleWidget::createWaitingWidget()
{
	if (m_bQuickHelpWidget)
	{
		m_waitingWidget = KPluginLoadWidget::createPluginLoadWidget(this, QLatin1String("konlinewpptablestyle_qh"));
		return m_waitingWidget;
	}

	if (!m_bIsTaskPaneWidget && !m_bQuickHelpWidget)
	{
		m_waitingWidget = KPluginLoadWidget::createPluginLoadWidget(this, QLatin1String("konlinetablestyle_dp"));
		return m_waitingWidget;
	}

	if (m_cmd && m_cmd->property("featureTableStyle").toBool())
	{
		return m_waitingWidget = KPluginLoadWidget::createPluginLoadWidget(this, QLatin1String("konlinewpptablestyle_feature"));
	}

	return KDocerCommonWebWidget::createWaitingWidget();
}

QString KxOnlineTableStyleWidget::getPaySource()
{
	QString entrance = property("p1").toString();
	QString detailEntrance = property("p4").toString();

	if (m_bIsTaskPaneWidget)
	{
		entrance = property("entrance").toString();
	}

	return KxOnlineTableResHelper::getPaySource(entrance, detailEntrance);
}

void KxOnlineTableStyleWidget::sendDisplayInfo()
{
	QString from = property("p4").toString();
	QString name = property("p1").toString();
	if (from.isEmpty() && name.isEmpty())
		return;
	QHash<QString,QString> args;
	args.insert("from", from);
	args.insert("name", name);

	KDocerUtils::postGeneralEvent("docer_tablestyle_display", args);
}

void KxOnlineTableStyleWidget::sendStayInfo()
{
	DWORD delta = QDateTime::currentMSecsSinceEpoch() - m_dwLastTick;

	QHash<QString,QString> args;
	args.insert("from", property("p4").toString());
	args.insert("name", property("p1").toString());
	args.insert("time", QString::number(delta));

	KDocerUtils::postGeneralEvent("docer_tablestyle_stay", args);
}

void KxOnlineTableStyleWidget::onHoverSystemResource()
{
	m_bSystemHovered = true;
}

void KxOnlineTableStyleWidget::onLeaveSystemResource()
{
	if (m_bSystemHovered)
		undo(this, 1);

	m_bSystemHovered = false;
}

void KxOnlineTableStyleWidget::onDrawingFinish()
{
	this->showWebWidget();
	m_bLoadFinish = true;

	while(!m_delayClosures.empty())
	{
		Closure closure = m_delayClosures.front();

		if (closure)
			closure();

		m_delayClosures.pop();
	}
}

void KxOnlineTableStyleWidget::pannelEnable(PanelEnableStatus status)
{
	QPointer<KxOnlineTableStyleWidget> spThis(this);
	callOnLoadFinish([=]{
		if (!spThis)
			return;
		KxOnlineTableStyleJsApi* jsApi = qobject_cast<KxOnlineTableStyleJsApi*>(m_jsApi);
		if (jsApi)
		{
			jsApi->pannelEnable(status);
		}
	});
}

void KxOnlineTableStyleWidget::tabIndexChanged(int index)
{
	QPointer<KxOnlineTableStyleWidget> spThis(this);
	auto callbackToJSFunc = [=]() {
		if (!spThis)
			return;
		KxOnlineTableStyleJsApi* jsApi = qobject_cast<KxOnlineTableStyleJsApi*>(m_jsApi);
		if (jsApi)
		{
			jsApi->tabIndexChanged(index);
		}

	};

	if (m_bLoadFinished)
		callbackToJSFunc();
	else
		m_delayClosures.push(callbackToJSFunc);
}

void KxOnlineTableStyleWidget::onGridLineChecked(bool checked)
{
	QPointer<KxOnlineTableStyleWidget> spThis(this);
	callOnLoadFinish([=] {
		if (!spThis)
			return;
		KxOnlineTableStyleJsApi* jsApi = qobject_cast<KxOnlineTableStyleJsApi*>(m_jsApi);
		if (jsApi)
		{
			jsApi->gridlineChckedChnage(checked);
		}
	});
}

void KxOnlineTableStyleWidget::onIndentifyStatusChange(TableRecoStaus status, TableAnalyzeFrom from)
{
	QPointer<KxOnlineTableStyleWidget> spThis(this);
	callOnLoadFinish([=] {
		if (!spThis)
			return;
		KxOnlineTableStyleJsApi* jsApi = qobject_cast<KxOnlineTableStyleJsApi*>(m_jsApi);
		if (jsApi)
		{
			jsApi->identifyResultNotify(status, from);
		}
	});
}

void KxOnlineTableStyleWidget::onArrangeApplyResult(bool bSuccess, TableArrangeResult errorType, qint64 totalTimes, TableApplyInfo tableApplyInfo)
{
	QPointer<KxOnlineTableStyleWidget> spThis(this);
	callOnLoadFinish([=] {
		if (!spThis)
			return;
		KxOnlineTableStyleJsApi* jsApi = qobject_cast<KxOnlineTableStyleJsApi*>(m_jsApi);
		if (jsApi)
		{
			jsApi->arrangeResultNotify(bSuccess, errorType, totalTimes, tableApplyInfo);
		}
	});
}

void KxOnlineTableStyleWidget::sendDownloadInfo(const QString& strResourceId, const ReportInfo& reportInfo, int errorCode, const QString& errorDesc)
{
	QHash<QString,QString> args;
	if (!reportInfo.from.isEmpty())
	{
		args.insert("from", reportInfo.from);
		args.insert("belong_page", reportInfo.belongPage);
		args.insert("tablestyle_id", reportInfo.tablestyleId);
	}
	else
	{
		args.insert("from", property("p4").toString());
		args.insert("belong_page", property("p1").toString());
		args.insert("tablestyle_id", strResourceId);
	}
	args.insert("tablestyle_download_status", "");
	args.insert("user_status_id", reportInfo.userStatusId);
	args.insert("source_status_id", reportInfo.sourceStatusId);
	args.insert("error_code", QString::number(errorCode));
	args.insert("error_reason", errorDesc);
	args.insert("operation", reportInfo.click ? "click" : "hover");
	KDocerUtils::postGeneralEvent("docer_tablestyle_commonality_download", args);
}

void KxOnlineTableStyleWidget::sendInsertInfo(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc)
{
	//多次绑定，排除非自己页面发生的插入上报
	if (m_bQuickHelpWidget)
	{
		if (reportInfo.belongPage != "quickbar_tablestyle_page")
			return;
	}
	else if (m_bIsTaskPaneWidget && !m_bQuickHelpWidget)
	{
		if ((reportInfo.belongPage != "taskbar_tablestyle_new_page")
			&& (reportInfo.belongPage != "taskbar_tablestyle_recommend_page")
			&& (reportInfo.belongPage != "taskbar_tablestyle_used_page")
			&& (reportInfo.belongPage != "taskbar_tablestyle_searchresult_page")
			&& (property("p1").toString() != "taskbar_tablestyle_page")) //et，文字组件场景
			return;
	}
	else if (!m_bQuickHelpWidget && !m_bIsTaskPaneWidget)
	{
		if (reportInfo.belongPage != "menubar_tablestyle_page")
			return;
	}
	QHash<QString,QString> args;
	if (!reportInfo.from.isEmpty())
	{
		args.insert("from", reportInfo.from);
		args.insert("belong_page", reportInfo.belongPage);
		args.insert("tablestyle_id", reportInfo.tablestyleId);
	}
	else
	{
		args.insert("from", property("p4").toString());
		args.insert("belong_page", property("p1").toString());
		args.insert("tablestyle_id", resourceInfo.id);
	}
	args.insert("tablestyle_insert_status", "");
	args.insert("user_status_id", reportInfo.userStatusId);
	args.insert("source_status_id", reportInfo.sourceStatusId);
	args.insert("error_code", QString::number(errorCode));
	args.insert("error_reason", errorDesc);
	KDocerUtils::postGeneralEvent("docer_tablestyle_commonality_used", args);
}

void KxOnlineTableStyleWidget::setKLMinfo(const QVariantMap& params)
{
	const char* strKLMInfo = "klmInfo";
	setProperty(strKLMInfo, params);
	if (m_jsApi)
	{
		QPair<QString, KxWebViewJSApiHandler*> handler = m_jsApi->findHandler("common.docerfeature.getKLMInfo");
		if (handler.second)
			handler.second->setProperty(strKLMInfo, property(strKLMInfo).toMap());
	}
}

QSize KxOnlineTableStyleWidget::sizeHint() const
{
	if (getIsTopstyleshow())
	{
		int nWidth = KLiteStyle::dpiScaled(418);
		auto policy = docer::widget::getMainWndHeightPolicy(this);
		if (policy == docer::widget::MainWndHeightPolicy::Between_1080_1440)
			return QSize(nWidth, KLiteStyle::dpiScaled(248));
		else if (policy == docer::widget::MainWndHeightPolicy::Over_1440)
			return QSize(nWidth, KLiteStyle::dpiScaled(372));
		return QSize(nWidth, KLiteStyle::dpiScaled(186));
	}
	return KDocerCommonWebWidget::sizeHint();
}
