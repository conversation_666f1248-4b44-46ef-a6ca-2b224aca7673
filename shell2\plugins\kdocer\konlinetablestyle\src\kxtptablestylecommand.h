﻿#pragma once

#include "kxonlinetablestyledefine.h"
#include <kxshare/kxtaskpanecommand.h>
#include <kcomctl/ktranspreview.h>

class KxtableStyleParser;

class KxDocerTpTableStyleCommand
	: public KCommand
{
	Q_OBJECT
public:
	explicit KxDocerTpTableStyleCommand(KxMainWindow* host, QObject* parent);
	~KxDocerTpTableStyleCommand();
public:
	void update();
	virtual PanelEnableStatus judgeEnableInsertTable();
	virtual OperatorErrorCode applyTableByTableInfo(const TableInfo& tableInfo);
	bool F4Repeat() override;

public slots:
	virtual void applyTableStyle(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, const QString& filePath, bool hover);
	void applySystemTableStyle(int resourceIndex, bool hover);
	virtual void onLeaveHoverPreview();

signals:
	void hoverOnlineResourceSuccess();
	void hoverSystemResourceSuccess();
	void updateTableInfo(const TableInfo& tableInfo);
	void pannelEnable(PanelEnableStatus status);
	void sendInsertInfo(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);
	void sendSystemInsertInfo(int resourceIndex, OperatorErrorCode errorCode, const QString& errorDesc);

protected:
	virtual void updateCurtableTableIndex();
	virtual OperatorErrorCode applyTableStyleDocument(const KOTSResourceInfo& resourceInfo, const QByteArray& content, bool hover);
	virtual OperatorErrorCode applySystemTableStyleDocument(int resourceIndex, bool hover);
	void checkStatusChange(PanelEnableStatus oldStatus);
	bool insertTableByCommand();

private:
	void recordRepeatInfo(const KOTSResourceInfo& resourceInfo, const QString& filePath);
	bool canRepeat();

protected:
	KxMainWindow* m_host;
	QPointer<KTransPreview> m_previewTr;
	bool m_bOnceFlag;
	PanelEnableStatus m_panelStatus = PanelEnableStatus::Enable;
	TableApplyInfo m_tableApplyInfo;
};

class KxTpWpsOnlineTableStyleCommand : public KxDocerTpTableStyleCommand
{
public:
	KxTpWpsOnlineTableStyleCommand(KxMainWindow* host, QObject* parent);
	virtual OperatorErrorCode applyTableStyleDocument(const KOTSResourceInfo& resourceInfo, const QByteArray& content, bool hover) override;
};