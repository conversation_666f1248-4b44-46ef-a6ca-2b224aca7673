#pragma once

class KxQhTableStyleTooltipsPopupWidget;

class KxQhTableSmartStyleCommand : public KxGalleryCommand
{
	Q_OBJECT

public:
	explicit KxQhTableSmartStyleCommand(KxMainWindow* host, QObject* parent);
	~KxQhTableSmartStyleCommand();

	virtual void update() override;
	virtual void syncCommandInfo(QWidget* widget) override;
	virtual QWidget* createExtendedWidget(QWidget* parent) override;
	virtual KCommand* clone(QObject* host, QObject* parent) override;
private:
	KCommand* m_proxyCommand;
	KCommand* m_featureCmd = nullptr;
};