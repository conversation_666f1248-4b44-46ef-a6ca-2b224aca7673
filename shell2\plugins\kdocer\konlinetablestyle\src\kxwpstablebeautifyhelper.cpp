﻿#include "stdafx.h"
#include "kxwpstablebeautifyhelper.h"
#include "kxshare/formatting_helper.h"
#include <public_header/drawing/view/theme_agent.h>
#include "kso/api/wpsapi_old.h"

namespace
{
	const QString strLineStyle = "lineStyle";
	const QString strLineWidth = "lineWidth";
	const QString strBorderType = "borderType";
	const QString strBorderColor = "borderColor";
	const QString strFontColor = "fontColor";
	const QString strBackgroundColor = "backgroundColor";
	const QString strBoldFont = "bBoldFont";
	const QString strBValid = "bValid";
	const QString strBorders = "borders";
	const QString strEmphasizeType = "emphasizeType";
	const QString strEmphasizeNum = "emphasizeNum";
	const QString strOriginStyle = "originStyle";
	const QString strHeaderCellStyle = "headerCellStyle";
	const QString strCurStyle = "curStyle";
	const QString strLastStyle = "lastStyle";
	const QString strRowOriginStyle = "rowOriginStyle";
	const QString strRow = "row";
	const QString strColumn = "column";
	const QString strIntersectCellInfo = "intersectCellInfo";
};

///////////////////////////KBorderStyleInfo//////////////////////////////////
KBorderStyleInfo::KBorderStyleInfo()
	: borderLineStyle(wpsLineStyleNone)
	, borderLineWidth(wpsLineWidth025pt)
	, borderType(wpsBorderTop)
{

}

KBorderStyleInfo::KBorderStyleInfo(const WpsLineStyle lineStyle, const WpsLineWidth lineWidth,
	const WpsBorderType bType, const QColor& color)
	: borderLineStyle(lineStyle)
	, borderLineWidth(lineWidth)
	, borderType(bType)
	, borderColor(color)
{

}

QJsonObject KBorderStyleInfo::toJsonObject()
{
	QJsonObject obj = {
		{strLineStyle, (int)borderLineStyle},
		{strLineWidth, (int)borderLineWidth},
		{strBorderType, (int)borderType},
		{strBorderColor, borderColor.name()},
	};

	return obj;
}

KBorderStyleInfo KBorderStyleInfo::fromJsonObject(const QJsonObject& obj)
{
	KBorderStyleInfo ret;
	if (!obj.contains(strLineStyle) || !obj.contains(strLineWidth)
		|| !obj.contains(strBorderType) || !obj.contains(strBorderColor))
		return ret;

	ret.borderLineStyle = (WpsLineStyle)obj.value(strLineStyle).toInt();
	ret.borderLineWidth = (WpsLineWidth)obj.value(strLineWidth).toInt();
	ret.borderType = (WpsBorderType)obj.value(strBorderType).toInt();
	ret.borderColor = QColor(obj.value(strBorderColor).toString());

	return ret;
}

/////////////////////////////////////////////////////////////////////////////

///////////////////////////KTableStyleInfo//////////////////////////////////
KEmphasizeStyleInfo::KEmphasizeStyleInfo()
	: bValid(false)
	, bBoldFont(false)
{

}

QJsonObject KEmphasizeStyleInfo::toJsonObject()
{
	QJsonObject obj = {
		{strFontColor, fontColor.name()},
		{strBackgroundColor, backgroundColor.name()},
		{strBoldFont, bBoldFont},
		{strBValid, bValid},
	};

	QJsonArray ar;
	foreach(KBorderStyleInfo border, borderStyleLst.values())
		ar.append(border.toJsonObject());

	obj[strBorders] = ar;

	return obj;
}

KEmphasizeStyleInfo KEmphasizeStyleInfo::fromJsonObject(const QJsonObject& obj)
{
	KEmphasizeStyleInfo ret;
	if (obj.contains(strFontColor))
		ret.fontColor = QColor(obj.value(strFontColor).toString());

	if (obj.contains(strBackgroundColor))
		ret.backgroundColor = QColor(obj.value(strBackgroundColor).toString());

	if (obj.contains(strBoldFont))
		ret.bBoldFont = (WpsLineStyle)obj.value(strBoldFont).toBool();

	if (obj.contains(strBValid))
		ret.bValid = (WpsLineStyle)obj.value(strBValid).toBool();

	if (obj.contains(strBorders))
	{
		QJsonArray ar = obj[strBorders].toArray();
		foreach(QJsonValue border, ar)
		{
			QJsonObject subObj = border.toObject();
			KBorderStyleInfo borderInfo = KBorderStyleInfo::fromJsonObject(subObj);
			if (borderInfo.borderColor.isValid())
				ret.borderStyleLst.insert(borderInfo.borderType, borderInfo);
		}
	}

	return ret;
}

KEmphasizeInfo::KEmphasizeInfo()
	: bValid(false)
	, emphasizeNum(-1)
	, emphasizeType(TET_Invalid)
{

}
////////////////////////////////////////////////////////////////////////////

///////////////////////////KEmphasizeInfo//////////////////////////////////
QT_NAMESPACE::QJsonObject KEmphasizeInfo::toJsonObject()
{
	QJsonObject obj = {
		{strEmphasizeType, (int)emphasizeType},
		{strEmphasizeNum, emphasizeNum},
	};

	if (originStyle.bValid)
		obj[strOriginStyle] = originStyle.toJsonObject();

	if (columnHeaderCellStyle.bValid)
		obj[strHeaderCellStyle] = columnHeaderCellStyle.toJsonObject();

	return obj;
}

KEmphasizeInfo KEmphasizeInfo::fromJsonObject(const QJsonObject& info)
{
	KEmphasizeInfo emInfo;
	if (info.isEmpty())
		return emInfo;

	if (!info.contains(strEmphasizeType) || !info.contains(strEmphasizeNum))
		return emInfo;

	emInfo.emphasizeType = (KTableEmphasizeType)info.value(strEmphasizeType).toInt();
	emInfo.emphasizeNum = info.value(strEmphasizeNum).toInt();
	emInfo.bValid = true;

	if (info.contains(strOriginStyle))
		emInfo.originStyle = KEmphasizeStyleInfo::fromJsonObject(info[strOriginStyle].toObject());

	if (info.contains(strHeaderCellStyle))
		emInfo.columnHeaderCellStyle = KEmphasizeStyleInfo::fromJsonObject(info[strHeaderCellStyle].toObject());


	return emInfo;
}

////////////////////////////////////////////////////////////////////////////

///////////////////////////KEmphasizeIntersectCellInfo//////////////////////////////////

KEmphasizeIntersectCellInfo::KEmphasizeIntersectCellInfo()
	:bValid(false)
	, row(-1)
	, column(-1)
{

}

QJsonObject KEmphasizeIntersectCellInfo::toJsonObject()
{
	QJsonObject obj;
	if (colStyle.bValid)
		obj[strCurStyle] = colStyle.toJsonObject();

	if (rowStyle.bValid)
		obj[strLastStyle] = rowStyle.toJsonObject();

	if (rowOriginStyle.bValid)
		obj[strRowOriginStyle] = rowOriginStyle.toJsonObject();

	obj[strRow] = row;
	obj[strColumn] = column;

	return obj;
}

KEmphasizeIntersectCellInfo KEmphasizeIntersectCellInfo::fromJsonObject(const QJsonObject& info)
{
	KEmphasizeIntersectCellInfo intersectInfo;
	if (info.contains(strCurStyle))
	{
		intersectInfo.colStyle = KEmphasizeStyleInfo::fromJsonObject(info[strCurStyle].toObject());
		intersectInfo.bValid = true;
	}

	if (info.contains(strLastStyle))
	{
		intersectInfo.rowStyle = KEmphasizeStyleInfo::fromJsonObject(info[strLastStyle].toObject());
		intersectInfo.bValid = true;
	}

	if (info.contains(strRowOriginStyle))
	{
		intersectInfo.rowOriginStyle = KEmphasizeStyleInfo::fromJsonObject(info[strRowOriginStyle].toObject());
		intersectInfo.bValid = true;
	}

	if (info.contains(strRow))
	{
		intersectInfo.row = info.value(strRow).toInt(-1);
	}

	if (info.contains(strColumn))
	{
		intersectInfo.column = info.value(strColumn).toInt(-1);
	}

	return intersectInfo;
}

////////////////////////////////////////////////////////////////////////////

///////////////////////////KEmphasizeTableHelper////////////////////////////
HRESULT KEmphasizeTableHelper::getCurSelectTable(wpsoldapi::Table** tb)
{
	*tb = nullptr;
	IKSelection* pSelection = KEmphasizeTableHelper::getCurSelection();
	if (!pSelection)
		return E_FAIL;

	ks_stdptr<wpsoldapi::Selection> spSelection;
	HRESULT hr = pSelection->QueryInterface(wpsoldapi::IID_Selection, (void**)&spSelection);
	if (FAILED(hr) || !spSelection)
		return E_FAIL;

	ks_stdptr<wpsoldapi::Tables> spTables;
	hr = spSelection->get_Tables(&spTables);
	if (FAILED(hr) || !spTables)
		return E_FAIL;

	ks_stdptr<wpsoldapi::Table> spTable;
	hr = spTables->Item(1, &spTable);
	if (FAILED(hr) || !spTable)
		return E_FAIL;

	*tb = spTable.detach();
	return S_OK;
}

IKView* KEmphasizeTableHelper::getActiveView()
{
	IKApplication* coreApp = kxApp->coreApplication();
	if (!coreApp)
		return NULL;

	return coreApp->GetActiveView();
}

IKSelection* KEmphasizeTableHelper::getCurSelection()
{
	IKView* view = getActiveView();
	if (view)
		return view->GetSelection();

	return NULL;
}

void KEmphasizeTableHelper::getShading(wpsoldapi::Shading** ppShading)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = getCurSelection();
	if (!spApiSelection)
		return;

	getSelShading(spApiSelection, ppShading);
}

QString KEmphasizeTableHelper::readEmphasizeInfo()
{
	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return "";

	ks_bstr info;
	hr = spTable->get_Descr(&info);
	if (FAILED(hr) || !info)
		return "";

	return krt::fromUtf16(info.c_str());
}

void KEmphasizeTableHelper::writeEmphasizeInfo(const QString& info)
{
	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return;
	ks_bstr desc(krt::utf16(info));
	spTable->put_Descr(desc);
}

void KEmphasizeTableHelper::getSelShading(wpsoldapi::Selection* selection, wpsoldapi::Shading** ppShading)
{
	KsoSelectionType nType;
	ks_stdptr<IKTxSelection> spTxSelection = selection;
	HRESULT hr = spTxSelection->GetType(&nType);
	if (FAILED(hr))
		return;

	UINT nMinorType = KsoSelectionTypeMinor(nType);
	ks_stdptr<wpsoldapi::Shading> spShading;

	if (nMinorType == TxSelectType_Table)
	{
		ks_stdptr<wpsoldapi::Tables> spTables;
		hr = selection->get_Tables(&spTables);
		if (FAILED(hr) || !spTables)
			return;

		ks_stdptr<wpsoldapi::Table> spTable;
		hr = spTables->Item(1, &spTable);
		if (FAILED(hr) || !spTable)
			return;

		ks_stdptr<wpsoldapi::Shading> spTableShading;
		hr = spTable->get_Shading(&spTableShading);
		if (FAILED(hr) || spTableShading)
			return;

		spShading = spTableShading;
	}
	else if (nMinorType == TxSelectType_TableCells || nMinorType == TxSelectType_TableRow
		|| nMinorType == TxSelectType_TableColumn || nMinorType == TxSelectType_TableInsertPoint)
	{
		ks_stdptr<wpsoldapi::Cells> spCells;
		hr = selection->get_Cells(&spCells);
		if (FAILED(hr) || !spCells)
			return;

		ks_stdptr<wpsoldapi::Shading> spCellsShading;
		hr = spCells->get_Shading(&spCellsShading);
		if (FAILED(hr) || !spCellsShading)
			return;

		spShading = spCellsShading;
	}
	else
	{
		ks_stdptr<wpsoldapi::_Font> spFont;
		hr = selection->get_Font(&spFont);
		if (FAILED(hr) || !spFont)
			return;

		ks_stdptr<wpsoldapi::Shading> spFontShading;
		hr = spFont->get_Shading(&spFontShading);
		if (FAILED(hr) || !spFontShading)
			return;

		spShading = spFontShading;
	}
	*ppShading = spShading.detach();
}

bool KEmphasizeTableHelper::getSelCellsFillColor(wpsoldapi::Selection* selection, QColor& color)
{
	ks_stdptr<wpsoldapi::Shading> spCellsShading;
	getSelShading(selection, &spCellsShading);
	if (!spCellsShading)
		return false;

	return getCellsFillColor(spCellsShading, color);
}

bool KEmphasizeTableHelper::getCellsFillColor(wpsoldapi::Shading* cells, QColor& color)
{
	ks_stdptr<IKShadingColor> spShadingColor = cells;
	if (!spShadingColor)
		return false;;

	drawing::Color tmp;
	HRESULT hr = spShadingColor->GetBackgroundPatternColor(&tmp);
	if (FAILED(hr))
	{
		color = QColor(Qt::white);
	}
	else
	{
		color = tmp.toRgb();
	}

	return true;
}

bool KEmphasizeTableHelper::getFontStyle(QColor& color, bool& bBold)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = getCurSelection();
	if (!spApiSelection)
		return false;

	return getSelFontStyle(spApiSelection, color, bBold);
}

bool KEmphasizeTableHelper::getSelFontStyle(wpsoldapi::Selection* selection, QColor& color, bool& bBold)
{
	ks_stdptr<wpsoldapi::_Font> spFont;
	HRESULT hr = selection->get_Font(&spFont);
	if (FAILED(hr) || !spFont)
		return false;

	long bold;
	hr = spFont->get_Bold(&bold);
	if (SUCCEEDED(hr))
		bBold = (bold == 0 ? false : true);

	ks_stdptr<wpsoldapi::ColorFormat> spColorFormat;
	hr = spFont->get_TextColor(&spColorFormat);
	if (FAILED(hr) || !spColorFormat)
		return false;

	ks_stdptr<IKsoColorFormatEx> spColorEx = spColorFormat;
	drawing::Color clr = spColorEx->GetColor();
	color = clr.toRgb();
	return true;
}

bool KEmphasizeTableHelper::getBorderStyle(QMap<WpsBorderType, KBorderStyleInfo>& borderStyleLst)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = getCurSelection();
	if (!spApiSelection)
		return false;

	return getSelBorderStyle(spApiSelection, borderStyleLst);
}

bool KEmphasizeTableHelper::getSelBorderStyle(wpsoldapi::Selection* selection, QMap<WpsBorderType, KBorderStyleInfo>& borderStyleLst)
{
	ks_stdptr<wpsoldapi::Borders> borders;
	HRESULT hr = selection->get_Borders(&borders);
	if (FAILED(hr) || !borders)
		return false;

	KBorderStyleInfo borderStyleInfo;
	QList<WpsBorderType> typeLst = { wpsBorderBottom , wpsBorderLeft, wpsBorderTop, wpsBorderRight };
	foreach(WpsBorderType bType, typeLst)
	{
		borderStyleInfo.borderType = bType;

		ks_stdptr<wpsoldapi::Border> spBorder;
		hr = borders->Item(bType, &spBorder);
		if (FAILED(hr) || !spBorder)
			continue;

		hr = spBorder->get_LineStyle(&borderStyleInfo.borderLineStyle);
		if (FAILED(hr))
			continue;

		hr = spBorder->get_LineWidth(&borderStyleInfo.borderLineWidth);
		if (FAILED(hr))
			continue;

		ks_stdptr<IKBorderColor>spBorderColor = spBorder;
		if (!spBorderColor)
			continue;

		drawing::Color col;
		hr = spBorderColor->GetColor(&col);
		if (FAILED(hr))
			continue;

		borderStyleInfo.borderColor = col.toRgb();
		borderStyleLst.insert(borderStyleInfo.borderType, borderStyleInfo);
	}

	return true;
}

int KEmphasizeTableHelper::getColorGray(const drawing::Color& o, int gray)
{
	gray -= 1;
	if (gray < -1 || gray > 4)
		return -1;

	QColor color;
	switch (o.type())
	{
	case drawing::Color::SRgb:
	{
		color = o.toRgb();
		break;
	}
	case drawing::Color::ScRgb:
	{
		double r = 0.0, g = 0.0, b = 0.0;
		o.getCrgb(&r, &g, &b);

		color = QColor::fromRgbF(r, g, b);
		if (gray == -1)
			return color.rgb();
		break;
	}
	case drawing::Color::Scheme:
	{
		const drawing::IThemeAgent* themeAgent = GetTheme(kxApp->coreApplication());
		if (!themeAgent)
			return -1;

		drawing::SchemeColorInterpreter interpreter = themeAgent->getColorSchemeInterpreter();
		color = o.toRgb(&interpreter);
		break;
	}
	default: break;
	}

	if (gray < 0)
		return -1;

	const qreal full_dark[] = { 0.50, 0.35, 0.25, 0.15, 0.05 };
	const qreal full_light[] = { -0.05, -0.15, -0.25, -0.35, -0.50 };
	const qreal very_dark[] = { 0.90, 0.75, 0.50, 0.25, 0.10 };
	const qreal very_light[] = { -0.10, -0.25, -0.50, -0.75, -0.90 };
	const qreal normal_lightness[] = { 0.80, 0.60, 0.40, -0.25, -0.50 };
	int lightness = color.lightness();
	qreal brightness = 0.;
	if (lightness == 0)
		brightness = full_dark[gray];
	else if (lightness == 255)
		brightness = full_light[gray];
	else if (lightness >= 204)
		brightness = very_light[gray];
	else if (lightness <= 50)
		brightness = very_dark[gray];
	else
		brightness = normal_lightness[gray];

	KThemeColorItem result(color);
	result.setBrightness(brightness);
	return result.toQColor().rgb();
}

bool KEmphasizeTableHelper::setFontStyle(const QColor& col, const bool bBold)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = getCurSelection();
	if (!spApiSelection)
		return false;

	return setSelFontStyle(spApiSelection, col, bBold);
}

bool KEmphasizeTableHelper::setSelFontStyle(wpsoldapi::Selection* selection, const QColor& col, const bool bBold)
{
	ks_stdptr<wpsoldapi::_Font> spFont;
	HRESULT hr = selection->get_Font(&spFont);
	if (FAILED(hr))
		return false;

	if (bBold)
	{
		spFont->put_Bold(-1);
	}
	else
	{
		spFont->put_Bold(0);
	}

	if (col.isValid())
	{
		ks_stdptr<wpsoldapi::ColorFormat> spColorFormat;
		hr = spFont->get_TextColor(&spColorFormat);
		if (FAILED(hr) || !spColorFormat)
			return false;

		ks_stdptr<IKsoColorFormatEx> spColorEx = spColorFormat;
		drawing::Color color;
		color.setRgb(col.rgba());
		spColorEx->SetColor(color);
	}

	return true;
}

bool KEmphasizeTableHelper::selectCellWithIndex(const int row, const int col)
{
	if (row <= 0 || col <= 0)
		return false;

	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return false;

	ks_stdptr<wpsoldapi::Cell> spCell;
	hr = spTable->Cell(row, col, &spCell);
	if (FAILED(hr) || !spCell)
		return false;

	spCell->Select();
	return true;
}

bool KEmphasizeTableHelper::getCurSelectIndex(int& row, int& column)
{
	IKView* pView = getActiveView();
	ASSERT(pView);
	IKSelection* pSelection = pView->GetSelection();
	ASSERT(pSelection);
	ks_stdptr<wpsoldapi::Selection> spApiSelection = pSelection;
	ASSERT(spApiSelection);
	HRESULT hr = E_FAIL;
	ks_stdptr<wpsoldapi::Cells> spCells;
	hr = spApiSelection->get_Cells(&spCells);
	if (FAILED(hr) || !spCells)
		return false;

	ks_stdptr<wpsoldapi::Cell> spCell;
	hr = spCells->Item(1, &spCell);
	if (FAILED(hr) || !spCell)
		return false;

	hr = spCell->get_RowIndex((long*)&row);
	if (FAILED(hr))
		return false;

	hr = spCell->get_ColumnIndex((long*)&column);
	if (FAILED(hr))
		return false;

	return true;
}

bool KEmphasizeTableHelper::getTableColRowInfo(wpsoldapi::Table* table, int& rowCnt, int& colCnt)
{
	HRESULT hr = E_FAIL;
	ks_stdptr<wpsoldapi::Rows> spRows;
	hr = table->get_Rows(&spRows);
	if (FAILED(hr) || !spRows)
		return false;

	hr = spRows->get_Count((long*)&rowCnt);
	if (FAILED(hr))
		return false;

	ks_stdptr<wpsoldapi::Columns> spCols;
	hr = table->get_Columns(&spCols);
	if (FAILED(hr) || !spCols)
		return false;

	hr = spCols->get_Count((long*)&colCnt);
	if (FAILED(hr))
		return false;

	return true;
}

////////////////////////////////////////////////////////////////////////////
