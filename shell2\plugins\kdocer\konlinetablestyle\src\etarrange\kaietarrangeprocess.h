﻿#pragma once

#include "kaietrecognizedata.h"
#include "kaietarrangeprocessdata.h"
#include "kxonlinetablestyledefine.h"
#include <smartidentify.h>
#include <kso/framework/coreobj.h>

class KArrangeProcess
	: public QObject
	, public IKCoreNotifyFilter
{
	Q_OBJECT
public:
	KArrangeProcess(const kaietrecognize::ParentChildTableInfo& tbinfo,
		const QSharedPointer<TableApplyParam>& pTblApplyInfo,
		const QSharedPointer<kaietrecognize::TableRangeStyle>& pTableRangeStyle,
		IStopProcess* pStop,
		bool bIgnoreSelectRange = true);
	~KArrangeProcess();

	HRESULT process(IStopProcess* pStop, TableApplyInfo& tableApplyInfo);
	void resetParam(const kaietrecognize::ParentChildTableInfo& tbinfo,
		const QSharedPointer<TableApplyParam>& pTblApplyInfo,
		const QSharedPointer<kaietrecognize::TableRangeStyle>& pTableRangeStyle,
		bool bResetDataTable = true,
		bool bIgnoreSelectRange = true);
	HRESULT resetColorOrStyle(IStopProcess* pStop, bool bServiceRecoResult);
	BOOL coreObjectIsDestroyed();
	IKWorksheet* getWorkSheet() { return m_spWorkSheet; }
	IKWorkbook* getWorkBook() { return m_spWorkbook; }
	int getWorkSheetIdx();

	bool isEnableAreaApply();
private:
	STDPROC_(BOOL) OnFilterNotify(ksoNotify* notify);
	HRESULT init(bool bIgnoreSelectRange);
	//获取表格识别数据
	HRESULT initRecData();
	//获取表格每一行的单元格以及合并单元格的信息
	HRESULT initAtomicTable();
	//初始化视图大小
	void initViewSize();
	void clearRangeCellVec();

	//更新当前选中范围，用于限制样式应用范围
	void updateSelectRange();

	void printfColWidthTestInfo();

	//前置处理
	HRESULT doPreProcess();
	//中置处理
	HRESULT doMidProcess();
	//后置处理
	HRESULT doPostProcess();
	//算法落地应用
	void applyProcessResult();
	//自定义排版处理-借由自适应宽高能力
	HRESULT doAutoFitProcess(bool& bUseAutoFit, TableApplyInfo& tableApplyInfo);

	//获取指定区域的Range
	HRESULT getSpecifiedRange(OUT etoldapi::Range** ppRange, IN int rowBegin, IN int rowEnd, IN int colBegin, IN int colEnd);
	HRESULT getSpecifiedRange(OUT etoldapi::Range** ppRange, IN const RANGE& rg);

	//相关api的汇总调用
	void callApiTest();

	//获取样式相关设置
	HRESULT getStyleData();

	//标准化调整
	HRESULT standardProcess();
	//结构标准化
	HRESULT structStandardProcess();

	//结构化调整
	HRESULT structProcess();

	//根据每列的字符数估算要设置的列宽
	HRESULT processRowHAndColW();

	//适应屏幕调整
	void doAdaptProcess();

	//获取当前的实际列宽
	double getColRealWidth(int col);
	//获取当前的实际行高
	double getRowRealHeight(int row);

	//调整列宽(单位:磅)
	void adjustColumnsWidth();
	//调整每一列的列宽
	void processEachAtomicCol(AtomicCol* pEachAtomicCol);

	//调整行高
	HRESULT adjustRowsHeight();
	//调整每一行的行高
	void processEachAtomicRow(AtomicRow* pEachAtomicRow);

	//调整行高留白
	HRESULT adjustRowHeightWithSpace();

	//获取行高上下间距
	double getRowSpaingWithChar();
	//获取列宽的最大字符数
	double getColMaxWidthWithChar();

	//计算具体单元格里面内容所应该占的宽度(单位:磅)
	QVector<int> getCellInfo(IN int row, IN int col, IN kaietrecognize::ZoneType zoneType, OUT int& cellWidth, OUT int& cellCharCnt, OUT bool& bLineBreak);

	//根据文字内容返回文本宽度
	int getTextWidth(ks_wstring text, kaietrecognize::ZoneType zoneType);
	//获取单元格的预设宽度
	double getCellWidth(AtomicRange* pEachAtomicRange);

	//获取单元格的估算的高度(与内核的排版会有误差) bAllowOverflow表示是否允许内容溢出
	double estimatedCellHeight(const QVector<int>& eachParaTextWidthVec, double dCurWidth, double eachLineHeight, bool bTextOverflow = false);
	double getCustomCellEstimatedHeight(AtomicRange* pEachAtomicRange,
		double eachLineHeight, const QVector<int>& eachParaTextWidthVec, bool bTextOverflow);

	QString getFontName();

	//合并区域整理
	HRESULT mergeAreaProcess();
	HRESULT mergeRange(etoldapi::Range* pRange, IETStringTools* pTools);

	//判断一个cell是否存在用户手动折行的情况
	bool bContainManualLineBreak(ROW cellRow, COL cellCol);
	//获取某一个单元格的内容
	HRESULT getCellText(IN ROW cellRow, IN COL cellCol, OUT ks_wstring& text);

	//获取指定的列
	HRESULT getSpecifiedCol(IN COL colIdx, OUT etoldapi::Range** ppRange);
	//获取一个特定的单元格
	HRESULT getSpecifiedCell(IN ROW cellRow, IN COL cellCol, OUT etoldapi::Range** ppRange);

	//初始化字号设置
	void initFontSizeInfo();
	single getCellFontSize(int row, int col);

	//初始化et相关信息参数
	void getRangeData();

	/////////处理合并单元格相关方法
	void processMergeCells();
	void processEachMergeCell(AtomicCells* pEachAtomicCells);

	double getMergeCellHeight(AtomicRange* pEachAtomicRange);
	void checkAndExpandMergeCellHeight(AtomicCells* pEachAtomicCells, bool bEnableAlignLeft);
	double getVerMergeCellWidth(IN const double dWidth, IN const int iRow);
	double getHorAndVerMergeCellWidth(double dWidth, int iRow, int iCol);
	void expandMergeCellWidth(AtomicRange* pEachAtomicRange, double dMoreColWidth);
	void expandMergeCellHeight(AtomicRange* pEachAtomicRange, double dMoreRowHeight);
	bool isMergeCell(int row, int col);
	bool isHorMergeCell(int row, int col);
	//////////////
	//处理内嵌图片的相关方法
	void processCellImgFmla();
	double getImgCellColMinWidth();
	double getImgCellRowMinHeight();

	//收集所有列的列宽以及水平对齐方式
	void presetColWidth(int iCol, double dWidth);
	double getPresetColWidth(int iCol);
	void syncColPresetWidth2RealWidth();
	//收集所有行的行高
	void presetRowHeight(int iRow, double dHeight);
	double getPresetRowHeight(int iRow);
	void syncRowPresetHeight2RealHeight();
	void restoreRowHidden(int iRow);
	void restoreColHidden(int iCol);

	//应用相关属性落地
	void applyAtomicTableProp();

	void applyAtomicTableFontProp();
	void applyAtomicTableAlignProp();

	void applyAtomicTableStyle();
	void applyFillAlterPlan();
	void applyFillTblHeadArea();
	void applyFillFirstLastColArea();
	void applyFillOtherArea();
	void applyFillAllContent();
	void applyFillRowAlterPlan();
	void applyFillColAlterPlan();
	void applyFillAllContentBorder();

	void applyRangeStyle(const RANGE& rg, const io_utils::DXF* pDXF, bool bClear = false, const KXFMASK* pMask = nullptr);
	void clearTableStyle();
	void clearRangeStyle(const RANGE& rg, const KXFMASK* pMask = nullptr);

	// 获取sheet上所有数据表区域
	HRESULT initDataTableRanges();
	//获取指定区域内不包含数据表的RangeList
	HRESULT getNoDataTableRangeList(OUT std::vector_s<RANGE>& rgl, const std::vector_s<RANGE>& ranges);

	static RANGE Rect2Range(const RECT& rect, IDX idxSht, BMP_PTR pBMP);

private:
	ks_stdptr<IKWorkbook> m_spWorkbook;
	ks_stdptr<IKWorksheet> m_spWorkSheet;

	ks_stdptr<etoldapi::Range>  m_spAllRangeCell;
	QVector<etoldapi::Range*> m_titleRangeCellVec;
	QVector<etoldapi::Range*> m_headRangeCellVec;
	QVector<etoldapi::Range*> m_contentRangeCellVec;
	QVector<etoldapi::Range*> m_subTitleRangeCellVec;
	QVector<etoldapi::Range*> m_otherRangeCellVec;
	QVector<etoldapi::Range*> m_infoRangeCellVec;

	single m_HeadRangeFontSize;
	single m_TitleRangeFontSize;
	single m_ContentRangeFontSize;
	single m_SubTitleRangeFontSize;
	single m_OtherRangeFontSize;
	single m_InfoRangeFontSize;

private:
	QSharedPointer<kaietrecognize::TableRangeStyle> m_pTableRangeStyle;

	kaietrecognize::ParentChildTableInfo m_tableInfo;

	QSharedPointer<TableApplyParam> m_pTableApplyParam;
	et_sptr<AtomicTable> m_spAtomicTable;

	bool m_bNeedResetDataTable = true;
	bool m_ignoreSelectRange = true;

	adaptScreenPrcessProxy* m_pAdaptScreenProxy;
	ViewSize m_viewSize;
	IStopProcess* m_pStop;

	std::vector_s<RANGE> m_dataTableRanges;
	QList<DWORD> m_dataTableStyleOpts;
	std::vector_s<RANGE> m_dataSelectRanges;
};

//辅助类
class KArrangeProcessAssistant : public QObject, public IStopProcess
{
	Q_OBJECT
public:
	KArrangeProcessAssistant(const QJsonObject& jsonDataObj, IKWorksheet* pWorkSheet, bool bServiceRecoResult = true);
	~KArrangeProcessAssistant();

	HRESULT doArrangeProcessList(const QString& tpye, QList<int> rangeIdxs, TableApplyInfo& tableApplyInfo);
	HRESULT doArrangeSwitchColorList(const KxtableStyleParser& parser, QList<int> rangeIdxs, bool bClearDataTable = true, bool bIgnoreSelectRange = true);
	bool getIsApplyAreaEnable(bool bApplyAll);
	bool getIsKFpCcombEnableAreaApply();
	virtual void stopProcess();
	virtual bool isContinue();
	ULONG AddRef();
	ULONG Release();
	kaietrecognize::TableRangeInfoList getRangeInfoList() const;
private:
	void parseRecData(const QJsonObject& jsonDataObj, IKWorksheet* pWorkSheet, bool bServiceRecoResult);
	HRESULT doApplyProcess(int rangeIdx, TableApplyInfo& tableApplyInfo);
	HRESULT doSwitchColor(int rangeIdx, bool bRestDataTable = true, bool bIgnoreSelectRange = true);
	//事务相关
	void initTranscation();
	void onBeginOrganize();
	void onEndOrganize(bool bCommit);

private:
	//识别返回的数据结构
	kaietrecognize::TableRangeInfoList m_rangeInfoList;
	int m_tableCnt;
	std::vector<kaietrecognize::TableStructCollectInfo> m_tableStructCollectInfoList;

	//前端返回的套用相关参数的数据结构
	QSharedPointer<TableApplyParam> m_pTableApplyPara;
	QSharedPointer<kaietrecognize::TableRangeStyle> m_pTableRangeStyle;

	//事务
	ks_stdptr<IKTransactionTool> m_spTransTool;
	//缩放比
	double m_curZoom;
	et_sptr<KArrangeProcess> m_spArrangeProcess;
	bool m_bNeedRollback;
	bool m_bFirstSendRevoke;
	bool m_bCanSendRevoke;
	bool m_bServiceRecoResult = true;
	KxMainWindow* m_pMainWindow = nullptr;
	long m_ref = 1;
};
