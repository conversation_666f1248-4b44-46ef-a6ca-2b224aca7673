﻿#include "stdafx.h"

#include "kaietarrangeprocess.h"
#include <kxshare/kxapplication.h>
#include <kxshare/kxview.h>
#include <kxshare/kxmainwindow.h>
#include <smartparam.h>
#include "kaietarrangeprocessdata.h"
#include "applogic/et_api_convert.h"
#include "etcore/et_core_basic.h"
#include "pivot_core/pivot_core_x.h"
#include <ksolite/kdocercoreitef.h>
#include <ksolite/ksolog/kxloggerlite.h>
#include <mso/io/basic/lenthconv.h>

#define MAX_ROW_HEIGHT	8190 //内核设置行高的最大值,单位:缇
#define MAX_COL_DEFAULT_CHAR_CNT 50 //表示超过此阈值就要考虑折行处理
#define MAX_HANDLE_CELL_CNT 100000 //自适应排版最大处理单元格数，避免被长时间占用主进程导致界面无响应
#define CHECK_PROCESSEVENTS_FREQ 200
#define HANDLE_AUTO_FIT_CELL_CNT 100000
#define STR_HANDLE_AUTO_FIT_CELL_CNT "handleAutoFitCellCnt"

static const QString NONE_FILL_COLOR = "#FFFFFF";
static const QString NONE_TEXT_COLOR = "#000000";
static const QString NONE_BORDER_COLOR = "#000000";
constexpr qint64 g_GroupIdisEnableAreaApply = 62113;

namespace
{
	HRESULT makeEtColor(IN QString strColor, IN IKWorkbook* pWorkBook, OUT long& colorValue)
	{
		if (!pWorkBook)
			return E_FAIL;

		//Todo:后续得考虑兼容值 = origin 或者 empty 的两种情况
		strColor = strColor.replace("#", "");
		QColor color(strColor.toUInt(NULL, 16));

		IBook* pBook = pWorkBook->GetBook();
		if (!pBook)
			return E_FAIL;

		EtColor etColor(ectARGB);
		etColor.setARGB(color.rgba());
		colorValue = ARGB2BGR(
			pBook->ToARGB(etColor, GdiAutoBkColor));
		return S_OK;
	}

	void CrossKXFMask(KXFMASK& mask, const KXFMASK* pMask)
	{
		if (pMask == nullptr)
			return;
		mask._cats = (XFMASK::_category)(mask._cats & pMask->_cats);
		mask._catsFont = (XFMASK::_category_font)(mask._catsFont & pMask->_catsFont);
	}

	bool checkProcessEvents(KArrangeProcess* pProcess)
	{
		QPointer<KArrangeProcess> spProcess = pProcess;
		QCoreApplication::processEvents();
		return spProcess;
	}

	ks_stdptr<etoldapi::Areas> getSelectionAreas(IKWorksheet* pWorkSheet)
	{
		if (!pWorkSheet)
			return nullptr;

		ks_stdptr<IKWorksheetView> spSheetView = pWorkSheet->GetActiveWorksheetView();
		if (!spSheetView)
			return nullptr;

		ks_stdptr<IKEtView> spETView = spSheetView->GetWindow()->GetActiveView();
		if (!spETView)
			return nullptr;

		ks_stdptr<IKEtWindow> spWindow = spETView->GetWindow();
		if (!spWindow)
			return nullptr;

		ks_stdptr<ISheetWndInfos> spSheetWndInfo = pWorkSheet->GetWndInfos();
		if (!spSheetWndInfo)
			return nullptr;

		ks_stdptr<IKRanges> spRanges;
		HRESULT hr = spSheetWndInfo->GetSelection(spWindow->GetIndex(), &spRanges);
		if (FAILED(hr) || !spRanges)
			return nullptr;

		ks_stdptr<etoldapi::Range> curRange;
		hr = pWorkSheet->GetRangeByData(spRanges, &curRange);
		if (FAILED(hr))
			return nullptr;

		ks_stdptr<etoldapi::Areas> spAreas;
		hr = curRange->get_Areas(&spAreas);
		if (FAILED(hr) || !spAreas)
			return nullptr;

		return spAreas;	
	}

	RANGE etRange2RANGE(const ks_stdptr<etoldapi::Range>& spApiRange, const BMP_PTR& bmp)
	{
		if (!spApiRange)
			return RANGE(bmp);

		do
		{
			long row = 0;
			long column = 0;
			HRESULT hr = spApiRange->get_Row(&row);
			if (FAILED(hr) || row == 0)
				break;

			hr = spApiRange->get_Column(&column);
			if (FAILED(hr) || column == 0)
				break;

			ks_stdptr<etoldapi::Range> spRows;
			hr = spApiRange->get_Rows(&spRows);
			if (FAILED(hr) || !spRows)
				break;

			ks_stdptr<etoldapi::Range> spColumns;
			hr = spApiRange->get_Columns(&spColumns);
			if (FAILED(hr) || !spColumns)
				break;

			long rowCnt = 0;
			long columnCnt = 0;
			hr = spRows->get_Count(&rowCnt);
			if (FAILED(hr) || rowCnt == 0)
				break;

			hr = spColumns->get_Count(&columnCnt);
			if (FAILED(hr) || columnCnt == 0)
				break;

			ES_CUBE cube;
			cube.rowFrom = row - 1;
			cube.rowTo = row + rowCnt - 2;
			cube.colFrom = column - 1;
			cube.colTo = column + columnCnt - 2;
			return RANGE(cube, bmp);

		} while (false);

		return RANGE(bmp);
	}

	bool isKFpCcombEnableAreaApply()
	{
		static bool bEnable = []() ->bool {
			if (docer::KLiteDocerCoreMgr::GetDocerCoreLite())
				return docer::KLiteDocerCoreMgr::GetDocerCoreLite()->getFpcCombValue(g_GroupIdisEnableAreaApply, "isEnableAreaApply", true).toBool();

			return true;
		}();

		return bEnable;
	}
}

KArrangeProcess::KArrangeProcess(const kaietrecognize::ParentChildTableInfo& tbinfo,
	const QSharedPointer<TableApplyParam>& pTblApplyInfo,
	const QSharedPointer<kaietrecognize::TableRangeStyle>& pTableRangeStyle,
	IStopProcess* pStop,
	bool bIgnoreSelectRange /*= true*/)
	: m_HeadRangeFontSize(11)
	, m_TitleRangeFontSize(16)
	, m_ContentRangeFontSize(10)
	, m_SubTitleRangeFontSize(14)
	, m_OtherRangeFontSize(10)
	, m_InfoRangeFontSize(10)
	, m_pTableRangeStyle(pTableRangeStyle)
	, m_tableInfo(tbinfo)
	, m_pTableApplyParam(pTblApplyInfo)
	, m_pAdaptScreenProxy(nullptr)
	, m_pStop(pStop)
{
	init(bIgnoreSelectRange);
}

KArrangeProcess::~KArrangeProcess()
{
	if (m_spWorkSheet)
		m_spWorkSheet->UnRegisterNotifyFilter(this);
	if (m_pAdaptScreenProxy)
	{
		delete m_pAdaptScreenProxy;
		m_pAdaptScreenProxy = nullptr;
	}

	clearRangeCellVec();
}


HRESULT KArrangeProcess::process(IStopProcess* pStop, TableApplyInfo& tableApplyInfo)
{
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle process: start process");
	if (pStop)
		m_pStop = pStop;
	if (!m_spWorkbook || !m_pTableApplyParam)
		return E_FAIL;
	if (m_pStop && !m_pStop->isContinue())
		return E_FAIL;

	HRESULT hr = E_FAIL;
	hr = doPreProcess();
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle process: finish PreProcess");
	if (FAILED(hr))
		return E_FAIL;

	if (!checkProcessEvents(this))
		return E_FAIL;
	if (coreObjectIsDestroyed() || (m_pStop && !m_pStop->isContinue()))
		return E_FAIL;

	bool bUseAutoFit = false;
	hr = doAutoFitProcess(bUseAutoFit, tableApplyInfo);
	if (FAILED(hr) || bUseAutoFit)
		return hr;	

	hr = doMidProcess();
	if (FAILED(hr))
		return E_FAIL;
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle process: finish doMidProcess");

	if (m_pStop && !m_pStop->isContinue())
		return E_FAIL;
	doPostProcess();
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle process: finish doPostProcess");
	if (!checkProcessEvents(this))
		return E_FAIL;
	if (coreObjectIsDestroyed() || (m_pStop && !m_pStop->isContinue()))
		return E_FAIL;

	applyProcessResult();
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle process: finish applyProcessResult");
	return S_OK;
}

void KArrangeProcess::resetParam(const kaietrecognize::ParentChildTableInfo& tbinfo,
	const QSharedPointer<TableApplyParam>& pTblApplyInfo,
	const QSharedPointer<kaietrecognize::TableRangeStyle>& pTableRangeStyle,
	bool bResetDataTable /*= true*/,
	bool bIgnoreSelectRange /*= true*/)
{
	m_tableInfo = tbinfo;
	m_pTableRangeStyle = pTableRangeStyle;
	m_pTableApplyParam = pTblApplyInfo;
	m_bNeedResetDataTable = bResetDataTable;
	init(bIgnoreSelectRange);
}

HRESULT KArrangeProcess::resetColorOrStyle(IStopProcess* pStop, bool bServiceRecoResult)
{
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle apply: start apply");
	if (pStop)
		m_pStop = pStop;
	if (!m_spWorkbook || !m_pTableApplyParam)
		return E_FAIL;
	if (m_pStop && !m_pStop->isContinue())
		return E_FAIL;

	if (bServiceRecoResult)
	{
		HRESULT hr = E_FAIL;
		hr = doPreProcess();
		if (FAILED(hr))
			return E_FAIL;
		KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle apply: finish PreProcess");

		if (m_pStop && !m_pStop->isContinue())
			return E_FAIL;

		hr = doMidProcess();
		if (FAILED(hr))
			return E_FAIL;
		KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle apply: finish doMidProcess");

		if (m_pStop && !m_pStop->isContinue())
			return E_FAIL;
	}
	else
	{
		m_spAtomicTable = new AtomicTable(m_tableInfo.parentRangeInfo.allRangeInfo, m_spWorkSheet);
		if (!m_spAtomicTable)
			return E_FAIL;
	}
	
	applyAtomicTableStyle();
	if (!checkProcessEvents(this))
		return E_FAIL;
	return coreObjectIsDestroyed() ? E_FAIL : S_OK;
}

BOOL KArrangeProcess::coreObjectIsDestroyed()
{
	return !m_spWorkSheet || !m_spWorkbook || m_spWorkSheet->IsDestroyed() || m_spWorkbook->IsDestroyed();
}

HRESULT KArrangeProcess::doPreProcess()
{
	HRESULT hr = getStyleData();
	initFontSizeInfo();
	if (m_pTableApplyParam->getEffectType() == AdaptScreenEffect)
		initViewSize();

	return hr;
}

HRESULT KArrangeProcess::doMidProcess()
{
	HRESULT hr = E_FAIL;
	hr = standardProcess();
	if (FAILED(hr))
		return hr;
	hr = structProcess();
	return hr;
}

HRESULT KArrangeProcess::doPostProcess()
{
	//Todo:适应屏幕调整
	if (m_pTableApplyParam->getEffectType() == AdaptScreenEffect)
		doAdaptProcess();

	return S_OK;
}

void KArrangeProcess::applyProcessResult()
{
	//真正设置单元格属性
	if (m_pStop && !m_pStop->isContinue())
		return;
	applyAtomicTableProp();
	if (!checkProcessEvents(this))
		return;

	//真正设置列宽
	if (coreObjectIsDestroyed() || (m_pStop && !m_pStop->isContinue()))
		return;
	syncColPresetWidth2RealWidth();
	if (!checkProcessEvents(this))
		return;

	//真正设置行高
	if (coreObjectIsDestroyed() || (m_pStop && !m_pStop->isContinue()))
		return;
	syncRowPresetHeight2RealHeight();
}

HRESULT KArrangeProcess::doAutoFitProcess(bool& bUseAutoFit, TableApplyInfo& tableApplyInfo)
{
	bUseAutoFit = false;

	QJsonObject fpcCombJsonObj;
	int handleAutoFitCellCnt = HANDLE_AUTO_FIT_CELL_CNT;
	docer::KDocerCoreLiteInterface* docerCoreLite = docer::KLiteDocerCoreMgr::GetDocerCoreLite();
	if (docerCoreLite && docerCoreLite->isFpcInitFinished())
	{
		fpcCombJsonObj = docerCoreLite->getFpcCombJsonObject(g_strGroupModuleId, g_strNonEmptyCellLimitRecId);
		if (fpcCombJsonObj.contains(STR_HANDLE_AUTO_FIT_CELL_CNT))
			handleAutoFitCellCnt = fpcCombJsonObj.value(STR_HANDLE_AUTO_FIT_CELL_CNT).toInt();
	}

	if (tableApplyInfo.nonemptyCellCnt <= handleAutoFitCellCnt)
		return S_OK;

	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle process: start auto fit");
	qint64 processStartTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	applyAtomicTableFontProp();
	qint64 fontEndTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle process: finish Font setting, total times: %1")
		.arg(fontEndTime - processStartTime).toStdWString());

	applyAtomicTableAlignProp();
	KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle process: finish Align setting, total times: %1")
		.arg(QDateTime::currentDateTime().toMSecsSinceEpoch() - fontEndTime).toStdWString());

	if (!checkProcessEvents(this))
		return E_FAIL;
	if (coreObjectIsDestroyed() || (m_pStop && !m_pStop->isContinue()))
		return E_FAIL;

	HRESULT hr = E_FAIL;
	double wTableAdapt = 0;

	//最适列宽调整
	KComVariant maxColDefaultCharCnt;
	double maxColDefaultChar = getColMaxWidthWithChar();
	if (m_pTableApplyParam->getEffectType() == AdaptScreenEffect)
		maxColDefaultChar = MAX_COL_DEFAULT_CHAR_CNT * 2;
	maxColDefaultCharCnt.AssignDouble(maxColDefaultChar);

	int cnt = 0;
	int checkProcesseventsFreq = MAX_HANDLE_CELL_CNT / (m_tableInfo.parentRangeInfo.allRangeInfo.rowTo - m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom + 1) + 1;
	for (int col = m_tableInfo.parentRangeInfo.allRangeInfo.colFrom; col <= m_tableInfo.parentRangeInfo.allRangeInfo.colTo; col++)
	{
		if (cnt++ == checkProcesseventsFreq)
		{
			cnt = 0;
			if (!checkProcessEvents(this))
				return E_FAIL;
			if (coreObjectIsDestroyed() || (m_pStop && !m_pStop->isContinue()))
				return E_FAIL;
		}

		ks_stdptr<etoldapi::Range> spColRange;
		hr = getSpecifiedRange(&spColRange, m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom, m_tableInfo.parentRangeInfo.allRangeInfo.rowTo, col, col);
		if (FAILED(hr) || !spColRange)
			return E_FAIL;

		spColRange->put_ColumnWidth(maxColDefaultCharCnt);
		spColRange->AutoFitCol();

		double colRealWidth = getColRealWidth(col);

		WholeEffectBase* pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
		if (!pWholeEffect)
			return E_FAIL;
		colRealWidth += pWholeEffect->getColSpacingWithChar(colRealWidth);
		colRealWidth = colRealWidth > getColMaxWidthWithChar() ? getColMaxWidthWithChar() : colRealWidth;

		wTableAdapt += colRealWidth;

		KComVariant colWidth;
		colWidth.AssignDouble(colRealWidth);
		spColRange->put_ColumnWidth(colWidth);
	}
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle process: finish ColumnWidth setting");

	//针对适屏，重新设置列宽
	double wTableAdaptAve = wTableAdapt / (m_tableInfo.parentRangeInfo.allRangeInfo.colTo - m_tableInfo.parentRangeInfo.allRangeInfo.colFrom + 1);
	if (m_pTableApplyParam->getEffectType() == AdaptScreenEffect)
	{
		for (int col = m_tableInfo.parentRangeInfo.allRangeInfo.colFrom; col <= m_tableInfo.parentRangeInfo.allRangeInfo.colTo; col++)
		{
			ks_stdptr<etoldapi::Range> spColRange;
			HRESULT hr = getSpecifiedRange(&spColRange, m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom, m_tableInfo.parentRangeInfo.allRangeInfo.rowTo, col, col);
			if (FAILED(hr) || !spColRange)
				return E_FAIL;

			double colRealWidth = getColRealWidth(col);
			if (colRealWidth > wTableAdaptAve * 2)
			{
				KComVariant colWidth;
				colWidth.AssignDouble(wTableAdaptAve * 2);
				spColRange->put_ColumnWidth(colWidth);
			}
		}
	}

	//最适行高调整
	cnt = 0;
	checkProcesseventsFreq = MAX_HANDLE_CELL_CNT / (m_tableInfo.parentRangeInfo.allRangeInfo.colTo - m_tableInfo.parentRangeInfo.allRangeInfo.colFrom + 1) + 1;
	double dMaRowHeight = MAX_ROW_HEIGHT / 20;
	double currentHeight = 0;
	int currentRowFrom = m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom;
	for (int row = m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom; row <= m_tableInfo.parentRangeInfo.allRangeInfo.rowTo; row++)
	{
		if (cnt++ == checkProcesseventsFreq)
		{
			cnt = 0;
			if (!checkProcessEvents(this))
				return E_FAIL;
			if (coreObjectIsDestroyed() || (m_pStop && !m_pStop->isContinue()))
				return E_FAIL;
		}
		ks_stdptr<etoldapi::Range> spOneRowRange;
		hr = getSpecifiedRange(&spOneRowRange, row, row, m_tableInfo.parentRangeInfo.allRangeInfo.colFrom, m_tableInfo.parentRangeInfo.allRangeInfo.colTo);
		if (FAILED(hr) || !spOneRowRange)
			continue;

		KCOMPTR(etoldapi::Range) pRowRange;
		spOneRowRange->get_EntireRow(&pRowRange);
		KCOMPTR(IRangeInfo) ptrRowRangeInfo;
		pRowRange->QI(IRangeInfo, &ptrRowRangeInfo);
		ptrRowRangeInfo->InnerAutoFit(TRUE);

		double dHeight = getRowRealHeight(row) + getRowSpaingWithChar();
		if (m_pTableApplyParam->getEffectType() == AdaptScreenEffect)
			dHeight += 2;
		if (dHeight > dMaRowHeight)
			dHeight = dMaRowHeight;
		if (0 == currentHeight)
			currentHeight = dHeight;
		if (currentHeight == dHeight)
			continue;

		KComVariant rowHeight;
		rowHeight.AssignDouble(currentHeight);

		ks_stdptr<etoldapi::Range> spRowRange;
		hr = getSpecifiedRange(&spRowRange, currentRowFrom, row - 1, m_tableInfo.parentRangeInfo.allRangeInfo.colFrom, m_tableInfo.parentRangeInfo.allRangeInfo.colTo);
		if (FAILED(hr) || !spRowRange)
			continue;
		spRowRange->put_RowHeight(rowHeight);

		currentRowFrom = row;
		currentHeight = dHeight;
	}
	KComVariant rowHeight;
	rowHeight.AssignDouble(currentHeight);

	ks_stdptr<etoldapi::Range> spRowRange;
	hr = getSpecifiedRange(&spRowRange, currentRowFrom, m_tableInfo.parentRangeInfo.allRangeInfo.rowTo, m_tableInfo.parentRangeInfo.allRangeInfo.colFrom, m_tableInfo.parentRangeInfo.allRangeInfo.colTo);
	if (SUCCEEDED(hr) && spRowRange)
		spRowRange->put_RowHeight(rowHeight);
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle process: finish RowHeight setting");
	
	bUseAutoFit = true;
	tableApplyInfo.isAutoFit = true;
	return S_OK;
}

BOOL KArrangeProcess::OnFilterNotify(ksoNotify* notify)
{
	switch (notify->notify)
	{
	case ksoNotify::ksoDestroy:
		if (notify->coreObj->GetType() == etCoWorksheet)
		{
			notify->coreObj->UnRegisterNotifyFilter(this);
			m_spWorkSheet = nullptr;
			m_spWorkbook = nullptr;
		}
	}
	return TRUE;
}

int KArrangeProcess::getWorkSheetIdx()
{
	int sheetIdx = 0;
	do
	{
		ks_stdptr<IKWorkbook> spWorkbook = getWorkBook();
		if (!spWorkbook)
			break;

		ks_stdptr<IKWorksheets> spWorkSheets = spWorkbook->GetWorksheets();
		if (!spWorkSheets)
			break;

		int sheetCnt = spWorkSheets->GetSheetCount();
		ks_stdptr<etoldapi::_Worksheet> _spWorkSheet = getWorkSheet();
		if (!_spWorkSheet)
			break;

		ks_bstr bstrCurName;
		HRESULT hr = _spWorkSheet->get_Name(&bstrCurName);
		if (FAILED(hr))
			break;
		QString curSheetName = krt::fromUtf16(bstrCurName.c_str());
		

		for (int idx = 0; idx < sheetCnt; idx++)
		{
			ks_stdptr<etoldapi::_Worksheet> spWorkSheet = spWorkSheets->GetSheetItem(idx);
			if (!spWorkSheet)
				break;

			ks_bstr bstrName;
			hr = spWorkSheet->get_Name(&bstrName);
			if (FAILED(hr))
				break;
			QString sheetName = krt::fromUtf16(bstrName.c_str());
			if (curSheetName == sheetName)
			{
				sheetIdx = idx;
				break;
			}
		}
	} while (false);

	return sheetIdx;
}

bool KArrangeProcess::isEnableAreaApply()
{
	if (!m_spWorkSheet)
		return false;

	ks_stdptr<etoldapi::Areas> spAreas = getSelectionAreas(m_spWorkSheet);
	if (!spAreas)
		return false;

	long nAreaCnt = 0;
	HRESULT hr = spAreas->get_Count(&nAreaCnt);
	if (FAILED(hr))
		return false;

	if (nAreaCnt != 1)
		return true;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return false;

	ks_stdptr<etoldapi::Range> spRange;
	hr = spAreas->get_Item(1, &spRange);
	if (FAILED(hr) || !spRange)
		return false;

	RANGE rg = etRange2RANGE(spRange, pSheet->GetBMP());
	if (!rg.IsValid())
		return false;
	if (rg.RowFrom() == rg.RowTo() && rg.ColFrom() == rg.ColTo())
		return false;

	//合并单元格算做单选
	for (ROW row = rg.RowFrom(); row <= rg.RowTo(); row++)
	{
		for (COL col = rg.ColFrom(); col <= rg.ColTo(); col++)
		{
			if (!isMergeCell(row, col))
				return true;
		}
	}
	
	ks_stdptr<etoldapi::Range> spRangeTopLeftCell;
	hr = getSpecifiedCell(rg.RowFrom(), rg.ColFrom(), &spRangeTopLeftCell);
	if (FAILED(hr) || !spRangeTopLeftCell)
		return false;

	KCOMPTR(etoldapi::Range) spMergeTopLeft;
	hr = spRangeTopLeftCell->get_MergeArea(&spMergeTopLeft);
	if (FAILED(hr) || !spMergeTopLeft)
		return false;

	RANGE rgTopLeft = etRange2RANGE(spMergeTopLeft, pSheet->GetBMP());
	if (!rgTopLeft.IsValid())
		return false;

	ks_stdptr<etoldapi::Range> spRangeBottomRightCell;
	hr = getSpecifiedCell(rg.RowTo(), rg.ColTo(), &spRangeBottomRightCell);
	if (FAILED(hr) || !spRangeBottomRightCell)
		return false;

	KCOMPTR(etoldapi::Range) spMergeBottomRight;
	hr = spRangeBottomRightCell->get_MergeArea(&spMergeBottomRight);
	if (FAILED(hr) || !spMergeBottomRight)
		return false;

	RANGE rgBottomRight = etRange2RANGE(spMergeBottomRight, pSheet->GetBMP());
	if (!rgBottomRight.IsValid())
		return false;

	RANGE tmpRange = rgTopLeft.Intersect(rgBottomRight);
	if (!tmpRange.IsValid())
		return true;
	return false;
}

HRESULT KArrangeProcess::init(bool bIgnoreSelectRange)
{
	//初始化成员变量
	HRESULT hr = initRecData();
	if(m_bNeedResetDataTable)
		initDataTableRanges();

	m_ignoreSelectRange = bIgnoreSelectRange;
	m_dataSelectRanges.clear();
	return hr;
}

//Todo:这方法为给产品和UI同学使用来测试列宽估算误差以及打印整一列所有单元的字符列宽
void KArrangeProcess::printfColWidthTestInfo()
{
	if (m_spAllRangeCell)
	{
		ks_stdptr<etoldapi::Font> spRangeFont;
		m_spAllRangeCell->get_Font(&spRangeFont);
		if (spRangeFont)
		{
			ks_bstr fontName(krt::utf16(getFontName()));
			spRangeFont->put_Name(fontName);
			KComVariant fontSize;
			fontSize.AssignDouble(m_ContentRangeFontSize);
			spRangeFont->put_Size(fontSize);
		}
	}

	qDebug() << "================【KArrangeProcess::outPutColWidth】 Info Start===================";
	for (int col = m_tableInfo.parentRangeInfo.allRangeInfo.colFrom; col <= m_tableInfo.parentRangeInfo.allRangeInfo.colTo; col++)
	{
		qDebug() << "【KArrangeProcess::outPutColWidth】 colId=" << col;
		double colMaxWidth = 0;
		double colSumWidth = 0;
		double colMeanWidth = 0;
		int rowCnt = m_tableInfo.parentRangeInfo.allRangeInfo.rowTo - m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom + 1;
		QVector<double> colEachCellWidth;
		for (int row = m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom; row <= m_tableInfo.parentRangeInfo.allRangeInfo.rowTo; row++)
		{
			//Todo:若为合并单元格 则跳过 单列的纵向合并单元格是否也要跳过呢？？？
			if (isMergeCell(row, col))
				continue;
			int cellTextWidth = 0;
			int cellCharCnt = 0;
			bool bLineBreak = false;
			getCellInfo(row, col, kaietrecognize::ZoneType::Content, cellTextWidth, cellCharCnt, bLineBreak);
			float contentMaxCharCnt = KArrangeProcessHelper::viewWidth2CharsWidth(cellTextWidth, m_spWorkbook);
			colSumWidth += contentMaxCharCnt;
			colEachCellWidth.push_back(contentMaxCharCnt);
			if (contentMaxCharCnt > colMaxWidth)
				colMaxWidth = contentMaxCharCnt;
			qDebug() << "【KArrangeProcess::outPutColWidth】rowId =" << row << ",guest width=" << contentMaxCharCnt;
		}
		if (rowCnt > 0)
			colMeanWidth = colSumWidth / rowCnt;
		double sqrDiv = 0;
		for (int i = 0; i < colEachCellWidth.size(); i++)
		{
			sqrDiv += ((colEachCellWidth[i] - colMeanWidth) * (colEachCellWidth[i] - colMeanWidth));
		}
		if (rowCnt > 0)
		{
			sqrDiv = sqrDiv / rowCnt;
			qDebug() << "【KArrangeProcess::outPutColWidth】sqrDiv =" << sqrDiv;
		}

		ks_stdptr<etoldapi::Range> spColRange;
		HRESULT hr = getSpecifiedRange(&spColRange, m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom, m_tableInfo.parentRangeInfo.allRangeInfo.rowTo, col, col);
		if (FAILED(hr) || !spColRange)
			return;
		KComVariant colWidth;
		spColRange->get_ColumnWidth(&colWidth);
		if (V_VT(&colWidth) == VT_R8)
		{
			colWidth.AssignDouble(200);
			spColRange->put_ColumnWidth(colWidth);
			spColRange->AutoFitCol();
		}

		spColRange->get_ColumnWidth(&colWidth);
		if (V_VT(&colWidth) == VT_R8)
		{
			double autoFitWidth = V_R8(&colWidth);
			qDebug() << "【KArrangeProcess::outPutColWidth】autoFitWidth =" << autoFitWidth;
			qDebug() << "【KArrangeProcess::outPutColWidth】guestMaxWidth =" << colMaxWidth;
			qDebug() << "【KArrangeProcess::outPutColWidth】guestMaxWidth - autoFitWidth =" << colMaxWidth - autoFitWidth;
			qDebug() << "+++++++++++++++【KArrangeProcess::outPutColWidth】 col finish+++++++++++++++";
		}
	}
	qDebug() << "===============【KArrangeProcess::outPutColWidth】 Info End===================";
}

void KArrangeProcess::processMergeCells()
{
	if (!m_spAtomicTable)
		return;
	int mergeCellsCnt = m_spAtomicTable->m_mergeCellsList.size();
	for (int i = 0; i < mergeCellsCnt; i++)
	{
		AtomicCells* pEachAtomicCells = m_spAtomicTable->m_mergeCellsList.at(i);
		if (!pEachAtomicCells)
			continue;
		processEachMergeCell(pEachAtomicCells);

	}
}

void KArrangeProcess::processEachMergeCell(AtomicCells* pEachAtomicCells)
{
	HRESULT hr = S_CONTINUE;

	int rowCnt = pEachAtomicCells->getRowCnt();
	int colCnt = pEachAtomicCells->getColCnt();
	bool bOneRow = pEachAtomicCells->IsOneRowCell();
	bool bOneCol = pEachAtomicCells->isOneColCell();
	MergeCellType mergeCellType = HorMergeCell;
	if (bOneRow && !bOneCol)
		mergeCellType = HorMergeCell;
	else if (!bOneRow && bOneCol)
		mergeCellType = VerMergeCell;
	else
		mergeCellType = HorAndVerMergeCell;

	//合并单元格的内容拉直所占的列宽
	int cellTextWidth = 0;
	int cellCharCnt = 0;
	bool bLineBreak = false;

	kaietrecognize::ZoneType zoneType = pEachAtomicCells->getZoneType();
	bool bContentType = (zoneType == kaietrecognize::Content);

	float contentMaxCharCnt = pEachAtomicCells->getEstWidthWithChar();
	QVector<int> eachParaTextWidthVec = pEachAtomicCells->getEachParaTextWidthVec();

	//计算合并单元格所占的宽度
	double sumWidth = getCellWidth(pEachAtomicCells);
	double eachLineHeight = pEachAtomicCells->getCellCharHeight();

	if (HorMergeCell == mergeCellType)//横向合并
	{
		if (contentMaxCharCnt > sumWidth)
		{
			//分配列宽
			double dMoreColWidth = contentMaxCharCnt - sumWidth;
			expandMergeCellWidth(pEachAtomicCells, dMoreColWidth);

		}
	}
	else if (VerMergeCell == mergeCellType)//纵向合并
	{
		//先调列宽
		double dNewWidth = getVerMergeCellWidth(contentMaxCharCnt, rowCnt);
		if (sumWidth < dNewWidth)//现有的列宽比新列宽还大的情形 则不调整
		{
			//更新新的列宽
			presetColWidth(pEachAtomicCells->iLeft, dNewWidth);
		}
	}
	else if (HorAndVerMergeCell == mergeCellType)
	{
		//先调列宽
		double dNewWidth = getHorAndVerMergeCellWidth(contentMaxCharCnt, rowCnt, colCnt);
		if (dNewWidth > sumWidth)
		{
			double dMoreColWidth = dNewWidth - sumWidth;
			expandMergeCellWidth(pEachAtomicCells, dMoreColWidth);
		}
	}
	bool bEnableAlignLeft = (bContentType && (HorMergeCell == mergeCellType || HorAndVerMergeCell == mergeCellType));
	checkAndExpandMergeCellHeight(pEachAtomicCells, bEnableAlignLeft);

}

double KArrangeProcess::getCellWidth(AtomicRange* pEachAtomicRange)
{
	return m_spAtomicTable->getCellWidth(pEachAtomicRange);
}

double KArrangeProcess::getMergeCellHeight(AtomicRange* pEachAtomicRange)
{
	return m_spAtomicTable->getMergeCellHeight(pEachAtomicRange);
}

double KArrangeProcess::estimatedCellHeight(const QVector<int>& eachParaTextWidthVec, double dCurWidth,
	double eachLineHeight, bool bTextOverflow /*= false*/)
{
	int sumLineCnt = 0;
	if (dCurWidth < 0)
		return eachLineHeight;
	for (int i = 0; i < eachParaTextWidthVec.size(); i++)
	{
		//Todo:后续得考虑对这个viewWidth2CharsWidth方法的依赖去掉
		float eachParaTextWidthCharCnt = KArrangeProcessHelper::viewWidth2CharsWidth(eachParaTextWidthVec[i], m_spWorkbook);
		//Todo:目前这种算法估算高度会有误差(尤其是在英文放不下的情况)
		int lineCnt = 1;
		if (!bTextOverflow)
		{
			lineCnt = ceil(eachParaTextWidthCharCnt / dCurWidth);
			if (lineCnt < 1)
				lineCnt = 1;
		}
		sumLineCnt += lineCnt;
	}
	//最少也要为一行
	if (sumLineCnt < 1)
		sumLineCnt = 1;
	return eachLineHeight * sumLineCnt;
}

void KArrangeProcess::checkAndExpandMergeCellHeight(AtomicCells* pEachAtomicCells, bool bEnableAlignLeft)
{
	return m_spAtomicTable->checkAndExpandMergeCellHeight(pEachAtomicCells, bEnableAlignLeft);
}

double KArrangeProcess::getCustomCellEstimatedHeight(AtomicRange* pEachAtomicRange, double eachLineHeight,
	const QVector<int>& eachParaTextWidthVec, bool bTextOverflow)
{
	double sumWidth = getCellWidth(pEachAtomicRange);
	double calcSumHeight = estimatedCellHeight(eachParaTextWidthVec, sumWidth, eachLineHeight, bTextOverflow);
	return calcSumHeight;
}

double KArrangeProcess::getVerMergeCellWidth(IN const double dWidth, IN const int iRow)
{
	if (iRow <= 10)
	{
		if (dWidth <= 10)
			return dWidth;
		else if (dWidth <= 32)
		{
			if (iRow <= 5)
				return dWidth / 2;
			else
				return dWidth / 3;
		}
		else if (dWidth <= 50)
		{
			if (iRow <= 5)
				return dWidth / 3;
			else
				return dWidth / 4;
		}
		else
		{
			//Todo:按照单个单元格的内容列宽规则调整
			return getColMaxWidthWithChar();
		}
	}
	else
	{
		if (dWidth <= 50)
			return dWidth / 3;
		else
		{
			//Todo:按照单个单元格的内容列宽规则调整
			return getColMaxWidthWithChar();
		}
	}

}

double KArrangeProcess::getHorAndVerMergeCellWidth(double dWidth, int iRow, int iCol)
{
	if (dWidth <= 10 * iCol)
		return dWidth;
	else if (dWidth <= 32 * iCol)
	{
		if (iRow <= 5)
			return dWidth / 2;
		else
			return dWidth / 3;
	}
	else if (dWidth <= 50 * iCol)
	{
		if (iRow <= 5)
			return dWidth / 3;
		else
			return dWidth / 4;
	}
	else
	{
		//Todo:按照单个单元格的内容列宽规则调整
		return getColMaxWidthWithChar();
	}
}

void KArrangeProcess::expandMergeCellWidth(AtomicRange* pEachAtomicRange, double dMoreColWidth)
{
	HRESULT hr = S_CONTINUE;
	int colCnt = pEachAtomicRange->getColCnt();
	if (colCnt <= 0)
		return;

	QMap<int, bool> bColFinishAllocateWidth;//记录对每一列是否已完成分配空间
	QMap<int, double> colNewWidthMap;//列的最终宽度

	for (int colIdx = pEachAtomicRange->iLeft; colIdx <= pEachAtomicRange->iRight; colIdx++)
	{
		bColFinishAllocateWidth.insert(colIdx, false);
		double dWidth = getPresetColWidth(colIdx);
		colNewWidthMap.insert(colIdx, dWidth);

	}
	double dColMaxWidthWithChar = getColMaxWidthWithChar();
	while (colCnt > 0 && dMoreColWidth > 0.1)
	{
		double dEachMoreColWidth = dMoreColWidth / colCnt;
		for (int colIdx = pEachAtomicRange->iLeft; colIdx <= pEachAtomicRange->iRight; colIdx++)
		{
			if (bColFinishAllocateWidth[colIdx])
				continue;
			if (colNewWidthMap[colIdx] >= dColMaxWidthWithChar)
			{
				bColFinishAllocateWidth[colIdx] = true;
				colCnt--;
				continue;
			}

			double newWidth = colNewWidthMap[colIdx] + dEachMoreColWidth;
			if (newWidth >= dColMaxWidthWithChar)
			{
				double realAddWidth = dColMaxWidthWithChar - colNewWidthMap[colIdx];
				dMoreColWidth -= realAddWidth;
				colNewWidthMap[colIdx] = dColMaxWidthWithChar;
				bColFinishAllocateWidth[colIdx] = true;
				colCnt--;
			}
			else
			{
				dMoreColWidth -= dEachMoreColWidth;
				colNewWidthMap[colIdx] += dEachMoreColWidth;
			}
		}

	}

	for (int colIdx = pEachAtomicRange->iLeft; colIdx <= pEachAtomicRange->iRight; colIdx++)
	{
		double dWidth = colNewWidthMap[colIdx];
		presetColWidth(colIdx, dWidth);
	}
}

void KArrangeProcess::expandMergeCellHeight(AtomicRange* pEachAtomicRange, double dMoreRowHeight)
{
	return m_spAtomicTable->expandMergeCellHeight(pEachAtomicRange, dMoreRowHeight);
}

bool KArrangeProcess::isMergeCell(int row, int col)
{
	ks_stdptr<etoldapi::Range> spRangeCell;
	HRESULT hr = getSpecifiedCell(row, col, &spRangeCell);
	if (FAILED(hr) || !spRangeCell)
		return false;

	KCOMPTR(etoldapi::Range) spMerge;
	hr = spRangeCell->get_MergeArea(&spMerge);
	if (SUCCEEDED(hr) && spMerge)
	{
		long nCellCnt = 0;
		spMerge->get_Count(&nCellCnt);
		if (nCellCnt > 1)
		{
			return true;
		}
	}
	return false;
}

void KArrangeProcess::processCellImgFmla()
{
	HRESULT hr = S_CONTINUE;
	if (!m_spAtomicTable)
		return;
	size_t hasImgFmlaCnt = m_spAtomicTable->m_imgFmlaCellsList.size();
	for (size_t i = 0; i < hasImgFmlaCnt; i++)
	{
		AtomicRange* pAtomicRange = m_spAtomicTable->m_imgFmlaCellsList.at(i);
		if (!pAtomicRange)
			continue;
		double dWidth = getCellWidth(pAtomicRange);
		double dHeight = getMergeCellHeight(pAtomicRange);
		if (dWidth < getImgCellColMinWidth())
		{
			double dMoreColWidth = getImgCellColMinWidth() - dWidth;
			expandMergeCellWidth(pAtomicRange, dMoreColWidth);
		}
		if (dHeight < getImgCellRowMinHeight())//判断会存在遮挡情况
		{
			double dMoreRowHeight = getImgCellRowMinHeight() - dHeight;
			expandMergeCellHeight(pAtomicRange, dMoreRowHeight);
		}
	}

}

double KArrangeProcess::getImgCellColMinWidth()
{
	WholeEffectBase* pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
	if (!pWholeEffect)
		return 0;
	return pWholeEffect->getImgCellColMinWidth();
}

double KArrangeProcess::getImgCellRowMinHeight()
{
	WholeEffectBase* pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
	if (!pWholeEffect)
		return 0;
	return pWholeEffect->getImgCellRowMinHeight();
}

void KArrangeProcess::presetColWidth(int iCol, double dWidth)
{
	if (!m_spAtomicTable)
		return;
	if (m_spAtomicTable->m_colList.find(iCol) != m_spAtomicTable->m_colList.end())
	{
		AtomicCol* pEachAtomicCol = m_spAtomicTable->m_colList.at(iCol);
		pEachAtomicCol->setColResultWidth(dWidth);
	}
}

double KArrangeProcess::getPresetColWidth(int iCol)
{
	return m_spAtomicTable->getPresetColWidth(iCol);
}

void KArrangeProcess::syncColPresetWidth2RealWidth()
{
	if (!m_spAtomicTable)
		return;
	HRESULT hr = S_CONTINUE;
	int cnt = 0;
	for (int col = m_tableInfo.parentRangeInfo.allRangeInfo.colFrom; col <= m_tableInfo.parentRangeInfo.allRangeInfo.colTo; col++)
	{
		if (m_spAtomicTable->m_colList.find(col) != m_spAtomicTable->m_colList.end())
		{
			AtomicCol* pEachAtomicCol = m_spAtomicTable->m_colList.at(col);
			int iCol = pEachAtomicCol->getColIdx();

			ks_stdptr<etoldapi::Range> spColRange;
			if (m_pStop && !m_pStop->isContinue())
				return;

			hr = getSpecifiedRange(&spColRange, m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom, m_tableInfo.parentRangeInfo.allRangeInfo.rowTo, iCol, iCol);
			if (FAILED(hr) || !spColRange)
				continue;

			double dWidth = pEachAtomicCol->getColResultWidth();
			KComVariant colWidth;
			colWidth.AssignDouble(dWidth);
			spColRange->put_ColumnWidth(colWidth);
			//保留原始的隐藏设置
			if (pEachAtomicCol->isHidden())
			{
				restoreColHidden(iCol);
			}

		}
		if (cnt++ == CHECK_PROCESSEVENTS_FREQ)
		{
			cnt = 0;
			if (!checkProcessEvents(this))
				return;
			if (coreObjectIsDestroyed() || (m_pStop && !m_pStop->isContinue()))
				return;
		}

	}
}

void KArrangeProcess::presetRowHeight(int iRow, double dHeight)
{
	m_spAtomicTable->presetRowHeight(iRow, dHeight);
}

double KArrangeProcess::getPresetRowHeight(int iRow)
{
	return m_spAtomicTable->getPresetRowHeight(iRow);
}

void KArrangeProcess::syncRowPresetHeight2RealHeight()
{
	if (!m_spAtomicTable)
		return;
	HRESULT hr = S_CONTINUE;
	double dMaRowHeight = MAX_ROW_HEIGHT / 20;
	int cnt = 0;
	double currentHeight = 0;
	int currentRowFrom = m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom;
	QList<AtomicRow*> hiddenRowList;
	for (int iRow = m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom; iRow <= m_tableInfo.parentRangeInfo.allRangeInfo.rowTo; iRow++)
	{
		if (m_pStop && !m_pStop->isContinue())
			return;

		if (!m_spAtomicTable)
			return;

		if (m_spAtomicTable->m_rowList.find(iRow) != m_spAtomicTable->m_rowList.end())
		{
			AtomicRow* pEachAtomicRow = m_spAtomicTable->m_rowList.at(iRow);
			double dHeight = pEachAtomicRow->getRowResultHeight();
			if (pEachAtomicRow->isHidden())
				hiddenRowList.append(pEachAtomicRow);
			if (dHeight > dMaRowHeight)
				dHeight = dMaRowHeight;
			if (0 == currentHeight)
				currentHeight = dHeight;
			if (currentHeight == dHeight)
				continue;

			KComVariant rowHeight;
			rowHeight.AssignDouble(currentHeight);

			ks_stdptr<etoldapi::Range> spRowRange;
			hr = getSpecifiedRange(&spRowRange, currentRowFrom, iRow - 1, m_tableInfo.parentRangeInfo.allRangeInfo.colFrom, m_tableInfo.parentRangeInfo.allRangeInfo.colTo);
			if (FAILED(hr) || !spRowRange)
				continue;
			spRowRange->put_RowHeight(rowHeight);

			currentRowFrom = iRow;
			currentHeight = dHeight;
		}
		if (cnt++ == CHECK_PROCESSEVENTS_FREQ)
		{
			cnt = 0;
			if (!checkProcessEvents(this))
				return;
			if (coreObjectIsDestroyed())
				return;
		}

	}
	KComVariant rowHeight;
	rowHeight.AssignDouble(currentHeight);

	ks_stdptr<etoldapi::Range> spRowRange;
	hr = getSpecifiedRange(&spRowRange, currentRowFrom, m_tableInfo.parentRangeInfo.allRangeInfo.rowTo, m_tableInfo.parentRangeInfo.allRangeInfo.colFrom, m_tableInfo.parentRangeInfo.allRangeInfo.colTo);
	if (SUCCEEDED(hr) && spRowRange)
		spRowRange->put_RowHeight(rowHeight);
	ks_stdptr<IRowColOp> spRowColOp;
	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (pSheet)
		pSheet->GetOperator(&spRowColOp);

	if (spRowColOp)
	{
		spRowColOp->BeginRowColBatchUpdate();
		for (QList<AtomicRow*>::iterator it = hiddenRowList.begin(); it != hiddenRowList.end(); it++)
		{
			ROW row = (*it)->getRowIdx();
			spRowColOp->SetRowHidden(row, row, TRUE);
		}
		spRowColOp->EndRowColBatchUpdate();
	}
}

void KArrangeProcess::restoreRowHidden(int iRow)
{
	VARIANT varEmpty;
	V_VT(&varEmpty) = VT_ERROR;
	V_ERROR(&varEmpty) = DISP_E_PARAMNOTFOUND;
	ks_stdptr<etoldapi::Range> spRows;
	HRESULT hr = m_spAllRangeCell->get_Rows(&spRows);
	if (SUCCEEDED(hr) && spRows)
	{
		long rowIdx = iRow - m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom + 1;
		KComVariant varRow(rowIdx, VT_I4);
		KComVariant var;
		hr = spRows->get_Item(varRow, varEmpty, &var);
		if (FAILED(hr))
			return;
		ks_stdptr<etoldapi::Range> spRow = KSmartParam(var).GetInterfaceValue();
		if (!spRow)
			return;
		KComVariant varHidden;
		varHidden.AssignBOOL(TRUE);
		spRow->put_Hidden(varHidden);
	}
}

void KArrangeProcess::restoreColHidden(int iCol)
{
	VARIANT varEmpty;
	V_VT(&varEmpty) = VT_ERROR;
	V_ERROR(&varEmpty) = DISP_E_PARAMNOTFOUND;
	ks_stdptr<etoldapi::Range> spCols;
	HRESULT hr = m_spAllRangeCell->get_Columns(&spCols);

	if (SUCCEEDED(hr) && spCols)
	{
		long colIdx = iCol - m_tableInfo.parentRangeInfo.allRangeInfo.colFrom + 1;
		KComVariant varCol(colIdx, VT_I4);
		KComVariant var;
		hr = spCols->get_Item(varCol, varEmpty, &var);
		if (FAILED(hr))
			return;
		ks_stdptr<etoldapi::Range> spCol = KSmartParam(var).GetInterfaceValue();
		if (!spCol)
			return;
		KComVariant varHidden;
		varHidden.AssignBOOL(TRUE);
		spCol->put_Hidden(varHidden);
	}
}

void KArrangeProcess::applyAtomicTableProp()
{
	HRESULT hr = S_CONTINUE;
	if (!m_spAtomicTable)
		return;

	qint64 processStartTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	applyAtomicTableFontProp();
	qint64 fontEndTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle process: finish Font setting, total times: %1")
		.arg(fontEndTime - processStartTime).toStdWString());

	applyAtomicTableAlignProp();
	KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle process: finish Align setting, total times: %1")
		.arg(QDateTime::currentDateTime().toMSecsSinceEpoch() - fontEndTime).toStdWString());

	int cnt = 0;
	for (auto iter = m_spAtomicTable->m_rowList.begin(); iter != m_spAtomicTable->m_rowList.end(); iter++)
	{
		AtomicRow* pEachAtomicRow = iter->second;
		if (!pEachAtomicRow)
			continue;
		std::vector<AtomicCells*> vecCells = pEachAtomicRow->vecCells;
		int iRow = pEachAtomicRow->getRowIdx();
		double rowMaxHeight = 0;
		for (size_t i = 0; i < vecCells.size(); i++)
		{
			if (m_pStop && !m_pStop->isContinue())
				return;

			AtomicCells* pEachAtomicCells = vecCells.at(i);
			if (!pEachAtomicCells)
				continue;

			if (pEachAtomicCells->isNeedSpecialApplyProp())
			{
				ks_stdptr<etoldapi::Range> spRange;
				hr = getSpecifiedRange(&spRange, pEachAtomicCells->iTop, pEachAtomicCells->iTop, pEachAtomicCells->iLeft, pEachAtomicCells->iLeft);
				if (spRange)
				{
					//设置特殊的对齐
					ETHAlign hAlign = pEachAtomicCells->getHAlign();
					if (etHAlignCenter != hAlign)
						spRange->put_HorizontalAlignment(hAlign);
					ETVAlign vAlign = pEachAtomicCells->getVAlign();
					if (etVAlignCenter != vAlign)
						spRange->put_VerticalAlignment(vAlign);

					if (!pEachAtomicCells->getWrapText())
					{
						KComVariant wrapText;
						wrapText.AssignBOOL(false);
						spRange->put_WrapText(wrapText);
					}

					if (pEachAtomicCells->isModifiedText())
					{
						ks_wstring modifiedText = pEachAtomicCells->getModifiedText();
						ks_bstr newText(modifiedText.c_str());
						KComVariant newVal;
						newVal.AssignBSTR(newText);
						spRange->put_Value(etRangeValueDefault, newVal);
					}

				}
				if (cnt++ == CHECK_PROCESSEVENTS_FREQ)
				{
					cnt = 0;
					if (!checkProcessEvents(this))
						return;
					if (coreObjectIsDestroyed())
						return;
				}

			}
		}

	}
}

void KArrangeProcess::applyAtomicTableFontProp()
{
	auto setRangeXF = [](const ks_stdptr<etoldapi::Range>& spRange, const KXF& xf, const KXFMASK& msak) {
		if (!spRange)
			return false;

		ks_stdptr<IRangeInfo> spRgInfo;
		HRESULT hr = spRange->QueryInterface(IID_IRangeInfo, (void**)&spRgInfo);
		if (spRgInfo == nullptr)
			return false;

		ks_stdptr<IAppCoreRange> spCoreRg;
		hr = spRgInfo->GetAppCoreRange(&spCoreRg);
		if (spCoreRg == nullptr)
			return false;
		spCoreRg->SetXF(&msak, &xf);
		return true;
	};

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	//清除缩进格式、自动换行
	KXF indentWrapXF;
	indentWrapXF.cIndent = 0;
	indentWrapXF.fWrap = true;
	KXFMASK indentWrapMask(XFMASK::_cat_cIndent | XFMASK::_cat_fWrap);
	indentWrapMask.inc_cIndent = 1;
	indentWrapMask.inc_fWrap = 1;
	if (!setRangeXF(m_spAllRangeCell, indentWrapXF, indentWrapMask))
		return;

	//标题
	for (int i = 0; i < m_titleRangeCellVec.size(); i++)
	{
		ks_stdptr<etoldapi::Range> spTitleRangeCell = m_titleRangeCellVec[i];
		if (spTitleRangeCell)
		{
			KXF xf;
			ks_bstr fontName(krt::utf16(getFontName()));
			ks_wcsncpy_s(xf.font.name, fontName, countof(xf.font.name) - 1);
			xf.font.dyHeight = LengthValue::pt_to_twip(m_TitleRangeFontSize);
			xf.font.bls = TRUE;
			KXFMASK maskSet(0, XFMASK::_cat_theme_name | XFMASK::_cat_dyHeight | XFMASK::_cat_bls);
			if (!setRangeXF(spTitleRangeCell, xf, maskSet))
				continue;
		}
	}

	//表头
	for (int i = 0; i < m_headRangeCellVec.size(); i++)
	{
		ks_stdptr<etoldapi::Range> spHeadRangeCell = m_headRangeCellVec[i];
		if (spHeadRangeCell)
		{
			KXF xf;
			ks_bstr fontName(krt::utf16(getFontName()));
			ks_wcsncpy_s(xf.font.name, fontName, countof(xf.font.name) - 1);
			xf.font.dyHeight = LengthValue::pt_to_twip(m_HeadRangeFontSize);
			xf.font.bls = TRUE;
			KXFMASK maskSet(0, XFMASK::_cat_theme_name | XFMASK::_cat_dyHeight | XFMASK::_cat_bls);
			if (!setRangeXF(spHeadRangeCell, xf, maskSet))
				continue;
		}
	}

	//副标题
	for (int i = 0; i < m_subTitleRangeCellVec.size(); i++)
	{
		ks_stdptr<etoldapi::Range> spSubTitleRangeCell = m_subTitleRangeCellVec[i];
		if (spSubTitleRangeCell)
		{
			KXF xf;
			ks_bstr fontName(krt::utf16(getFontName()));
			ks_wcsncpy_s(xf.font.name, fontName, countof(xf.font.name) - 1);
			xf.font.dyHeight = LengthValue::pt_to_twip(m_SubTitleRangeFontSize);
			xf.font.bls = TRUE;
			KXFMASK maskSet(0, XFMASK::_cat_theme_name | XFMASK::_cat_dyHeight | XFMASK::_cat_bls);
			if (!setRangeXF(spSubTitleRangeCell, xf, maskSet))
				continue;
		}
	}

	//内容
	for (int i = 0; i < m_tableInfo.parentRangeInfo.contentRangeInfoVec.size(); i++)
	{
		int cnt = 0;
		RANGE contentRangeInfo(m_tableInfo.parentRangeInfo.contentRangeInfoVec[i], pSheet->GetBMP());
		int handleProcesseventsFreq = MAX_HANDLE_CELL_CNT / (contentRangeInfo.ColTo() - contentRangeInfo.ColFrom() + 1);
		int currentRowFrom = contentRangeInfo.RowFrom();
		for (int row = contentRangeInfo.RowFrom(); row <= contentRangeInfo.RowTo(); row++)
		{
			if (cnt++ < handleProcesseventsFreq && row != contentRangeInfo.RowTo())
			{
				continue;
			}

			cnt = 0;
			if (!checkProcessEvents(this))
				return;
			if (coreObjectIsDestroyed() || (m_pStop && !m_pStop->isContinue()))
				return;

			ks_stdptr<etoldapi::Range> spContentRange;
			HRESULT hr = getSpecifiedRange(&spContentRange, currentRowFrom, row == contentRangeInfo.RowTo() ? contentRangeInfo.RowTo() : row - 1, contentRangeInfo.ColFrom(), contentRangeInfo.ColTo());
			if (FAILED(hr) || !spContentRange)
				return;
			
			KXF xf;
			ks_bstr fontName(krt::utf16(getFontName()));
			ks_wcsncpy_s(xf.font.name, fontName, countof(xf.font.name) - 1);
			xf.font.dyHeight = LengthValue::pt_to_twip(m_ContentRangeFontSize);
			KXFMASK maskSet(0, XFMASK::_cat_theme_name | XFMASK::_cat_dyHeight);
			if (!setRangeXF(spContentRange, xf, maskSet))
				continue;

			currentRowFrom = row;
		}
	}

	//信息类型
	for (int i = 0; i < m_infoRangeCellVec.size(); i++)
	{
		ks_stdptr<etoldapi::Range> spInfoRangeCell = m_infoRangeCellVec[i];
		if (spInfoRangeCell)
		{
			KXF xf;
			ks_bstr fontName(krt::utf16(getFontName()));
			ks_wcsncpy_s(xf.font.name, fontName, countof(xf.font.name) - 1);
			xf.font.dyHeight = LengthValue::pt_to_twip(m_InfoRangeFontSize);
			KXFMASK maskSet(0, XFMASK::_cat_theme_name | XFMASK::_cat_dyHeight);
			if (!setRangeXF(spInfoRangeCell, xf, maskSet))
				continue;
		}
	}

}

void KArrangeProcess::applyAtomicTableAlignProp()
{
	if (m_spAllRangeCell)
	{
		m_spAllRangeCell->put_VerticalAlignment(etVAlignCenter);
		m_spAllRangeCell->put_HorizontalAlignment(etHAlignCenter);
	}
}

void KArrangeProcess::applyAtomicTableStyle()
{
	if (!m_pTableRangeStyle)
		return;

	updateSelectRange();
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle apply: finish updateSelectRange");
	applyFillAlterPlan();
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle apply: finish applyFillAlterPlan");
	applyFillFirstLastColArea();
	applyFillOtherArea();
	applyFillTblHeadArea();
	KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle apply: finish applyAtomicTableStyle");
}

void KArrangeProcess::applyFillAlterPlan()
{
	qint64 startTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	applyFillAllContent();
	qint64 endContentTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle apply: finish applyFillAllContent, total times: %1")
		.arg(endContentTime - startTime).toStdWString());

	applyFillColAlterPlan();
	qint64 endColTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle apply: finish applyFillColAlterPlan, total times: %1")
		.arg(endColTime - endContentTime).toStdWString());

	applyFillRowAlterPlan();
	qint64 endRowTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle apply: finish applyFillRowAlterPlan, total times: %1")
		.arg(endRowTime - endColTime).toStdWString());

	applyFillAllContentBorder();
	qint64 endBorderTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle apply: finish applyFillAllContentBorder, total times: %1")
		.arg(endBorderTime - endRowTime).toStdWString());
}

void KArrangeProcess::applyFillTblHeadArea()
{
	if (coreObjectIsDestroyed())
		return;

	if (!alg::IsBitUsed(m_pTableRangeStyle->getStyleOptino(), TSO_HEADER_ROW))
		return;

	HRESULT hr = E_FAIL;
	const io_utils::DXF* pStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleRowTitle);
	if (!pStyle)
		return;

	//表头
	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	for (const auto& subTable : m_tableInfo.childRangeList())
	{
		for (const auto& headerCube :subTable.headRangeInfoVec)
		{
			RANGE headRangeInfo(headerCube, pSheet->GetBMP());
			applyRangeStyle(headRangeInfo, pStyle, true);
		}
	}
}

void KArrangeProcess::applyFillFirstLastColArea()
{
	if (coreObjectIsDestroyed())
		return;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	DWORD styleOption = m_pTableRangeStyle->getStyleOptino();
	for (auto& childArea : m_tableInfo.childRangeList())
	{
		int firstColIdx = childArea.getFirstColIdx();
		int lastColIdx = childArea.getLastColIdx();
		const io_utils::DXF* pFirstStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleFirstCol);
		if (alg::IsBitUsed(styleOption, TSO_FIRST_COL) && pFirstStyle != nullptr)
		{
			ES_CUBE fillAlterArea = childArea.getFillAlterArea();
			RANGE rg(fillAlterArea, pSheet->GetBMP());
			rg.SetColFromTo(firstColIdx);
			applyRangeStyle(rg, pFirstStyle);
		}

		if (lastColIdx > firstColIdx)
		{
			const io_utils::DXF* pLastStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleLastCol);
			if (alg::IsBitUsed(styleOption, TSO_LAST_COL) && pLastStyle != nullptr)
			{
				ES_CUBE fillAlterArea = childArea.getFillAlterArea();
				RANGE rg(fillAlterArea, pSheet->GetBMP());
				rg.SetColFromTo(lastColIdx);
				applyRangeStyle(rg, pLastStyle);
			}
		}
	}
}

void KArrangeProcess::applyFillOtherArea()
{
	if (coreObjectIsDestroyed())
		return;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	KXFMASK mask(XFMASK::_catAll, XFMASK::_catFontAll);
	mask._cats = (XFMASK::_category)(mask._cats ^ XFMASK::_catBorder);
	//标题
	const io_utils::DXF* pTitleStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleBigTitle);
	if (pTitleStyle != nullptr)
	{
		for (int i = 0; i < m_titleRangeCellVec.size(); i++)
		{
			RANGE rg(pSheet->GetBMP());
			IdentifyTool::GetTableRange(m_titleRangeCellVec[i], &rg);
			applyRangeStyle(rg, pTitleStyle, false, &mask);
		}
	}
		
	//信息类型
	const io_utils::DXF* pStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleInfo);
	if (pStyle != nullptr)
	{
		for (int i = 0; i < m_infoRangeCellVec.size(); i++)
		{
			RANGE rg(pSheet->GetBMP());
			IdentifyTool::GetTableRange(m_infoRangeCellVec[i], &rg);
			applyRangeStyle(rg, pStyle, false, &mask);
		}
	}
}

void KArrangeProcess::applyFillAllContent()
{
	if (coreObjectIsDestroyed())
		return;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	const io_utils::DXF* pStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleAllContent);
	if (!pStyle)
		return;

	for (auto& childArea : m_tableInfo.childRangeList())
	{
		ES_CUBE fillAlterArea = childArea.getFillAlterArea();
		RANGE rg(fillAlterArea, pSheet->GetBMP());
		applyRangeStyle(rg, pStyle);
	}
}

void KArrangeProcess::applyFillRowAlterPlan()
{
	if (coreObjectIsDestroyed() || !m_spAtomicTable)
		return;

	if (!alg::IsBitUsed(m_pTableRangeStyle->getStyleOptino(), TSO_BANDED_ROWS))
		return;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	const io_utils::DXF* pOddRowAlterStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleOddRowAlter);
	const io_utils::DXF* pEvenRowAlterStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleEvenRowAlter);
	if (!pOddRowAlterStyle && !pEvenRowAlterStyle)
		return;

	int count = 0;
	for (auto& childArea : m_tableInfo.childRangeList())
	{
		ES_CUBE fillAlterArea = childArea.getFillRowAlterArea();
		bool bOdd = true;
		int firstColIdx = m_spAtomicTable->getFirstMergeColIdx(childArea);
		for (int iRow = fillAlterArea.rowFrom; iRow <= fillAlterArea.rowTo; iRow++)
		{
			if (m_pStop && !m_pStop->isContinue())
				return;

			if (count == 127)
			{
				if (!checkProcessEvents(this))
					return;
				count = 0;
				if (coreObjectIsDestroyed())
					return;
			}
			count++;

			const io_utils::DXF* pDXF = bOdd ? pOddRowAlterStyle : pEvenRowAlterStyle;
			RANGE rg(fillAlterArea, pSheet->GetBMP());
			//Todo:这样取合并单元格可能会比较慢，因为需要遍历，当这个表的合并单元格数目大的时候，后续看看能不能直接取
			AtomicCells* pCell = m_spAtomicTable->getMergeCell(iRow, firstColIdx);
			if (pCell)//合并单元格
			{
				rg.SetRowFromTo(pCell->iTop, pCell->iBottom);
				iRow = pCell->iBottom;//跳过合并单元格所占的行
			}
			else//普通单元格
			{
				rg.SetRowFromTo(iRow);
			}
			applyRangeStyle(rg, pDXF);
			bOdd = !bOdd;
		}
	}
}

void KArrangeProcess::applyFillColAlterPlan()
{
	if (coreObjectIsDestroyed() || !m_spAtomicTable)
		return;

	if (!alg::IsBitUsed(m_pTableRangeStyle->getStyleOptino(), TSO_BANDED_COLS))
		return;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	const io_utils::DXF* pOddColStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleOddColAlter);
	const io_utils::DXF* pEvenColStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleEvenColAlter);
	if (!pOddColStyle && !pEvenColStyle)
		return;

	for (auto& childArea : m_tableInfo.childRangeList())
	{
		ES_CUBE fillAlterArea = childArea.getFillRowAlterArea();
		bool bOdd = true;
		int idxRow = m_spAtomicTable->getFirstHeaderRowIdx(childArea);
		for (int iCol = fillAlterArea.colFrom; iCol <= fillAlterArea.colTo; iCol++)
		{
			if (m_pStop && !m_pStop->isContinue())
				return;

			const io_utils::DXF* pDXF = bOdd ? pOddColStyle : pEvenColStyle;
			RANGE rg(fillAlterArea, pSheet->GetBMP());
			AtomicCells* pCell = nullptr;
			if (idxRow >= 0)
				pCell = m_spAtomicTable->getMergeCell(idxRow, iCol);
			if (pCell)//合并单元格
			{
				rg.SetColFromTo(pCell->iLeft, pCell->iRight);
				iCol = pCell->iRight;//跳过合并单元格所占的列
			}
			else//普通单元格
			{
				rg.SetColFromTo(iCol);
			}
			applyRangeStyle(rg, pDXF);
			bOdd = !bOdd;
		}
	}	
}

void KArrangeProcess::applyFillAllContentBorder()
{
	if (coreObjectIsDestroyed())
		return;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	const io_utils::DXF* pStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleAllContent);
	if (!pStyle)
		return;

	for (auto& childArea : m_tableInfo.childRangeList())
	{
		ES_CUBE fillAlterArea = childArea.getFillRowAlterArea();
		RANGE rg(fillAlterArea, pSheet->GetBMP());
		KXFMASK mask(XFMASK::_catBorder);
		applyRangeStyle(rg, pStyle, false, &mask);
	}
}

void KArrangeProcess::applyRangeStyle(const RANGE& rg, const io_utils::DXF* pDXF, bool bClear /* = false */, const KXFMASK* pMask /* = nullptr */)
{
	if (pDXF == nullptr)
		return;

	IBookOp* pBookOp = m_spWorkbook->GetBook()->LeakOperator();
	if (pBookOp == nullptr)
		return;

	if (!rg.IsValid())
		return;
	
	std::vector_s<RANGE> validRanges;
	if (m_ignoreSelectRange)
	{
		validRanges.push_back(rg);
	}
	else  //单个样式应用时，应用范围为：当前选中位置与可应用位置交集
	{
		for(auto it = m_dataSelectRanges.cbegin(); it != m_dataSelectRanges.cend(); it++)
		{
			RANGE tmpRange = it->Intersect(rg);
			if (tmpRange.IsValid())
				validRanges.push_back(tmpRange);
		}
	}

	std::vector_s<RANGE> rgl;
	getNoDataTableRangeList(rgl, validRanges);
	for (size_t i = 0; i < rgl.size(); ++i)
	{
		ks_stdptr<etoldapi::Range> spRange;
		getSpecifiedRange(&spRange, rgl[i]);
		if (spRange == nullptr)
			continue;

		ks_stdptr<IRangeInfo> spRgInfo;
		HRESULT hr = spRange->QueryInterface(IID_IRangeInfo, (void**)&spRgInfo);
		if (spRgInfo == nullptr)
			continue;

		ks_stdptr<IAppCoreRange> spCoreRg;
		hr = spRgInfo->GetAppCoreRange(&spCoreRg);
		if (spCoreRg == nullptr)
			continue;

		if (bClear)
		{
			KXFMASK maskClean(XFMASK::_catBorder | XFMASK::_catFills, XFMASK::_cat_bls | XFMASK::_cat_fItalic | XFMASK::_cat_fStrikeout | XFMASK::_cat_uls | XFMASK::_cat_clr);
			CrossKXFMask(maskClean, pMask);
			spCoreRg->SetXF(&maskClean, nullptr);
		}

		KXFMASK mask(pDXF->getmask());
		CrossKXFMask(mask, pMask);
		spCoreRg->SetXF(&mask, &pDXF->getxf());

		// 内部边框
		if (pMask == nullptr && (pDXF->inc_clrHorz || pDXF->inc_clrVert || pDXF->inc_dgHorz || pDXF->inc_dgVert))
		{
			KXFMASK maskBorderInside;
			KXF xfBorderInside;
			if (pDXF->inc_clrHorz)
			{
				maskBorderInside.inc_clrBottom = 1;
				xfBorderInside.clrBottom = pDXF->clrHorz;
			}
			if (pDXF->inc_clrVert)
			{
				maskBorderInside.inc_clrRight = 1;
				xfBorderInside.clrRight = pDXF->clrVert;
			}
			if (pDXF->inc_dgHorz)
			{
				maskBorderInside.inc_dgBottom = 1;
				xfBorderInside.dgBottom = pDXF->dgHorz;
			}
			if (pDXF->inc_dgVert)
			{
				maskBorderInside.inc_dgRight = 1;
				xfBorderInside.dgRight = pDXF->dgVert;
			}
			spCoreRg->SetBorderInsideXF(&maskBorderInside, &xfBorderInside);
		}
	}
}

void KArrangeProcess::clearTableStyle()
{
	if (coreObjectIsDestroyed())
		return;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	ES_CUBE fillAlterArea = m_tableInfo.getFillAlterArea();
	RANGE rg(fillAlterArea, pSheet->GetBMP());
	clearRangeStyle(rg);

	const io_utils::DXF* pTitleStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleBigTitle);
	if (pTitleStyle != nullptr)
	{
		for (int i = 0; i < m_titleRangeCellVec.size(); i++)
		{
			RANGE rg(pSheet->GetBMP());
			IdentifyTool::GetTableRange(m_titleRangeCellVec[i], &rg);
			clearRangeStyle(rg);
		}
	}

	const io_utils::DXF* pInfoStyle = m_pTableRangeStyle->getTableRangeStyle(kaietrecognize::StyleInfo);
	if (pInfoStyle != nullptr)
	{
		for (int i = 0; i < m_infoRangeCellVec.size(); i++)
		{
			RANGE rg(pSheet->GetBMP());
			IdentifyTool::GetTableRange(m_infoRangeCellVec[i], &rg);
			clearRangeStyle(rg);
		}
	}
}

void KArrangeProcess::clearRangeStyle(const RANGE& rg, const KXFMASK* pMask /* = nullptr */)
{
	IBookOp* pBookOp = m_spWorkbook->GetBook()->LeakOperator();
	if (pBookOp == nullptr)
		return;

	std::vector_s<RANGE> ranges;
	ranges.push_back(rg);
	std::vector_s<RANGE> rgl;
	getNoDataTableRangeList(rgl, ranges);
	for (size_t i = 0; i < rgl.size(); ++i)
	{
		ks_stdptr<etoldapi::Range> spRange;
		getSpecifiedRange(&spRange, rgl[i]);
		if (spRange == nullptr)
			continue;

		ks_stdptr<IRangeInfo> spRgInfo;
		HRESULT hr = spRange->QueryInterface(IID_IRangeInfo, (void**)&spRgInfo);
		if (spRgInfo == nullptr)
			continue;

		ks_stdptr<IAppCoreRange> spCoreRg;
		hr = spRgInfo->GetAppCoreRange(&spCoreRg);
		if (spCoreRg == nullptr)
			continue;

		KXFMASK maskClean(XFMASK::_catBorder | XFMASK::_catFills, XFMASK::_cat_bls | XFMASK::_cat_fItalic | XFMASK::_cat_fStrikeout | XFMASK::_cat_uls | XFMASK::_cat_clr);
		CrossKXFMask(maskClean, pMask);
		spCoreRg->SetXF(&maskClean, nullptr);
	}
}

HRESULT KArrangeProcess::initDataTableRanges()
{
	m_dataTableRanges.clear();
	m_dataTableStyleOpts.clear();

	if (!m_spWorkSheet || m_spWorkSheet->IsDestroyed())
		return E_FAIL;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return E_FAIL;

	// 数据表
	ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_spWorkSheet;
	ks_stdptr<etoldapi::ListObjects> spListObjects;
	HRESULT hr = spWorksheet->get_ListObjects(&spListObjects);
	if (SUCCEEDED(hr) && spListObjects != nullptr)
	{
		long nCnt = 0;
		spListObjects->get_Count(&nCnt);
		for (long i = 1; i <= nCnt; ++i)
		{
			ks_stdptr<etoldapi::ListObject> spListObject;
			KComVariant varI(i, VT_I4);
			hr = spListObjects->get_Item(varI, &spListObject);
			if (FAILED(hr) || !spListObject)
				continue;
			RANGE rg(pSheet->GetBMP());
			hr = spListObject->get_Range(&rg);
			if (FAILED(hr))
				continue;
			m_dataTableRanges.push_back(rg);
			m_dataTableStyleOpts << spListObject->GetStyleOptions();
		}
	}

	// 数据透视表
	ks_stdptr<pivot_core::IPivotTables> spPivotTables;
	pivot_core::GetPivotTablesFromSheet(pSheet, &spPivotTables);
	if (spPivotTables != nullptr && spPivotTables->Count() > 0)
	{
		IDX iSheet = 0;
		pSheet->GetIndex(&iSheet);
		for (size_t i = 0; i < spPivotTables->Count(); ++i)
		{
			ks_stdptr<pivot_core::IPivotTable> spPivotTable = spPivotTables->Item(i);
			if (!spPivotTable)
				continue;
			RECT rc = { 0 };
			spPivotTable->GetBodyRect(&rc);
			if (rc.left >= 0 && rc.right >= 0 && rc.bottom >= rc.top && rc.right >= rc.left)
			{
				m_dataTableRanges.push_back(Rect2Range(rc, iSheet, pSheet->GetBMP()));
				m_dataTableStyleOpts << INV_TABLESTYLE_OPTS;
			}
			UINT nPageCnt = spPivotTable->GetPageRectCount();
			if (nPageCnt > 0)
			{
				RECT* arrPageRect = new RECT[nPageCnt];
				if (arrPageRect)
				{
					nPageCnt = spPivotTable->GetPageRect(arrPageRect, nPageCnt);
					for (UINT j = 0; j < nPageCnt; ++j)
					{
						const RECT& rcPage = arrPageRect[j];
						if (rcPage.left >= 0 && rcPage.right >= 0 && rcPage.bottom >= rcPage.top && rcPage.right >= rcPage.left)
						{
							m_dataTableRanges.push_back(Rect2Range(rcPage, iSheet, pSheet->GetBMP()));
							m_dataTableStyleOpts << INV_TABLESTYLE_OPTS;
						}
					}
					delete[] arrPageRect;
					arrPageRect = nullptr;
				}
			}
		}
	}

	return S_OK;
}

double KArrangeProcess::getColMaxWidthWithChar()
{
	WholeEffectBase* pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
	if (!pWholeEffect)
		return 0;
	return pWholeEffect->getColMaxWidthWithChar();
}

HRESULT KArrangeProcess::getSpecifiedRange(OUT etoldapi::Range** ppRange, IN int rowBegin, IN int rowEnd, IN int colBegin, IN int colEnd)
{
	if (!m_spWorkSheet || rowBegin > rowEnd || colBegin > colEnd)//容错处理
		return E_FAIL;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return E_FAIL;

	RANGE rg(pSheet->GetBMP());
	rg.SetRowFromTo(rowBegin, rowEnd);
	rg.SetColFromTo(colBegin, colEnd);

	ks_stdptr<etoldapi::Range> spRange;
	m_spWorkSheet->GetRangeByData(&rg, &spRange);

	ks_stdptr<etoldapi::Range> spRangeCell;
	spRange->get_Cells(&spRangeCell);
	if (!spRangeCell)
		return E_FAIL;

	*ppRange = spRangeCell.detach();

	return S_OK;
}

HRESULT KArrangeProcess::getSpecifiedRange(OUT etoldapi::Range** ppRange, IN const RANGE& rg)
{
	return getSpecifiedRange(ppRange, rg.RowFrom(), rg.RowTo(), rg.ColFrom(), rg.ColTo());
}

HRESULT KArrangeProcess::getNoDataTableRangeList(OUT std::vector_s<RANGE>& rgl, const std::vector_s<RANGE>& ranges)
{
	if (!m_spWorkSheet || ranges.empty())
		return E_FAIL;

	rgl.clear();
	rgl.insert(rgl.end(), ranges.begin(), ranges.end());
	DWORD resStyleOpt = m_pTableRangeStyle->getStyleOptino();
	for (size_t i = 0; i < m_dataTableRanges.size(); ++i)
	{
		std::vector_s<RANGE> vecSubRgs;
		RANGE tableRg = m_dataTableRanges[i];
		UINT tableStyleOpt = m_dataTableStyleOpts[i];
		if (m_bNeedResetDataTable && m_pTableRangeStyle && tableStyleOpt != INV_TABLESTYLE_OPTS)
		{
			BMP_PTR bmp = tableRg.GetBMP();
			if (!bmp)
				continue;
			if (alg::IsBitUsed(tableStyleOpt, TSO_HEADER_ROW) != alg::IsBitUsed(resStyleOpt, TSO_HEADER_ROW))
			{
				ROW tableRowFrom = tableRg.RowFrom() + (alg::IsBitUsed(tableStyleOpt, TSO_HEADER_ROW) ? 1 : -1);
				if (tableRowFrom >= 0)
					tableRg.SetRowFrom(tableRowFrom);
				else if (tableRg.RowTo() + 1 < bmp->cntRows)
					tableRg.SetRowTo(tableRg.RowTo() + 1);
			}
			if (alg::IsBitUsed(tableStyleOpt, TSO_TOTAL_ROW) != alg::IsBitUsed(resStyleOpt, TSO_TOTAL_ROW))
			{
				ROW tableRowTo = tableRg.RowTo() + (alg::IsBitUsed(tableStyleOpt, TSO_TOTAL_ROW) ? -1 : 1);
				if(tableRowTo < bmp->cntRows)
					tableRg.SetRowTo(tableRowTo);
			}
		}
		for (size_t j = 0; j < rgl.size(); ++j)
		{
			if (!rgl[j].GetSubtraction(tableRg, vecSubRgs))
				vecSubRgs.push_back(rgl[j]);
		}
		rgl = vecSubRgs;
	}
	return S_OK;
}

RANGE KArrangeProcess::Rect2Range(const RECT& rect, IDX idxSht, BMP_PTR pBMP)
{
	RANGE rg(pBMP);
	rg.SetSheetFromTo(idxSht);
	rg.SetRowFromTo(rect.top, rect.bottom);
	rg.SetColFromTo(rect.left, rect.right);
	return rg;
}

void KArrangeProcess::callApiTest()
{
	//这个方法是把一些后面可能会用到的api给记录起来，后面接入了具体业务要把这个方法给干掉
	if (!m_spAllRangeCell)
		return;

	ks_stdptr<etoldapi::Range> spRangeCell;
	m_spAllRangeCell->get_Cells(&spRangeCell);
	if (!spRangeCell)
		return;

	long cnt = 0;
	spRangeCell->get_Count(&cnt);

	long colCnt = 0;
	long rowCnt = 0;
	spRangeCell->get_Column(&colCnt);
	spRangeCell->get_Row(&rowCnt);

	//获取字体属性
	ks_stdptr<etoldapi::Font> spFont;
	spRangeCell->get_Font(&spFont);
	if (spFont)
	{
		KComVariant fontSize;
		spFont->get_Size(&fontSize);

		VARIANT bBold;
		spFont->get_Bold(&bBold);//V_VT(&Value) = VT_BOOL; //V_VT(&bBold) = VT_BOOL;

		if (V_BOOL(&bBold) == VARIANT_TRUE)
		{
			if (V_VT(&fontSize) == VT_R8)
			{
				double size = V_R8(&fontSize);
				size += 5;
				fontSize.AssignDouble(size);
			}
		}
		else
		{
			if (V_VT(&fontSize) == VT_R8)
			{
				double size = V_R8(&fontSize);
				size -= 5;
				fontSize.AssignDouble(size);
			}
		}
		
		V_BOOL(&bBold) = !V_BOOL(&bBold);

		spFont->put_Bold(bBold);
		spFont->put_Size(fontSize);
		long fontColor = 0;
		spFont->get_Color(&fontColor);
		fontColor = 0;
		spFont->put_Color(fontColor);
	}

	//边框相关设置
	ks_stdptr<etoldapi::Borders> spBorder;
	spRangeCell->get_Borders(&spBorder);
	KComVariant borderLineStyle;
	spBorder->get_LineStyle(&borderLineStyle);
	spBorder->put_LineStyle(etDouble);
	spBorder->put_Weight(etHairline);
	long boderColor = 0;
	spBorder->get_Color(&boderColor);

	EtColor borderEtColor(ectNONE);
	spBorder->get_EtColor(&borderEtColor);
	DWORD rgbWord = borderEtColor.getARGB();

	//获取相关样式
	//获取背景颜色
	KComVariant fontStyle;
	spFont->get_FontStyle(&fontStyle);

	KComVariant prop;
	spRangeCell->get_Style(&prop);
	ks_stdptr<etoldapi::Style> tmp(V_DISPATCH(&prop));

	//缩进
	int indentLevel = 0;
	spRangeCell->get_IndentLevel(&indentLevel);

	if (1)
	{
		//设置自动换行
		KComVariant wrapText;
		spRangeCell->get_WrapText(&wrapText);
		wrapText.AssignBOOL(TRUE);
		spRangeCell->put_WrapText(wrapText);
	}

	else//适应调整
	{
		KCOMPTR(etoldapi::Range) pRowRange;
		KCOMPTR(etoldapi::Range) pColRange;
		spRangeCell->get_EntireRow(&pRowRange);
		spRangeCell->get_EntireColumn(&pColRange);
		//最适列宽调整
		spRangeCell->AutoFitCol();
		//最适行高调整
		KCOMPTR(IRangeInfo) ptrRowRangeInfo;
		pRowRange->QI(IRangeInfo, &ptrRowRangeInfo);
		ptrRowRangeInfo->InnerAutoFit(TRUE);
	}

}

HRESULT KArrangeProcess::initRecData()
{
	clearRangeCellVec();

	HRESULT hr = E_FAIL;
	if (m_tableInfo.isEmptyTableInfo())
		return E_FAIL;

	m_spWorkbook = m_tableInfo.getWorkBook();
	if (!m_spWorkbook)
		return E_FAIL;

	if (m_spWorkSheet && !m_spWorkSheet->IsDestroyed())
		m_spWorkSheet->UnRegisterNotifyFilter(this);
	m_spWorkSheet = m_tableInfo.getWorkSheet();
	if (!m_spWorkSheet)
		return E_FAIL;
	m_spWorkSheet->RegisterNotifyFilter(this);

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return E_FAIL;

	//处理的整个范围
	RANGE allRangeInfo(m_tableInfo.parentRangeInfo.allRangeInfo, pSheet->GetBMP());
	ks_stdptr<etoldapi::Range> spAllRange;
	m_spWorkSheet->GetRangeByData(&allRangeInfo, &spAllRange);
	if (!spAllRange)
		return E_FAIL;
	hr = spAllRange->get_Cells(&m_spAllRangeCell);
	if (!m_spAllRangeCell)
		return E_FAIL;

	//标题范围
	for (int i = 0; i < m_tableInfo.parentRangeInfo.titleRangeInfoVec.size(); i++)
	{
		RANGE titleRangeInfo(m_tableInfo.parentRangeInfo.titleRangeInfoVec[i], pSheet->GetBMP());
		ks_stdptr<etoldapi::Range> spTitleRange;
		m_spWorkSheet->GetRangeByData(&titleRangeInfo, &spTitleRange);
		if (!spTitleRange)
			return E_FAIL;
		ks_stdptr<etoldapi::Range> spRangeCell;
		hr = spTitleRange->get_Cells(&spRangeCell);
		if (!spRangeCell)
			return E_FAIL;
		m_titleRangeCellVec.push_back(spRangeCell.detach());
	}

	//表头(又称行标题)范围
	for (int i = 0; i < m_tableInfo.parentRangeInfo.headRangeInfoVec.size(); i++)
	{
		RANGE headRangeInfo(m_tableInfo.parentRangeInfo.headRangeInfoVec[i], pSheet->GetBMP());
		ks_stdptr<etoldapi::Range> spHeadRange;
		m_spWorkSheet->GetRangeByData(&headRangeInfo, &spHeadRange);
		if (!spHeadRange)
			return E_FAIL;
		ks_stdptr<etoldapi::Range> spRangeCell;
		hr = spHeadRange->get_Cells(&spRangeCell);
		if (!spRangeCell)
			return E_FAIL;
		m_headRangeCellVec.push_back(spRangeCell.detach());
	}

	//表内容范围
	for (int i = 0; i < m_tableInfo.parentRangeInfo.contentRangeInfoVec.size(); i++)
	{
		RANGE contentRangeInfo(m_tableInfo.parentRangeInfo.contentRangeInfoVec[i], pSheet->GetBMP());
		ks_stdptr<etoldapi::Range> spContentRange;
		m_spWorkSheet->GetRangeByData(&contentRangeInfo, &spContentRange);
		if (!spContentRange)
			return E_FAIL;
		ks_stdptr<etoldapi::Range> spRangeCell;
		hr = spContentRange->get_Cells(&spRangeCell);
		if (!spRangeCell)
			return E_FAIL;
		m_contentRangeCellVec.push_back(spRangeCell.detach());
	}


	//副标题范围
	for (int i = 0; i < m_tableInfo.parentRangeInfo.subTitleRangeInfoVec.size(); i++)
	{
		RANGE subTitleRangeInfo(m_tableInfo.parentRangeInfo.subTitleRangeInfoVec[i], pSheet->GetBMP());
		ks_stdptr<etoldapi::Range> spSubTitleRange;
		m_spWorkSheet->GetRangeByData(&subTitleRangeInfo, &spSubTitleRange);
		if (!spSubTitleRange)
			return E_FAIL;
		ks_stdptr<etoldapi::Range> spRangeCell;
		hr = spSubTitleRange->get_Cells(&spRangeCell);
		if (!spRangeCell)
			return E_FAIL;
		m_subTitleRangeCellVec.push_back(spRangeCell.detach());
	}

	//其他内容 例如备注之类的
	for (int i = 0; i < m_tableInfo.parentRangeInfo.otherRangeInfoVec.size(); i++)
	{
		RANGE otherRangeInfo(m_tableInfo.parentRangeInfo.otherRangeInfoVec[i], pSheet->GetBMP());
		ks_stdptr<etoldapi::Range> spOtherRange;
		m_spWorkSheet->GetRangeByData(&otherRangeInfo, &spOtherRange);
		if (!spOtherRange)
			return E_FAIL;
		ks_stdptr<etoldapi::Range> spRangeCell;
		hr = spOtherRange->get_Cells(&spRangeCell);
		if (!spRangeCell)
			return E_FAIL;
		m_otherRangeCellVec.push_back(spRangeCell.detach());
	}
	//信息类型
	for (int i = 0; i < m_tableInfo.parentRangeInfo.infoRangeInfoVec.size(); i++)
	{
		RANGE infoRangeInfo(m_tableInfo.parentRangeInfo.infoRangeInfoVec[i], pSheet->GetBMP());
		ks_stdptr<etoldapi::Range> spInfoRange;
		m_spWorkSheet->GetRangeByData(&infoRangeInfo, &spInfoRange);
		if (!spInfoRange)
			return E_FAIL;
		ks_stdptr<etoldapi::Range> spRangeCell;
		hr = spInfoRange->get_Cells(&spRangeCell);
		if (!spRangeCell)
			return E_FAIL;
		m_infoRangeCellVec.push_back(spRangeCell.detach());
	}
	return S_OK;
}

HRESULT KArrangeProcess::initAtomicTable()
{
	if (!m_spAllRangeCell || !m_spWorkSheet)
		return E_FAIL;
	m_spAtomicTable = new AtomicTable(m_tableInfo.parentRangeInfo.allRangeInfo, m_spWorkSheet);
	if (!m_spAtomicTable)
		return E_FAIL;
	return m_spAtomicTable->BuildTable(m_spAllRangeCell, m_tableInfo.parentRangeInfo, m_pTableApplyParam, m_pStop);//暂时
}

HRESULT KArrangeProcess::getStyleData()
{
	return S_OK;
}

HRESULT KArrangeProcess::standardProcess()
{
	HRESULT hr = structStandardProcess();
	return hr;
}

HRESULT KArrangeProcess::structStandardProcess()
{
	HRESULT hr = E_FAIL;

	//Todo:后续的合并也要转成描述信息，而不在这里直接设
	hr = mergeAreaProcess();
	//在处理完需要合并的区域之后再收集合并单元格的信息
	hr = initAtomicTable();

	return hr;
}

HRESULT KArrangeProcess::structProcess()
{
	HRESULT hr = E_FAIL;

	hr = processRowHAndColW();
	//处理合并单元格
	processMergeCells();

	//设置完最适合行高之后 上下进行留白处理
	hr = adjustRowHeightWithSpace();

	//处理内嵌的图片单元格所在的行高与列宽
	processCellImgFmla();

	return hr;
}

HRESULT KArrangeProcess::processRowHAndColW()
{
	HRESULT hr = E_FAIL;
	if (!m_spAllRangeCell)
		return E_FAIL;

	//调整列宽到合适的值
	adjustColumnsWidth();

	//调整行高到合适的值
	hr = adjustRowsHeight();
	return hr;
}

void KArrangeProcess::doAdaptProcess()
{
	if (!m_pAdaptScreenProxy)
		m_pAdaptScreenProxy = new adaptScreenPrcessProxy(m_spAtomicTable, m_viewSize);
	else
		m_pAdaptScreenProxy->init(m_spAtomicTable, m_viewSize);
	//运用“代理模式”，外层直接通过这个代理类去调用适应屏幕调整，而不用去区分要调用哪一种“策略”
	m_pAdaptScreenProxy->doAdaptScreen();

}

IKEtView* getWorksheetView(IKWorksheet* pWorkSheet)
{
	if (!pWorkSheet)
		return nullptr;

	IKWorksheetView* pSheetView = pWorkSheet->GetActiveWorksheetView();
	ks_stdptr<IKEtView> cpView;
	if (pSheetView)
		cpView = pWorkSheet->GetActiveWorksheetView()->GetWindow()->GetActiveView();

	return cpView.get();
}

void KArrangeProcess::initViewSize()
{
	QDesktopWidget* pdw = QApplication::desktop();
	int wViewWithPx = 1843, hViewWithPx = 789;//单位：像素 注意单位
	if (!m_spWorkSheet)
		return;

	IKEtView* pView = getWorksheetView(m_spWorkSheet);
	if (pView)
		pView->GetSize(&wViewWithPx, &hViewWithPx);

	//视图大小 宽度单位为字符数，高度单位为磅
	double dpiX = pdw->logicalDpiX();
	wViewWithPx = wViewWithPx / (dpiX / 72);
	float wView = KArrangeProcessHelper::viewWidth2CharsWidth(wViewWithPx, m_spWorkbook, false);

	double dpiY = pdw->logicalDpiY();
	double hView = hViewWithPx / (dpiY / 72);//单位:磅，转换公式：像素长度 = （dpi / 72）* 磅数

	wView -= 15;
	hView -= 25;
	m_viewSize.setW(wView);
	m_viewSize.setH(hView);
}

void KArrangeProcess::clearRangeCellVec()
{
	for (int i = 0, n = m_titleRangeCellVec.size(); i < n; i++)
		if (m_titleRangeCellVec[i])
			m_titleRangeCellVec[i]->Release();
	m_titleRangeCellVec.clear();

	for (int i = 0, n = m_headRangeCellVec.size(); i < n; i++)
		if (m_headRangeCellVec[i])
			m_headRangeCellVec[i]->Release();
	m_headRangeCellVec.clear();

	for (int i = 0, n = m_contentRangeCellVec.size(); i < n; i++)
		if (m_contentRangeCellVec[i])
			m_contentRangeCellVec[i]->Release();
	m_contentRangeCellVec.clear();

	for (int i = 0, n = m_subTitleRangeCellVec.size(); i < n; i++)
		if (m_subTitleRangeCellVec[i])
			m_subTitleRangeCellVec[i]->Release();
	m_subTitleRangeCellVec.clear();

	for (int i = 0, n = m_otherRangeCellVec.size(); i < n; i++)
		if (m_otherRangeCellVec[i])
			m_otherRangeCellVec[i]->Release();
	m_otherRangeCellVec.clear();

	for (int i = 0, n = m_infoRangeCellVec.size(); i < n; i++)
		if (m_infoRangeCellVec[i])
			m_infoRangeCellVec[i]->Release();
	m_infoRangeCellVec.clear();

	if (m_spAllRangeCell)
		m_spAllRangeCell.clear();
}

void KArrangeProcess::updateSelectRange()
{
	m_dataSelectRanges.clear();
	if (m_ignoreSelectRange || !isKFpCcombEnableAreaApply() || !isEnableAreaApply())
	{
		m_ignoreSelectRange = true;
		clearTableStyle();
		return;
	}

	ks_stdptr<etoldapi::Areas> spAreas = getSelectionAreas(m_spWorkSheet);
	if (!spAreas)
		return;

	long nAreaCnt = 0;
	HRESULT hr = spAreas->get_Count(&nAreaCnt);
	if (FAILED(hr))
		return;

	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return;

	
	int sheetIdx = getWorkSheetIdx();
	for (int i = 1; i <= nAreaCnt; i++)
	{
		ks_stdptr<etoldapi::Range> spRange;
		hr = spAreas->get_Item(i, &spRange);
		if (FAILED(hr) || !spRange)
			continue;

		RANGE rg = etRange2RANGE(spRange, pSheet->GetBMP());
		rg.SetSheetFromTo(sheetIdx);
		rg.SetSheetTo(sheetIdx);
		if (!rg.IsValid())
			continue;
		m_dataSelectRanges.push_back(rg);

		//清除需要应用位置的样式，防止边框等表格样式残留
		for (auto& childArea : m_tableInfo.childRangeList())
		{
			ES_CUBE fillAlterArea = childArea.getFillAlterArea();
			RANGE fillAreaRg(fillAlterArea, pSheet->GetBMP());
			RANGE tmpRange = fillAreaRg.Intersect(rg);
			if (!tmpRange.IsValid())
				continue;
			clearRangeStyle(tmpRange);
		}
	}
}

double KArrangeProcess::getColRealWidth(int col)
{
	double dWidth = 0;
	ks_stdptr<etoldapi::Range> spColRange;
	HRESULT hr = getSpecifiedRange(&spColRange, m_tableInfo.parentRangeInfo.allRangeInfo.rowFrom, m_tableInfo.parentRangeInfo.allRangeInfo.rowTo, col, col);
	if (FAILED(hr) || !spColRange)
		return dWidth;
	KComVariant colWidth;
	spColRange->get_ColumnWidth(&colWidth);
	if (V_VT(&colWidth) == VT_R8)
		dWidth = V_R8(&colWidth);
	return dWidth;
}

double KArrangeProcess::getRowRealHeight(int row)
{
	double dHeight = 0;
	ks_stdptr<etoldapi::Range> spRowRange;
	HRESULT hr = getSpecifiedRange(&spRowRange, row, row, m_tableInfo.parentRangeInfo.allRangeInfo.colFrom, m_tableInfo.parentRangeInfo.allRangeInfo.colTo);
	if (FAILED(hr) || !spRowRange)
		return dHeight;
	KComVariant rowHeight;
	spRowRange->get_RowHeight(&rowHeight);
	if (V_VT(&rowHeight) == VT_R8)
		dHeight = V_R8(&rowHeight);
	return dHeight;
}

void KArrangeProcess::adjustColumnsWidth()
{
	if (!m_spAtomicTable)
		return;

	for (int col = m_tableInfo.parentRangeInfo.allRangeInfo.colFrom; col <= m_tableInfo.parentRangeInfo.allRangeInfo.colTo; col++)
	{
		if (m_spAtomicTable->m_colList.find(col) != m_spAtomicTable->m_colList.end())
		{
			AtomicCol* pEachAtomicCol = m_spAtomicTable->m_colList.at(col);
			//Todo:获取初始列宽，这个设置后续放到初始化AtomicCol的位置
			pEachAtomicCol->setColOriginWidth(getColRealWidth(col));
			processEachAtomicCol(pEachAtomicCol);
		}
	}
}

void KArrangeProcess::processEachAtomicCol(AtomicCol* pEachAtomicCol)
{
	WholeEffectBase* pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
	if (pWholeEffect)
		pEachAtomicCol->calcColResultWidth(pWholeEffect);
}

HRESULT KArrangeProcess::adjustRowsHeight()
{
	HRESULT hr = S_CONTINUE;
	if (!m_spAtomicTable)
		return E_FAIL;

	for (auto iter = m_spAtomicTable->m_rowList.begin(); iter != m_spAtomicTable->m_rowList.end(); iter++)
	{
		if (m_pStop && !m_pStop->isContinue())
			return hr;

		AtomicRow* pEachAtomicRow = iter->second;
		if (!pEachAtomicRow)
			continue;
		processEachAtomicRow(pEachAtomicRow);
	}
	return hr;
}

void KArrangeProcess::processEachAtomicRow(AtomicRow* pEachAtomicRow)
{
	if (!pEachAtomicRow)
		return;

	std::vector<AtomicCells*> vecCells = pEachAtomicRow->vecCells;
	int iRow = pEachAtomicRow->getRowIdx();
	double rowMaxHeight = 0;
	for (size_t i = 0; i < vecCells.size(); i++)
	{
		AtomicCells* pEachAtomicCells = vecCells.at(i);
		if (!pEachAtomicCells->IsOneCell())//合并单元格 跳过
			continue;

		double sumWidth = getCellWidth(pEachAtomicCells);
		double calcHeight = pEachAtomicCells->getCellEstimatedHeight(sumWidth);
		if (rowMaxHeight < calcHeight)
			rowMaxHeight = calcHeight;
	}

	//设置新的行高
	pEachAtomicRow->setRowResultHeight(rowMaxHeight);

}

double KArrangeProcess::getRowSpaingWithChar()
{
	WholeEffectBase* pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
	if (!pWholeEffect)
		return 0;
	return pWholeEffect->getRowSpaingWithChar();
}

bool isImgFmlaCell(etoldapi::Range* pRange)
{
	static const LPCWSTR CELLIMG_FMLA_PATTERN = __X("=DISPIMG(\"");

	if (!pRange)
		return false;
	VARIANT_BOOL hasFormula;
	pRange->get_HasFormula(&hasFormula);
	if (!hasFormula)
		return false;

	ks_bstr bsFormula;
	pRange->get_Formula(&bsFormula);
	ks_wstring strFormula = bsFormula.c_str();
	size_t size = strFormula.size();
	size_t pos = strFormula.find(CELLIMG_FMLA_PATTERN);
	if (pos < size)
		return true;
	return false;
}

QVector<int> KArrangeProcess::getCellInfo(IN int row, IN int col, IN kaietrecognize::ZoneType zoneType, OUT int& cellWidth, OUT int& cellCharCnt, OUT bool& bLineBreak)
{
	cellWidth = 0;//返回值单位:磅
	cellCharCnt = 0;
	bLineBreak = false;
	QVector<int> eachParaTextWidthVec;

	ks_stdptr<etoldapi::Range> spRangeCell;
	HRESULT hr = getSpecifiedCell(row, col, &spRangeCell);
	if (FAILED(hr) || !spRangeCell)
		return eachParaTextWidthVec;

	VARIANT varCell;
	VariantInit(&varCell);
	hr = spRangeCell->get_Value(etRangeValueDefault, &varCell);
	if (FAILED(hr))
		return eachParaTextWidthVec;

	if (VarIsEmpty(varCell))
		return eachParaTextWidthVec;

	//内嵌图片的单元格,列宽算作0
	if (isImgFmlaCell(spRangeCell))
	{
		return eachParaTextWidthVec;
	}
	//Todo:普通公式类型在et的本质也是字符串来的，后续要不要也考虑跳过呢？
	ks_bstr txt;
	spRangeCell->get_Text(&txt);
	ks_wstring text = txt.c_str();

	//获取该单元格最长的字符数
	if (!text.empty())
	{
		size_t size = text.size();
		int startPos = 0;
		size_t multiLinePos = 0;
		bool bFindManualLineBreak = false;
		int maxCharEachPara = 0;//若存在换行符用户换行的情形下，统计每一行的字符最大数
		do
		{
			multiLinePos = text.find('\n', startPos);//换行符所在的位置
			bFindManualLineBreak = (multiLinePos < size);
			if (bFindManualLineBreak)
				bLineBreak = true;//存在手动换行
			int endPos = bFindManualLineBreak ? multiLinePos : size - 1;

			//去获取子串
			int eachLineCharCnt = endPos - startPos;//int subStrEndPos = bFindManualLineBreak ? endPos : endPos + 1;
			eachLineCharCnt = eachLineCharCnt + (bFindManualLineBreak ? 0 : 1);

			if (eachLineCharCnt > maxCharEachPara)//更新该段的最大值，注意这个值是否准确
				maxCharEachPara = eachLineCharCnt;

			ks_wstring eachParaText = text.substr(startPos, eachLineCharCnt);//注意这里endPos是否要加一

			int eachParaTextWidth = getTextWidth(eachParaText, zoneType);
			if (eachParaTextWidth > 0)
				eachParaTextWidthVec.push_back(eachParaTextWidth);

			if (cellWidth < eachParaTextWidth)
			{
				cellWidth = eachParaTextWidth;
			}
			startPos = endPos + 1;//更新起始值

		} while (bFindManualLineBreak);

		cellCharCnt = maxCharEachPara;

	}

	return eachParaTextWidthVec;
}

int KArrangeProcess::getTextWidth(ks_wstring text, kaietrecognize::ZoneType zoneType)
{
	QFont font;
	font.setFamily(getFontName());
	//Todo:目前只处理了内容
	int fontSize = m_ContentRangeFontSize;
	switch (zoneType)
	{
	case kaietrecognize::ZoneType::RowTitle:
		fontSize = m_HeadRangeFontSize;
		font.setBold(true);
		break;
	case kaietrecognize::ZoneType::BigTitle:
		fontSize = m_TitleRangeFontSize;
		font.setBold(true);
		break;
	case kaietrecognize::ZoneType::Content:
		fontSize = m_ContentRangeFontSize;
		break;
	case kaietrecognize::ZoneType::SubTitle:
		fontSize = m_SubTitleRangeFontSize;
		font.setBold(true);
		break;
	case kaietrecognize::ZoneType::Other:
		fontSize = m_OtherRangeFontSize;
		break;
	case kaietrecognize::ZoneType::Info:
		fontSize = m_InfoRangeFontSize;
		break;
	default:
		fontSize = m_ContentRangeFontSize;
		break;
	}

	font.setPixelSize(fontSize);
	QFontMetrics fm(font);

	QString txt(krt::fromUtf16(text.c_str()));
	int width = fm.width(txt);
	return width;
}

HRESULT KArrangeProcess::adjustRowHeightWithSpace()
{
	double spacing = getRowSpaingWithChar();
	if (spacing <= 0 || !m_spAtomicTable)
		return S_OK;

	for (auto iter = m_spAtomicTable->m_rowList.begin(); iter != m_spAtomicTable->m_rowList.end(); iter++)
	{
		AtomicRow* pEachAtomicRow = iter->second;
		if (!pEachAtomicRow)
			continue;
		double dHeight = pEachAtomicRow->getRowResultHeight();
		dHeight += spacing;
		pEachAtomicRow->setRowResultHeight(dHeight);
	}

	return S_OK;
}

QString KArrangeProcess::getFontName()
{
	return QString("Microsoft YaHei");
}

HRESULT KArrangeProcess::mergeRange(etoldapi::Range* pRange, IETStringTools* pTools)
{
	if (!pRange || !pTools || !m_spWorkSheet || !m_spWorkSheet->GetSheet())
		return E_FAIL;
	RANGE rg(m_spWorkSheet->GetSheet()->GetBMP());
	IdentifyTool::GetTableRange(pRange, &rg);
	if (!rg.IsValid())
		return E_FAIL;
	if (rg.IsSingleCell())
		return S_OK;
	HRESULT hr = S_OK;
	QString strText;
	for (ROW iRow = rg.RowFrom(); iRow <= rg.RowTo(); ++iRow)
		for (COL iCol = rg.ColFrom(); iCol <= rg.ColTo(); ++iCol)
		{
			QString strCellText = IdentifyTool::GetCellText(iRow, iCol, pTools);
			if (!strCellText.isEmpty() && !strText.isEmpty() && strCellText != strText)
				return E_FAIL;
			else if (!strCellText.isEmpty() && strText.isEmpty())
				strText = strCellText;
		}
	return pRange->Merge(TRUE);
}

HRESULT KArrangeProcess::mergeAreaProcess()
{
	HRESULT hr = E_FAIL;
	ks_stdptr<IETStringTools> spTools;
	_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&spTools);
	if (!spTools)
		return E_FAIL;
	spTools->SetEnv(m_spWorkSheet->GetSheet());
	//标题默认做合并处理
	for (int i = 0; i < m_titleRangeCellVec.size(); i++)
	{
		if (m_pStop && !m_pStop->isContinue())
			return hr;

		mergeRange(m_titleRangeCellVec[i], spTools);
	}

	//副标题也做合并处理
	for (int i = 0; i < m_subTitleRangeCellVec.size(); i++)
	{
		if (m_pStop && !m_pStop->isContinue())
			return hr;

		mergeRange(m_subTitleRangeCellVec[i], spTools);
	}

	//其他的暂时也做合并处理
	for (int i = 0; i < m_otherRangeCellVec.size(); i++)
	{
		if (m_pStop && !m_pStop->isContinue())
			return hr;

		mergeRange(m_otherRangeCellVec[i], spTools);
	}

	return hr;
}

bool KArrangeProcess::bContainManualLineBreak(ROW cellRow, COL cellCol)
{
	ks_wstring text;
	HRESULT hr = getCellText(cellRow, cellCol, text);
	if (FAILED(hr))
		return false;

	size_t size = text.size();
	size_t pos = text.find('\n', 0);
	if (pos < size)
		return true;
	return  false;
}

HRESULT KArrangeProcess::getCellText(IN ROW cellRow, IN COL cellCol, OUT ks_wstring& text)
{
	ks_stdptr<etoldapi::Range> spRangeCell;
	HRESULT hr = getSpecifiedCell(cellRow, cellCol, &spRangeCell);
	if (FAILED(hr) || !spRangeCell)
	{
		return E_FAIL;
	}
	ks_bstr txt;
	spRangeCell->get_Text(&txt);
	text = txt.c_str();
	return S_OK;
}

HRESULT KArrangeProcess::getSpecifiedCell(IN ROW cellRow, IN COL cellCol, OUT etoldapi::Range** ppRange)
{
	if (!m_spWorkSheet)
		return E_FAIL;
	ISheet* pSheet = m_spWorkSheet->GetSheet();
	if (!pSheet)
		return E_FAIL;

	RANGE cellRange(pSheet->GetBMP());
	cellRange.SetRowFromTo(cellRow, cellRow);
	cellRange.SetColFromTo(cellCol, cellCol);
	ks_stdptr<etoldapi::Range> spRange;
	m_spWorkSheet->GetRangeByData(&cellRange, &spRange);
	if (!spRange)
		return E_FAIL;

	ks_stdptr<etoldapi::Range> spRangeCell;
	spRange->get_Cells(&spRangeCell);
	if (!spRangeCell)
		return E_FAIL;

	*ppRange = spRangeCell.detach();
	return S_OK;
}

void KArrangeProcess::initFontSizeInfo()
{
	WholeEffectBase* pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
	if (!pWholeEffect)
	{
		//适应
		m_TitleRangeFontSize = 16;
		m_SubTitleRangeFontSize = 14;
		m_HeadRangeFontSize = 11;
		m_ContentRangeFontSize = 10;
		m_OtherRangeFontSize = 10;
		m_InfoRangeFontSize = 10;
		return;
	}

	m_TitleRangeFontSize = pWholeEffect->getTitleRangeFontSize();
	m_SubTitleRangeFontSize = pWholeEffect->getSubTitleRangeFontSize();
	m_HeadRangeFontSize = pWholeEffect->getHeadRangeFontSize();
	m_ContentRangeFontSize = pWholeEffect->getContentRangeFontSize();
	m_OtherRangeFontSize = pWholeEffect->getOtherRangeFontSize();
	m_InfoRangeFontSize = pWholeEffect->getInfoRangeFontSize();
}

single KArrangeProcess::getCellFontSize(int row, int col)
{
	kaietrecognize::ZoneType zoneType = m_tableInfo.getCellZoneType(row, col);
	switch (zoneType)
	{
	case kaietrecognize::RowTitle:
		return m_HeadRangeFontSize;
	case kaietrecognize::BigTitle:
		return m_TitleRangeFontSize;
	case kaietrecognize::Content:
		return m_ContentRangeFontSize;
	case kaietrecognize::SubTitle:
		return m_SubTitleRangeFontSize;
	case kaietrecognize::Other:
		return m_OtherRangeFontSize;
	case kaietrecognize::Info:
		return m_InfoRangeFontSize;
	}
	return m_ContentRangeFontSize;
}

void KArrangeProcess::getRangeData()
{
	if (!m_spAllRangeCell)
		return;

	ks_stdptr<etoldapi::Range> spRangeCell;
	m_spAllRangeCell->get_Cells(&spRangeCell);
	if (!spRangeCell)
		return;

	VARIANT varCell;
	VariantInit(&varCell);
	HRESULT hr = spRangeCell->get_Value(etRangeValueDefault, &varCell);
	if (FAILED(hr))
		return;

	QJsonArray jsonArr;
	if ((V_VT(&varCell) & VT_ARRAY) != 0)
	{
		SAFEARRAY* psa = V_ARRAY(&varCell);
		/*对二维数组的元素进行逐个遍历*/
		SIZE32 index[2] = { 0, 0 };

		SIZE32 lFirstLBound = 0;
		SIZE32 lFirstUBound = 0;
		SIZE32 lSecondLBound = 0;
		SIZE32 lSecondUBound = 0;
		hr = SafeArrayGetLBound(psa, 1, &lFirstLBound);
		if (!SUCCEEDED(hr))
			return;
		hr = SafeArrayGetUBound(psa, 1, &lFirstUBound);
		if (!SUCCEEDED(hr))
			return;
		hr = SafeArrayGetLBound(psa, 2, &lSecondLBound);
		if (!SUCCEEDED(hr))
			return;
		hr = SafeArrayGetUBound(psa, 2, &lSecondUBound);
		if (!SUCCEEDED(hr))
			return;
		for (SIZE32 i = lFirstLBound; i <= lFirstUBound; i++)
		{
			QJsonArray jsonRow;
			index[0] = i;
			VARIANT var;
			for (SIZE32 j = lSecondLBound; j <= lSecondUBound; j++)
			{
				index[1] = j;
				VariantInit(&var);
				hr = SafeArrayGetElement(psa, index, &var);
				if (!SUCCEEDED(hr))
					return;

				if (V_VT(&var) == VT_BSTR)
				{
					jsonRow.append(QJsonValue(krt::fromUtf16(V_BSTR(&var))));
				}
				else if (V_VT(&var) == VT_R8)
				{
					DOUBLE dbl = V_R8(&var);
					jsonRow.append(QJsonValue(QString::number(dbl, 10, 4)));
				}
				else
				{
					jsonRow.append(QJsonValue(QString("")));
				}
				VariantClear(&var);
			}
			jsonArr.append(QJsonValue(jsonRow));
		}
	}

	else if (V_VT(&varCell) == VT_BSTR)
	{
		QJsonArray jsonRow;
		jsonRow.append(QJsonValue(krt::fromUtf16(V_BSTR(&varCell))));
		jsonArr.append(QJsonValue(jsonRow));
	}
	else if (V_VT(&varCell) == VT_R8)
	{
		QJsonArray jsonRow;
		DOUBLE dbl = V_R8(&varCell);
		jsonRow.append(QJsonValue(QString::number(dbl, 10, 4)));
		jsonArr.append(QJsonValue(jsonRow));
	}
	else
	{
		QJsonArray jsonRow;
		jsonRow.append(QJsonValue(QString("")));
		jsonArr.append(QJsonValue(jsonRow));
	}

	VariantClear(&varCell);


}

KArrangeProcessAssistant::KArrangeProcessAssistant(const QJsonObject& jsonDataObj, IKWorksheet* pWorkSheet, bool bServiceRecoResult /*= true*/)
	: QObject(nullptr)
	, m_pTableApplyPara(nullptr)
	, m_pTableRangeStyle(nullptr)
	, m_curZoom(1)
	, m_bNeedRollback(false)
	, m_bFirstSendRevoke(true)
	, m_bCanSendRevoke(true)
	, m_tableCnt(0)
	, m_bServiceRecoResult(bServiceRecoResult)
{
	parseRecData(jsonDataObj, pWorkSheet, bServiceRecoResult);
	if (pWorkSheet)
	{
		const IKWorksheetView* pSheetView = pWorkSheet->GetActiveWorksheetView();
		ks_stdptr<IKWindow> cpWindow;
		if (pSheetView)
			cpWindow = pWorkSheet->GetActiveWorksheetView()->GetWindow();
		if (cpWindow)
		{
			IKMainWindow* pMainWindow = cpWindow->GetMainWindow();
			QList<KMainWindow*> mainWindows;
			kxApp->getMainWindows(mainWindows);
			for (auto mv = mainWindows.begin(); mv != mainWindows.end(); mv++)
			{
				KxMainWindow* currentMv = qobject_cast<KxMainWindow*>(*mv);
				if (currentMv)
				{
					if (pMainWindow == currentMv->coreMainWindow())
						m_pMainWindow = currentMv;
					else
						continue;
				}
			}
		}
	}
}

KArrangeProcessAssistant::~KArrangeProcessAssistant()
{
}

HRESULT KArrangeProcessAssistant::doArrangeProcessList(const QString& type, QList<int> rangeIdxs, TableApplyInfo& tableApplyInfo)
{
	WholeEffectType effectType = AdaptEffect;
	if (type == "compact")
		effectType = CompactEffect;
	else if (type == "loose")
		effectType = LooseEffect;
	else if (type == "fitScreen")
		effectType = AdaptScreenEffect;

	if (!m_pTableApplyPara)
		m_pTableApplyPara = QSharedPointer<TableApplyParam>(new TableApplyParam(effectType));
	else
		m_pTableApplyPara->setEffectType(effectType);

	HRESULT hr = E_FAIL;
	onBeginOrganize();

	QPointer<KArrangeProcessAssistant> spObject = this;
	foreach(auto idx, qAsConst(rangeIdxs))
	{
		if (idx < 0 || idx >= m_rangeInfoList.elementCnt())
			continue;
		if (!isContinue())
		{
			hr = S_OK;
			break;
		}
		if (SUCCEEDED(doApplyProcess(idx, tableApplyInfo)))
			hr = S_OK;
	}
	if (spObject.isNull())
		return hr;

	onEndOrganize(!m_bNeedRollback);//提交事务
	return hr;
}

void KArrangeProcessAssistant::parseRecData(const QJsonObject& jsonDataObj, IKWorksheet* pWorkSheet, bool bServiceRecoResult)
{
	IKSheet* pSheet = (IKSheet*)pWorkSheet;
	if (!pSheet)
		return;
	QJsonObject jsonResult = jsonDataObj;
	TransformResultJson(pSheet, jsonResult, 0);
	
	kaietrecognize::RangeList rangeList = kaietrecognize::RangeList(jsonResult, pWorkSheet, bServiceRecoResult);
	m_rangeInfoList = rangeList.getTableInfoList();

	m_tableCnt = 0;
	m_tableStructCollectInfoList.clear();
	rangeList.getTableStructCollectInfo(m_tableCnt, m_tableStructCollectInfoList);
}

HRESULT KArrangeProcessAssistant::doApplyProcess(int rangeIdx, TableApplyInfo& tableApplyInfo)
{
	kaietrecognize::ParentChildTableInfo& applyTableInfo = m_rangeInfoList.item(rangeIdx);
	if (applyTableInfo.isEmptyTableInfo())
		return E_FAIL;
	HRESULT hr = E_FAIL;
	if (!m_spArrangeProcess)
		m_spArrangeProcess = new KArrangeProcess(applyTableInfo, m_pTableApplyPara, m_pTableRangeStyle, this);
	else
		m_spArrangeProcess->resetParam(applyTableInfo, m_pTableApplyPara, m_pTableRangeStyle);
	if (SUCCEEDED(m_spArrangeProcess->process(this, tableApplyInfo)))
		hr = S_OK;
	return hr;
}

bool KArrangeProcessAssistant::getIsApplyAreaEnable(bool bApplyAll)
{
	if (bApplyAll)
		return false;

	if (m_rangeInfoList.elementCnt() == 0)
		return false;

	kaietrecognize::ParentChildTableInfo& applyTableInfo = m_rangeInfoList.item(0);
	if (applyTableInfo.isEmptyTableInfo())
		return false;

	if (!m_spArrangeProcess)
		m_spArrangeProcess = new KArrangeProcess(applyTableInfo, m_pTableApplyPara, m_pTableRangeStyle, this);
	else
		m_spArrangeProcess->resetParam(applyTableInfo, m_pTableApplyPara, m_pTableRangeStyle);
	return isKFpCcombEnableAreaApply() && m_spArrangeProcess->isEnableAreaApply();
}

bool KArrangeProcessAssistant::getIsKFpCcombEnableAreaApply()
{
	return isKFpCcombEnableAreaApply();
}

void KArrangeProcessAssistant::stopProcess()
{
	m_bNeedRollback = true;
}

bool KArrangeProcessAssistant::isContinue()
{
	return !m_bNeedRollback;
}

ULONG KArrangeProcessAssistant::AddRef()
{
	KS_VALID_REFCOUNT(m_ref);
	return InterlockedIncrement(&m_ref);
}

ULONG KArrangeProcessAssistant::Release()
{
	KS_VALID_REFCOUNT(m_ref);
	ULONG l = InterlockedDecrement(&m_ref);
	if (m_ref == 0)
	{
		m_ref = KS_INVALID_REFCOUNT;
		deleteLater();
	}
	return l;
}

kaietrecognize::TableRangeInfoList KArrangeProcessAssistant::getRangeInfoList() const
{
	return m_rangeInfoList;
}

void KArrangeProcessAssistant::initTranscation()
{
	//事务的初始化需要更具实际交互调整，是不是一定是ActiveDocument上拿到事务
	ks_stdptr<IKWorkbook> pWorkbook = m_rangeInfoList.elementCnt() > 0 ? m_rangeInfoList.item(0).getWorkBook() : nullptr;
	ks_stdptr<etoldapi::_Workbook> pApiWorkBook = pWorkbook;
	if (pApiWorkBook)
		m_spTransTool = pApiWorkBook->GetTransactionTool();
}

void KArrangeProcessAssistant::onBeginOrganize()
{
	//记录当前的缩放比
	IKEtView* pEtView = getWorksheetView(m_spArrangeProcess ? m_spArrangeProcess->getWorkSheet() : nullptr);
	if (pEtView)
	{
		IRenderView* pRenderView = pEtView->GetRenderView();
		if (pRenderView)
		{
			IRenderNormalView* pRenderNormalView = pRenderView->GetNormalView();
			if (pRenderNormalView)
			{
				IRenderData* pRenderData = pRenderNormalView->GetRenderData();
				if (pRenderData)
					m_curZoom = pRenderData->GetZoom();
			}
		}
	}

	initTranscation();
	if (m_spTransTool)
		m_spTransTool->StartTrans();
}

void KArrangeProcessAssistant::onEndOrganize(bool bCommit)
{
	if (!m_spTransTool || !m_spArrangeProcess)
		return;

	if (!m_spArrangeProcess->getWorkBook() || m_spArrangeProcess->getWorkBook()->IsDestroyed())
		return;

	if (bCommit)
	{
		m_spTransTool->CommitTrans(__X("arrange table"), ksoCommit, FALSE);
	}
	else
	{
		m_spTransTool->Rollback();
	}

	if (!m_spArrangeProcess->getWorkSheet() || m_spArrangeProcess->getWorkSheet()->IsDestroyed())
	{
		m_bNeedRollback = false;
		return;
	}

	IKEtView* pEtView = getWorksheetView(m_spArrangeProcess ? m_spArrangeProcess->getWorkSheet() : nullptr);
	if (pEtView)
	{
		IRenderView* pRenderView = pEtView->GetRenderView();
		if (pRenderView)
			pRenderView->DrawRangeRefreshDataByZoom(m_curZoom);//还原为初始的视图缩放比
	}
	m_bNeedRollback = false;
}

HRESULT KArrangeProcessAssistant::doArrangeSwitchColorList(const KxtableStyleParser& parser, QList<int> rangeIdxs, bool bResetDataTable /*= true*/, bool bIgnoreSelectRange /*= true*/)
{
	if (!m_pTableApplyPara)
		m_pTableApplyPara = QSharedPointer<TableApplyParam>(new TableApplyParam(DefaultEffect));
	else
		m_pTableApplyPara->setEffectType(DefaultEffect);

	if (m_pTableRangeStyle)
		m_pTableRangeStyle.clear();
	m_pTableRangeStyle = QSharedPointer<kaietrecognize::TableRangeStyle>(new kaietrecognize::TableRangeStyle(parser));

	HRESULT hr = E_FAIL;
	onBeginOrganize();

	QPointer<KArrangeProcessAssistant> spObject = this;
	foreach(auto idx, qAsConst(rangeIdxs))
	{
		if (idx < 0 || idx >= m_rangeInfoList.elementCnt())
			continue;
		if (!isContinue())
		{
			hr = S_OK;
			break;
		}
		if (SUCCEEDED(doSwitchColor(idx, bResetDataTable, bIgnoreSelectRange)))
			hr = S_OK;
	}
	if (spObject.isNull())
		return hr;

	onEndOrganize(!m_bNeedRollback);//提交事务

	return hr;
}

HRESULT KArrangeProcessAssistant::doSwitchColor(int rangeIdx, bool bRestDataTable /*= true*/, bool bIgnoreSelectRange /*= true*/)
{
	kaietrecognize::ParentChildTableInfo& applyTableInfo = m_rangeInfoList.item(rangeIdx);
	if (applyTableInfo.isEmptyTableInfo())
		return E_FAIL;

	if (!m_spArrangeProcess)
		m_spArrangeProcess = new KArrangeProcess(applyTableInfo, m_pTableApplyPara, m_pTableRangeStyle, this, bIgnoreSelectRange);
	else
		m_spArrangeProcess->resetParam(applyTableInfo, m_pTableApplyPara, m_pTableRangeStyle, bRestDataTable, bIgnoreSelectRange);
	HRESULT hr = m_spArrangeProcess->resetColorOrStyle(this, m_bServiceRecoResult);

	return hr;
}
