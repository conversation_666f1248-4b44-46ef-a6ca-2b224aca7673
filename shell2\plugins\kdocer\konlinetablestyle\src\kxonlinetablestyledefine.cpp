﻿#include "stdafx.h"
#include "kxetcommonheander.h"
#include "kxonlinetablestyledefine.h"

const QString getErrorDesc(OperatorErrorCode code)
{
	const char* ErrorDesc[] = {
		"success",
		"style file canot open",
		"style file content invalide",
		"insert operation error",
		"download operation error",
		"insert operation canseled",
		"parser tablestyle file failed",
		"insert operation do not implemented",
		"no active table",
		"table update recognize data failed",
		"table update recognize canceled",
		"current use item changed",
		"batch one sheet protected or hided",
		"batch one sheet finish",
		"batch apply finished",
		"batch apply start",
	};

	if (code >= Success && code < ErrorCodeEnd)
		return QLatin1String(ErrorDesc[code]);
	else
		return QString();
}