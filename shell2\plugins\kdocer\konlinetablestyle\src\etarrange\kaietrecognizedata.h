﻿#pragma once

#include <etx_kfc.h>
#include <etx_numfmt.h>
#include <etx_exec.h>
#include <etx_core.h>
#include <etx_appcore.h>
#include <etx_applogic.h>
#include <applogic/etapi_old.h>
#include <applogic/et_applogic_i.h>
#include <applogic/et_applogic_infos.h>
#include <src/tablestyleparser/kxettablestyleparser.h>

namespace kaietrecognize
{
	enum ZoneType
	{
		RowTitle = 0,//行标题（表头）
		BigTitle,//大标题
		Content,//内容
		SubTitle,//副标题
		Other,//其他
		Info,//表格信息
		Empty,//空白
	};

	enum StyleZoneType
	{
		StylyUnvaild = -1,//非法
		StyleBigTitle ,//大标题
		StyleInfo,//表格信息
		StyleRowTitle,//行标题（表头）
		StyleFirstCol,//首列
		StyleLastCol,//尾列
		StyleOddRowAlter,//行交替区域-奇数行
		StyleEvenRowAlter,//行交替区域-偶数行
		StyleOddColAlter,//列交替区域-奇数列
		StyleEvenColAlter,//列交替区域-偶数列
		StyleAllContent,//整个内容区域
		StyleTotalRow,//总结内容区域，不做填充，主要标记用
	};

	////////////////////////////////////////////////////
	HRESULT createRange(IN const QJsonArray& rangeArray, IDX iSheet, OUT ES_CUBE& cube);

	//表格整理区域识别参数解析结构
	struct Zone
	{
	public:
		Zone();
		Zone(const QJsonArray& eachZone, IDX iSheet);
	public:
		ZoneType m_type;
		ES_CUBE m_range;
	};

	//一个描述表格的数据结构，包括它的标题、副标题、行标题、内容的结构
	class TableRangeInfo
	{
	public:
		TableRangeInfo();

		void setWorksheet(IKWorksheet* pWorkSheet);
		IKWorksheet* getWorkSheet();
		IKWorkbook* getWorkBook();
		bool isEmptyTableInfo();
		void insertZone(const Zone& zone);
		ZoneType getCellZoneType(int row, int col);
		ES_CUBE getFillAlterArea();
		ES_CUBE getFillRowAlterArea();
		int getFirstColIdx();
		int getLastColIdx();
	private:
		bool isCellInZone(int row, int col, const ES_CUBE& cube);
	public:
		ES_CUBE allRangeInfo;
		QVector<ES_CUBE> titleRangeInfoVec;//大标题
		QVector<ES_CUBE> headRangeInfoVec;//表头(行标题)
		QVector<ES_CUBE> contentRangeInfoVec;//内容
		QVector<ES_CUBE> subTitleRangeInfoVec;//副标题
		QVector<ES_CUBE> otherRangeInfoVec;//其他
		QVector<ES_CUBE> infoRangeInfoVec;//表格信息类型
	private:
		ks_stdptr<IKWorksheet> m_pWorkSheet;
	};

	//父子表数据结构
	class ParentChildTableInfo
	{
	public:
		ParentChildTableInfo();

		void setWorksheet(IKWorksheet* pWorkSheet);
		IKWorksheet* getWorkSheet();
		IKWorkbook* getWorkBook();
		bool isEmptyTableInfo();
		void insertZone(const Zone& zone);
		void insertChildRange(const ES_CUBE& cube);
		ZoneType getCellZoneType(int row, int col);
		ES_CUBE getFillAlterArea();
		ES_CUBE getFillRowAlterArea();
		int getFirstColIdx();
		int getLastColIdx();
		QVector<TableRangeInfo> childRangeList();
	public:
		TableRangeInfo parentRangeInfo;

	private:
		QVector<TableRangeInfo> m_childRangeInfoList;
	};

	struct borderStyleInfo
	{
	public:
		borderStyleInfo(const QString& borderColor, const QString& borderStyle)
		{
			m_borderColor = borderColor;
			m_borderLineStyle = borderStyle;
		}
		QString getBorderColor()
		{
			return m_borderColor;
		}
		QString getBorderStyle()
		{
			return m_borderLineStyle;
		}
	private:
		QString m_borderColor;
		QString m_borderLineStyle;
	};

	class TableRangeStyle
	{
	public:
		TableRangeStyle(const KxtableStyleParser& parser);
		~TableRangeStyle();

		const io_utils::DXF* getTableRangeStyle(StyleZoneType type);
		DWORD getStyleOptino() { return m_styleOption; }

	private:
		QMap<StyleZoneType, io_utils::DXF> m_TableRangeStyleMap;
		DWORD m_styleOption = 0;
	};

	class TableRangeInfoList
	{
	public:
		TableRangeInfoList();
		void insertRangeInfo(const ParentChildTableInfo& tableInfo);
		int elementCnt();
		ParentChildTableInfo& item(int idx);
		void setZoneTypeInfo(const Zone& zone);
	private:
		bool isCubeEqual(const ES_CUBE& cube1, const ES_CUBE& cube2);
	private:
		QVector<ParentChildTableInfo> m_rangeInfoList;
	};

	struct TableStructCollectInfo
	{
	public:
		TableStructCollectInfo()
			:m_TitleCount(0), m_HeaderCount(0), m_ContentCount(0), m_TableInfoCount(0), m_Range()
		{
		}
		void increaseZoneTypeCnt(const Zone* zone)
		{
			switch (zone->m_type)
			{
			case kaietrecognize::RowTitle:
				m_HeaderCount++;
				break;
			case kaietrecognize::BigTitle:
				m_TitleCount++;
				break;
			case kaietrecognize::Content:
				m_ContentCount++;
				break;
			case kaietrecognize::Info:
				m_TableInfoCount++;
				break;
			}
			m_Range = zone->m_range;
		}
		int getTitleCnt() const
		{
			return m_TitleCount;
		}
		int getHeaderCnt() const
		{
			return m_HeaderCount;
		}
		int getContentCnt() const
		{
			return m_ContentCount;
		}
		int getTableInfoCnt() const
		{
			return m_TableInfoCount;
		}

		ES_CUBE* getTableRange() const
		{
			return (ES_CUBE*)(&m_Range);
		}

	private:
		int m_TitleCount;
		int m_HeaderCount;
		int m_ContentCount;
		int m_TableInfoCount;
		ES_CUBE m_Range;
	};

	class RangeList
	{
	public:
		RangeList(const QJsonObject& jsonObject, IKWorksheet* pWorkSheet, bool bServiceRecoResult);
		~RangeList();
		TableRangeInfoList& getTableInfoList();
		void getTableStructCollectInfo(OUT int& tableCnt, OUT std::vector<TableStructCollectInfo>& tableStructCollectInfoList);
	private:
		void init(const QJsonObject& jsonObject, IKWorksheet* pWorkSheet);
		void initByCustomStruct(const QJsonObject& jsonObject, IKWorksheet* pWorkSheet);
		void splitTableByDataTable(IKWorksheet* pSheet, const std::vector<RANGE>& dataTableRgList, ES_CUBE subRangeCube, QVector<ES_CUBE>& outRanges);
		void initDataTableRange(IKWorksheet* pWorksheet, std::vector<RANGE>& dataTableList);
	private:
		TableRangeInfoList m_tableInfoList;
		int m_tableCnt;
		std::vector<TableStructCollectInfo> m_tableStructCollectInfo;
	};

}
