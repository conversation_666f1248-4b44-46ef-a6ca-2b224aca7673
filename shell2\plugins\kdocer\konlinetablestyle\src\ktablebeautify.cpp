﻿#include "stdafx.h"
#include "ktablebeautify.h"
#include "kxonlinereshelper.h"
#include "common/jsapi/kdocerthemeloader.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include <kcomctl/kappskin.h>
#include <ksolite/kcachefile.h>
#include <kcomctl/kmemberuser.h>
#include <kcomctl/kshadowborder.h>
#include <kcomctl/kcommandfactory.h>
#include <kcomctl/kappidlesvr.h>
#include <kcomctl/kquickhelpbarwidget.h>
#include <kxshare/kxapplication.h>
#include <kxshare/kxquickhelpbarcontainer.h>
#include <ksolite/kcoreapplication.h>
#include <ksolite/kdcinfoc.h>
#include <ksolite/kdomainmgr.h>
#include "kdocertoolkitlite/account/kdoceraccount.h"
#include "common/adapter/kxonlinereshelper.h"
namespace
{
	const int TableBeautifyResDownloadTimeout = 3000;
	QString getTableBeautifyResDownloadApi()
	{
		if (!kcoreApp || !kcoreApp->getDomainMgr())
			return QString();
		return kcoreApp->getDomainMgr()->getDomainUrl("docerapiwps") % QLatin1String("/proxy/tablestyle/tablestyle/v1/get_list");
	}

	QString getTableBeautifyResReportApplyApi()
	{
		if (!kcoreApp || !kcoreApp->getDomainMgr())
			return QString();
		return kcoreApp->getDomainMgr()->getDomainUrl("docerapiwps") % QLatin1String("/proxy/tablestyle/tablestyle/v1/download");
	}
}

KTableBeautify::KTableBeautify()
	: m_cmd(nullptr)
{
	if (KCommand* cmd = getCommand())
		connect(cmd, SIGNAL(selectionTableChanged()), this, SIGNAL(selectionTableChanged()));
}

KTableBeautify::~KTableBeautify()
{
}

KTableBeautify* KTableBeautify::instance()
{
	static KTableBeautify m_instance;
	return &m_instance;
}

bool KTableBeautify::isAvaliable()
{
	return getCommand() != nullptr;
}

bool KTableBeautify::isEmptyTable()
{
	return invokeCommandMethod("isEmptyTable");
}

bool KTableBeautify::getTableInfo(QString* id)
{
	return invokeCommandMethod("getTableInfo", Q_ARG(QString*, id));
}

bool KTableBeautify::getTableInfo(int* rowCnt, int* colCnt, int* curRow, int* curCol)
{
	return invokeCommandMethod("getTableInfo",
		Q_ARG(int*, rowCnt), Q_ARG(int*, colCnt), Q_ARG(int*, curRow), Q_ARG(int*, curCol));
}

bool KTableBeautify::getSkinInfo(int* skinIdx, int* onekeySkinIdx, QString* colorIdx)
{
	return invokeCommandMethod("getSkinInfo",
		Q_ARG(int*, skinIdx), Q_ARG(int*, onekeySkinIdx), Q_ARG(QString*, colorIdx));
}

bool KTableBeautify::getEmphasizeInfo(const QString& type, int* pos, int* styleId)
{
	return invokeCommandMethod("getEmphasizeInfo",
		Q_ARG(const QString&, type), Q_ARG(int*, pos), Q_ARG(int*, styleId));
}

bool KTableBeautify::getSelectedCellRange(int* rowBegin, int* rowEnd, int* colBegin, int* colEnd)
{
	return invokeCommandMethod("getSelectedCellRange",
		Q_ARG(int*, rowBegin), Q_ARG(int*, rowEnd), Q_ARG(int*, colBegin), Q_ARG(int*, colEnd));
}

bool KTableBeautify::onekeyBeautify()
{
	return invokeCommandMethod("onekeyBeautify");
}

bool KTableBeautify::setSkin(int skinIdx, const QString& colorIdx, const QVariantList& rgbs, bool useTrans)
{
	return invokeCommandMethod("setSkin",
		Q_ARG(int, skinIdx), Q_ARG(const QString&, colorIdx), Q_ARG(const QVariantList&, rgbs), Q_ARG(bool, useTrans));
}

bool KTableBeautify::setEmphasize(const QString& type, int row, int styleId)
{
	return invokeCommandMethod("setEmphasize",
		Q_ARG(const QString&, type), Q_ARG(int, row), Q_ARG(int, styleId));
}

bool KTableBeautify::clearEmphasize(const QString& type)
{
	return invokeCommandMethod("clearEmphasize", Q_ARG(const QString&, type));
}

bool KTableBeautify::setEmphasizeColor(const QString& color)
{
	return invokeCommandMethod("setEmphasizeColor", Q_ARG(const QString&, color));
}

void KTableBeautify::cancelOnekeyBeautify()
{
	if (KCommand* cmd = getCommand())
		QMetaObject::invokeMethod(cmd, "cancelOnekeyBeautify", Qt::DirectConnection);
}

void KTableBeautify::notifySelectionTableChanged()
{
	emit selectionTableChanged();
}

bool KTableBeautify::applyFont(const QString& fontName)
{
	return invokeCommandMethod("applyFont",
		Q_ARG(const QString&, fontName));
}

bool KTableBeautify::invokeCommandMethod(const char* member, const QGenericArgument& val0,
	const QGenericArgument& val1, const QGenericArgument& val2, const QGenericArgument& val3,
	const QGenericArgument& val4, const QGenericArgument& val5, const QGenericArgument& val6,
	const QGenericArgument& val7, const QGenericArgument& val8, const QGenericArgument& val9)
{
	if (KCommand* cmd = getCommand())
	{
		bool ret = false;
		if (QMetaObject::invokeMethod(cmd, member, Qt::DirectConnection, Q_RETURN_ARG(bool, ret),
			val0, val1, val2, val3, val4, val5, val6, val7, val8, val9))
		{
			return ret;
		}
	}
	return false;
}

KCommand* KTableBeautify::getCommand()
{
	auto _getCommand = []() -> KCommand*
	{
		const char* const cmdId = "TableBeautifyBridge";
		const char* const cmdName = "KxWppTableBeautifyBridgeCommand";

		KxMainWindow* mainWindow = kxApp->currentMainWindow();
		if (mainWindow && mainWindow->commands())
		{
			KCommand* cmd = mainWindow->commands()->command(cmdId);
			if (!cmd)
			{
				cmd = KCommandFactory::createCommand(cmdName, mainWindow, mainWindow);
				if (cmd)
					mainWindow->commands()->addCommand(cmdId, cmd);
			}
			return cmd;
		}
		return nullptr;
	};

	if (!m_cmd)
		m_cmd = _getCommand();
	return m_cmd;
}

///////////////////////////////////////////
KTableBeautifyResMgr::KTableBeautifyResMgr()
	: m_isHandled(false)
{
	m_timer = new QTimer(this);
	m_network = new QNetworkAccessManager(this);
}

KTableBeautifyResMgr* KTableBeautifyResMgr::instance()
{
	static KTableBeautifyResMgr m_instance;
	return &m_instance;
}

bool KTableBeautifyResMgr::getNextResource(int* idx, int* skinIdx, QString* colorIdx, QVariantList* colorRgbs)
{
	if (m_resource.empty() || !idx)
		return false;

	*idx = (*idx + 1) % m_resource.size();
	const SkinResource& res = m_resource.at(*idx);

	if (skinIdx) *skinIdx = res.skinIdx;
	if (colorIdx) *colorIdx = QString::number(res.id);
	if (colorRgbs) *colorRgbs = res.colorRgbs;

	return true;
}

void KTableBeautifyResMgr::downloadResources()
{
	QUrl url(getTableBeautifyResDownloadApi());
	QUrlQuery urlQuery(url);
	urlQuery.addQueryItem("offset", "0");
	urlQuery.addQueryItem("limit", "20");
	urlQuery.addQueryItem("moban_type", "1");
	urlQuery.addQueryItem("rmsp", rmsp());
	url.setQuery(urlQuery);

	m_isHandled = false;
	connect(m_timer, SIGNAL(timeout()), this, SLOT(onDownloadTimeout()), Qt::UniqueConnection);
	connect(m_network, SIGNAL(finished(QNetworkReply*)), this, SLOT(onDownloadFinished(QNetworkReply*)), Qt::UniqueConnection);

	QNetworkRequest request(url);
	m_network->get(request);

	m_timer->setSingleShot(true);
	m_timer->start(TableBeautifyResDownloadTimeout);
}

void KTableBeautifyResMgr::onDownloadTimeout()
{
	if (m_isHandled)
		return;

	m_isHandled = true;
	emit downloadFailed();
}

void KTableBeautifyResMgr::onDownloadFinished(QNetworkReply* reply)
{
	bool isOk = false;
	if (reply->error() == QNetworkReply::NoError)
		isOk = parseResource(reply->readAll());
	reply->deleteLater();

	if (m_isHandled)
		return;

	m_isHandled = true;

	if (!isOk)
		emit downloadFailed();
	else
		emit downloadSucceeded();
}

bool KTableBeautifyResMgr::parseResource(const QByteArray& json)
{
	QJsonDocument document = QJsonDocument::fromJson(json);
	if (document.isNull() || !document.isObject())
		return false;

	QJsonObject object = document.object();

	QJsonValue result = object.value("result");
	if (result.toString() != "ok")
		return false;

	QJsonValue data = object.value("data");
	if (!data.isArray())
		return false;

	QJsonArray items = data.toArray();
	for (int i = 0; i < items.size(); i++)
	{
		QJsonValue value = items.at(i);
		if (!value.isObject())
			return false;

		QJsonObject item = value.toObject();
		int id = item.value("id").toInt();
		int skinId = item.value("skin").toInt();
		QVariantList rgbs = item.value("rgbs").toVariant().toList();
		if (id == 0 || rgbs.isEmpty())
			return false;

		SkinResource resource;
		resource.id = id;
		resource.skinIdx = skinId;
		resource.colorRgbs = rgbs;
		m_resource.append(resource);
	}
	return !m_resource.isEmpty();
}

QString KTableBeautifyResMgr::rmsp()
{
	return QString("pc_konlinewpptablestyle_app_%2_%3_%4")
		.arg(KDcInfoCDetail::getVersion())
		.arg(KDcInfoCDetail::getMC())
		.arg(KDcInfoCDetail::getHdid());
}

///////////////////////////////////////////
KTableOnekeyBeautifyHelper::KTableOnekeyBeautifyHelper(QObject* parent)
	: QObject(parent)
{
}

void KTableOnekeyBeautifyHelper::trigger(const QString& source)
{
	m_source = source;
	doOnekeyBeautify();
}

void KTableOnekeyBeautifyHelper::doOnekeyBeautify()
{
	KTableBeautifyTip* tip = getTipWidget();

	if (!KTableBeautify::instance()->isEmptyTable())
	{
		tip->showProcessing();
		QCoreApplication::processEvents();
	}

	bool result = KTableBeautify::instance()->onekeyBeautify();
	emit finished(result);

	tip->close();
}

void KTableOnekeyBeautifyHelper::doOnekeyBeautifyFailed()
{
	KTableBeautifyTip* tip = getTipWidget();
	tip->showProcessError();
	connect(tip, SIGNAL(retryClicked()), this, SLOT(doOnekeyBeautify()), Qt::UniqueConnection);
}

KTableBeautifyTip* KTableOnekeyBeautifyHelper::getTipWidget()
{
	if (!m_tip)
		m_tip = new KTableBeautifyTip((kxApp->findRelativeMainWindowX(this)));

	return m_tip;
}

///////////////////////////////////////////
void invalidateView(QObject* obj)
{
	KxView* view = KxOnlineTableResHelper::wppGetView(PANEID_NORMAL_SLIDE, obj);
	if (view && view->coreView())
		view->coreView()->Invalidate(NULL);
}

HRESULT getActivePresentation(_Presentation** ppPres)
{
	if (ks_castptr<_Application> pApp = kxApp->coreApplication())
	{
		return pApp->get_ActivePresentation(ppPres);
	}
	return E_FAIL;
}

IKTransactionTool* getTransactionTool(_Presentation* pPres)
{
	if (ks_castptr<IKDocument> spDocument = pPres)
	{
		return spDocument->GetTransactionTool();
	}
	return nullptr;
}

KPreviewHelper::KPreviewHelper(QObject* parent)
	: QObject(parent)
	, m_oldState(ksoFalse)
	, m_commitDesc(nullptr)
{
	kxApp->installEventFilter(this);
}

KPreviewHelper::~KPreviewHelper()
{
}

KPreviewHelper* KPreviewHelper::instance()
{
	static KPreviewHelper m_instance;
	return &m_instance;
}

bool KPreviewHelper::isInPreview()
{
	return m_spTransTool != nullptr;
}

void KPreviewHelper::beginPreview(QWidget* webView, LPCWSTR desc)
{
	endPreview();

	m_pWebView = webView;
	m_commitDesc = desc;
	m_spPres.clear();
	HRESULT hr = getActivePresentation(&m_spPres);
	if (SUCCEEDED(hr) && m_spPres)
		m_spPres->get_Saved(&m_oldState);

	if (m_spTransTool)
	{
		m_spTransTool->Rollback();
		m_spTransTool.clear();
	}

	m_spTransTool = getTransactionTool(m_spPres);
	if (m_spTransTool)
	{
		m_spTransTool->StartTrans();
	}

	blockQhBarUpdate();
}

void KPreviewHelper::endPreview(bool isCommit /* = false*/)
{
	if (!kxApp || !kxApp->coreApplication())
		return;

	if (!m_spTransTool)
		return;

	if (isCommit && m_commitDesc)
	{
		if (m_spTransTool->GetNestLevel() > 0)
			m_spTransTool->CommitTrans(m_commitDesc, ksoCommit, FALSE);
		m_oldState = ksoFalse;
	}
	else
	{
		if (m_spTransTool->GetNestLevel() > 0)
			m_spTransTool->Rollback();
		invalidateView(this);
	}

	m_pWebView = nullptr;
	m_spTransTool.clear();
	m_commitDesc = nullptr;

	if (m_spPres && m_oldState)
		m_spPres->put_Saved(m_oldState);

	unblockQhBarUpdate();
}

bool KPreviewHelper::eventFilter(QObject* o, QEvent* e)
{
	if (!m_spTransTool)
		return false;

	if ((!m_pWebView || !m_pWebView->isVisible()) ||
		(e->type() == QEvent::KeyPress) ||
		(e->type() == QEvent::Leave && o == m_pWebView))
	{
		endPreview();
	}
	else if (e->type() == QEvent::MouseMove)
	{
		QMouseEvent* mouse = static_cast<QMouseEvent*>(e);
		QPoint pos = mouse->globalPos();
		QRect rc = m_pWebView->rect();
		QPoint topLeft = m_pWebView->mapToGlobal(rc.topLeft());
		rc.moveTopLeft(topLeft);
		if (!rc.contains(pos))
		{
			endPreview();
		}
	}
	return false;
}

void KPreviewHelper::blockQhBarUpdate()
{
	KxMainWindow* pMainWin = kxApp->findRelativeMainWindowX(m_pWebView);
	if (pMainWin)
	{
		auto qhBars = pMainWin->findChildren<KxQuickHelpBarContainer*>();
		foreach(KxQuickHelpBarContainer* qhBar, qhBars)
		{
			if (!qhBar->isVisible())
				continue;

			kApp->removeEventFilter(qhBar);
			m_qhBars.push_back(qhBar);
		}
	}

	kxApp->idleSvr()->stop();
}

void KPreviewHelper::unblockQhBarUpdate()
{
	foreach(KxQuickHelpBarContainer* qhBar, m_qhBars)
	{
		if (!qhBar)
			continue;

		kApp->installEventFilter(qhBar);
	}
	m_qhBars.clear();

	kxApp->idleSvr()->start();
}

///////////////////////////////////////////
namespace
{
	static const QSize TipWidgetSize = QSize(228, 36);
	static const qreal TipBorderRadius = 4;
	const char* const TipRetryText = "retry";
	const char* const TipCancelText = "cancel";
	const char* const TipWorkingText = "prettying your table...";
	const char* const TipNetworkErrorText = "network error, please retry";
	const char* const TipProcessErrorText = "beautify failed, please retry";
	const char* const TipTextStyle = "font-family: Microsoft YaHei; text-align: left; font-size: %1px; color: %2; letter-spacing: 0;";
	const char* const TipLinkStyle =
		"QPushButton { font-family: Microsoft YaHei; border: 0; font-size: %1px; letter-spacing: 0; color: %2; }"
		"QPushButton:hover { color: %3; }"
		"QPushButton:press { color: %4; }";
};

KTableBeautifyTip::KTableBeautifyTip(QWidget* parent)
	: QWidget(parent)
{
	setAttribute(Qt::WA_TranslucentBackground);
	setWindowFlags(Qt::Tool | Qt::FramelessWindowHint);
	setWindowModality(Qt::ApplicationModal);
	setFixedSize(KWPSStyle::dpiScaledSize(TipWidgetSize));

	KShadowBorder* shadow = new KShadowBorder(this, this, false, KWPSStyle::dpiScaled(20));
	shadow->setRadius(KWPSStyle::dpiScaled(TipBorderRadius));

	m_stackedLayout = new QStackedLayout(this);
	m_stackedLayout->insertWidget(0, createProcessingWidget(this));
	m_stackedLayout->insertWidget(1, createProcessErrorWidget(this));
	m_stackedLayout->insertWidget(2, createNetworkErrorWidget(this));

	if (KxView* view = KxOnlineTableResHelper::wppGetView(PANEID_NORMAL_SLIDE, this))
	{
		QRect rect = view->rect();
		QPoint position(rect.center().x() - size().width() / 2, rect.center().y() - size().height() / 2);
		move(view->mapToGlobal(position));
	}
}

void KTableBeautifyTip::showProcessing()
{
	m_stackedLayout->setCurrentIndex(0);
	show();
}

void KTableBeautifyTip::showProcessError()
{
	m_stackedLayout->setCurrentIndex(1);
	show();
}

void KTableBeautifyTip::showNetworkError()
{
	m_stackedLayout->setCurrentIndex(2);
	show();
}

void KTableBeautifyTip::paintEvent(QPaintEvent* e)
{
	qreal border = KWPSStyle::dpiScaled(1);
	qreal radius = KWPSStyle::dpiScaled(TipBorderRadius);
	QRect background = rect().adjusted(border, border, -border, -border);
	QPainterPath path = KDrawHelpFunc::getBoundPath(background, radius, ROUND_ALL);

	QPainter painter(this);
	painter.setRenderHint(QPainter::Antialiasing);

	painter.save();
	painter.setPen(getColor("tip-background"));
	painter.setBrush(getColor("tip-background"));
	painter.drawPath(path);
	painter.restore();

	painter.save();
	painter.setPen(QPen(getColor("tip-border"), border));
	painter.drawPath(path);
	painter.restore();
}

void KTableBeautifyTip::cancel()
{
	KTableBeautify::instance()->cancelOnekeyBeautify();
}

bool KTableBeautifyTip::isDarkTheme() const
{
	return KDrawHelpFunc::getHintFromTheme(this, "DocerBaseConfig", "is-dark-skin") == 1;
}

QIcon KTableBeautifyTip::getIcon(const QString& iconName) const
{
	return QIcon(QString(":/icons/%1%2.svg").arg(iconName).arg(isDarkTheme() ? "_dark" : ""));
}

QColor KTableBeautifyTip::getColor(const QString& propName) const
{
	return isDarkTheme() ? getDarkSkinColor(propName) : getWhiteSkinColor(propName);
}

QString KTableBeautifyTip::getRgba(const QString& propName) const
{
	QColor color = getColor(propName);
	return QString("rgba(%1,%2,%3,%4)").arg(color.red()).arg(color.green()).arg(color.blue()).arg(color.alpha());
}

QRgb KTableBeautifyTip::getWhiteSkinColor(const QString& key) const
{
	static QMap<QString, QRgb> skin;
	if (skin.isEmpty())
	{
		skin.insert("tip-border", 0x33b7bfd0);
		skin.insert("tip-background", 0xffffff);
		skin.insert("tip-text", 0x4f5d79);
		skin.insert("tip-link-btn", 0x417ff9);
		skin.insert("tip-link-btn-hover", 0x608dfa);
		skin.insert("tip-link-btn-press", 0x1f72f1);
	}
	return skin.value(key);
}

QRgb KTableBeautifyTip::getDarkSkinColor(const QString& key) const
{
	QMap<QString, QRgb> skin;
	if (skin.isEmpty())
	{
		skin.insert("tip-border", 0x4dffffff);
		skin.insert("tip-background", 0x323233);
		skin.insert("tip-text", 0x99ffffff);
		skin.insert("tip-link-btn", 0xd9498ff2);
		skin.insert("tip-link-btn-hover", 0xf2498ff2);
		skin.insert("tip-link-btn-press", 0x99498ff2);
	}
	return skin.value(key);
}

QWidget* KTableBeautifyTip::createProcessingWidget(QWidget* parent) const
{
	QWidget* w = new QWidget(parent);

	QLabel* lblIcon = createIconLabel(getIcon("table_loading"), w);
	QLabel* lblText = createTextLabel(tr(TipWorkingText), w);

	QPushButton* btnClose = createCloseButton(w);
	QPushButton* btnCancel = createLinkButton(tr(TipCancelText), w);

	connect(btnClose, SIGNAL(clicked()), this, SLOT(close()));
	connect(btnCancel, SIGNAL(clicked()), this, SLOT(cancel()));

	return w;
}

QWidget* KTableBeautifyTip::createProcessErrorWidget(QWidget* parent) const
{
	QWidget* w = new QWidget(parent);

	QLabel* lblIcon = createIconLabel(getIcon("table_error"), w);
	QLabel* lblText = createTextLabel(tr(TipProcessErrorText), w);

	QPushButton* btnClose = createCloseButton(w);
	QPushButton* btnRetry = createLinkButton(tr(TipRetryText), w);

	connect(btnClose, SIGNAL(clicked()), this, SLOT(close()));
	connect(btnRetry, SIGNAL(clicked()), this, SIGNAL(retryClicked()));

	return w;
}

QWidget* KTableBeautifyTip::createNetworkErrorWidget(QWidget* parent) const
{
	QWidget* w = new QWidget(parent);

	QLabel* lblIcon = createIconLabel(getIcon("table_error"), w);
	QLabel* lblText = createTextLabel(tr(TipNetworkErrorText), w);

	QPushButton* btnClose = createCloseButton(w);
	QPushButton* btnRetry = createLinkButton(tr(TipRetryText), w);

	connect(btnClose, SIGNAL(clicked()), this, SLOT(close()));
	connect(btnRetry, SIGNAL(clicked()), this, SIGNAL(retryClicked()));

	return w;
}

QLabel* KTableBeautifyTip::createIconLabel(const QIcon& icon, QWidget* parent) const
{
	QLabel* label = new QLabel(parent);
	label->setGeometry(KWPSStyle::dpiScaledRect(QRect(16, 10, 16, 16)));
	label->setPixmap(icon.pixmap(KWPSStyle::dpiScaledSize(QSize(16, 16))));
	return label;
}

QLabel* KTableBeautifyTip::createTextLabel(const QString& text, QWidget* parent) const
{
	QString styleSheet = QString(TipTextStyle)
		.arg(qRound(KWPSStyle::dpiScaled(12)))
		.arg(getRgba("tip-text"));

	QLabel* label = new QLabel(text, parent);
	label->setGeometry(KWPSStyle::dpiScaledRect(QRect(38, 7, 106, 22)));
	label->setAlignment(Qt::AlignLeading | Qt::AlignLeft | Qt::AlignVCenter);
	label->setStyleSheet(styleSheet);
	return label;
}

QPushButton* KTableBeautifyTip::createLinkButton(const QString& text, QWidget* parent) const
{
	QString styleSheet = QString(TipLinkStyle)
		.arg(qRound(KWPSStyle::dpiScaled(12)))
		.arg(getRgba("tip-link-btn"))
		.arg(getRgba("tip-link-btn-hover"))
		.arg(getRgba("tip-link-btn-press"));

	QPushButton* button = new QPushButton(text, parent);
	button->setGeometry(KWPSStyle::dpiScaledRect(QRect(160, 7, 24, 22)));
	button->setCursor(Qt::PointingHandCursor);
	button->setStyleSheet(styleSheet);
	return button;
}

QPushButton* KTableBeautifyTip::createCloseButton(QWidget* parent) const
{
	KxTriStateButton* btnClose = new KxTriStateButton(parent);
	btnClose->setGeometry(KWPSStyle::dpiScaledRect(QRect(200, 10, 16, 16)));
	btnClose->setStyleSheet("border: noborder;");
	btnClose->setNormalIcon(getIcon("table_close"));
	btnClose->setHoverIcon(getIcon("table_close_hover"));
	btnClose->setPressIcon(getIcon("table_close_press"));
	btnClose->setIconSize(KWPSStyle::dpiScaledSize(QSize(16, 16)));
	btnClose->setCursor(Qt::PointingHandCursor);
	return btnClose;
}

///////////////////////////////////////////
KQhPopupHelper::KQhPopupHelper(QWidget* w)
	: QObject(w)
	, m_widget(w)
{
	if (m_widget)
	{
		m_widget->installEventFilter(this);
		m_elaspedTimer.invalidate();
	}
}

void KQhPopupHelper::trigger()
{
	QMetaObject::invokeMethod(this, "onPopup", Qt::QueuedConnection);
}

bool KQhPopupHelper::eventFilter(QObject* o, QEvent* e)
{
	if (e->type() == QEvent::Show)
	{
		if (m_elaspedTimer.isValid() &&
			m_elaspedTimer.elapsed() <= 1000)
		{
			if (!isPopped(o))
			{
				doPopup();
				m_elaspedTimer.invalidate();
			}
		}
	}
	return false;
}

bool KQhPopupHelper::update()
{
	KxQuickHelpBarContainer* container =
		KTik::findParentByType<KxQuickHelpBarContainer>(m_widget);
	return QMetaObject::invokeMethod(container, "onTimeOut");
}

void KQhPopupHelper::onPopup()
{
	if (update())
	{
		if (m_widget->isVisible())
			doPopup();
		else
			m_elaspedTimer.start();
	}
}

void KQhPopupHelper::doPopup()
{
	QMetaObject::invokeMethod(m_widget, "onLButtonClicked");
	if (isPopped(m_widget))
	{
		emit aboutPopup();
	}
}

bool KQhPopupHelper::isPopped(QObject* o)
{
	auto w = qobject_cast<KQuickHelpShadowButtonBase*>(o);
	return w && w->popupedWidgetVisible();
}

KxTriStateButton::KxTriStateButton(QWidget* parent)
	: QPushButton(parent)
{
}

void KxTriStateButton::paintEvent(QPaintEvent*)
{
	QIcon* icon;
	if (underMouse())
		icon = &m_hoverIcon;
	else if (isDown())
		icon = &m_pressIcon;
	else
		icon = &m_normalIcon;

	QPainter painter(this);
	painter.drawPixmap(0, 0, icon->pixmap(iconSize()));
}
