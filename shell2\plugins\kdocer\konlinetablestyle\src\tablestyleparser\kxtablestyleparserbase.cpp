﻿#include "stdafx.h"
#include <src/tablestyleparser/kxtablestyleparserbase.h>
#include <package/openXmlServer.h>

bool KxlslParser::parserContent(const QString& content)
{
	QDomDocument doc;
	doc.setContent(content);
	QDomElement root = doc.documentElement();
	if (root.isNull())
		return false;

	return parserElement(root);
}

bool KxlslParser::parserAttr(const QDomElement& element, CollectAttributeFunc func)
{
	if (element.hasAttributes())
	{
		QDomNamedNodeMap attrs = element.attributes();

		int count = attrs.count();
		for (int index = 0;index < count; ++index)
		{
			QDomAttr attr = attrs.item(index).toAttr();
			if (!attr.isNull())
			{
				XmlName xn = getXmlName(krt::utf16(attr.name()));

				if (func && !func(xn, attr))
				{
					return false;
				}
			}
		}
	}

	return true;
}

bool KxlslParser::parserChild(const QDomElement& element, CollectElementFunc func)
{
	for (QDomElement childElement = element.firstChildElement();
		!childElement.isNull(); childElement = childElement.nextSiblingElement())
	{
		const QString& tagName = childElement.tagName();
		XmlName xn = getXmlName(krt::utf16(tagName));

		if (func && !func(xn, childElement))
		{
			return false;
		}
	}

	return true;
}

XmlName KxlslParser::getXmlName(const ks_wstring& xmlTag)
{
	const WCHAR* const defaultNamespace = __X("http://schemas.openxmlformats.org/spreadsheetml/2006/main");
	static OpenXmlServer _openXmlServer;
	return XmlMapper::Map(defaultNamespace, xmlTag.c_str(), ks_wcslen(defaultNamespace), xmlTag.length());
}

bool KxlslParser::onOff(const QString& attr)
{
	return	attr == "true" ||
		attr == "True" ||
		attr == "on" ||
		attr == "1" ||
		attr == "t";
}

UINT32 KxlslParser::hexLong(const QString& val)
{
	UINT32 v = 0;
#ifdef X_OS_WINDOWS
	ks_swscanf_s(krt::utf16(val), __X("%8x"), &v);
#else
	ks_sscanf_s(val.toLocal8Bit().data(), "%8x", &v);
#endif
	return v;
}
