﻿#include "stdafx.h"
#include <public_header/kso/krepl/ksorepl.h>
#include "kxwpptablestylehelper.h"
#include "kxonlinereshelper.h"
#include "ksolite/kxjsonhelper.h"

#include <kso/framework/api/wppapi_old.h>
using namespace wppoldapi;
#include "office/wpp/objtable/model/table_style.h"
#include "shell2/wpp/src/wpptablestyle.h"

#include <krt/krt.h>
#include "kxonlinetablestylejsapi.h"
#include "common/jsapi/widget/kdocercommonwebwidget.h"
#include "utilities/path/module/kcurrentmod.h"
#include <src/tablestyleparser/kxcommontablestyleparser.h>

namespace
{
	const QString LastTime("last_time");
	const QString AutoPopupCounts("auto_popup_counts");
	const QString AutoPopupFrequencyOneDay("auto_popup_frequency_one_day");
	const QString NotAutoPopupWithinSevenDays("not_auto_popup_within_seven_days");
	const QString NotAutoPopupWithinSevenDaysSetTime("not_auto_popup_within_seven_days_set_time");
	const qint64 ToleranceDaysForNotAutoPopup = 7;

	void beginSettingsGroup(QSettings& sets, const QString& group)
	{
		sets.setIniCodec("UTF-8");
		QString targetGroup = krt::appName() + group;
		sets.beginGroup(targetGroup);
	}

	void endSettingsGroup(QSettings& sets)
	{
		sets.endGroup();
	}

	QString getSettingsName(const QString& action, const QString& settingSuffix)
	{
		return QString("%1_%2").arg(action).arg(settingSuffix);
	}
}

QMutex KxWppTableStyleHelper::m_Mutex;
QSharedPointer<KxWppTableStyleHelper> KxWppTableStyleHelper::m_pInstance;

inline drawing::Color _String2Color(const QString& strColor)
{
	QColor color(strColor.toUInt(NULL, 16));
	return drawing::Color::fromRgb(color.rgb());
}

inline void _MakeGUID(const char* lpszGUID, GUID& guid)
{
	unsigned int d1 = 0, d2 = 0, d3 = 0, d4 = 0;
	unsigned long long d5 = 0;
	kcsscanf_s(lpszGUID, "{%x-%x-%x-%x-%llx}", &d1, &d2, &d3, &d4, &d5);
	guid.Data1 = d1;
	guid.Data2 = (unsigned short)d2;
	guid.Data3 = (unsigned short)d3;
	guid.Data4[0] = (unsigned char)(d4 >> 8);
	guid.Data4[1] = (unsigned char)d4;
	guid.Data4[2] = (unsigned char)(d5 >> 40);
	guid.Data4[3] = (unsigned char)(d5 >> 32);
	guid.Data4[4] = (unsigned char)(d5 >> 24);
	guid.Data4[5] = (unsigned char)(d5 >> 16);
	guid.Data4[6] = (unsigned char)(d5 >> 8);
	guid.Data4[7] = (unsigned char)(d5);
}

void setPartStyleInfo(objtable::TablePartStyle* partTableStyle, const KxCommonTableStyleInfo::PartStyleInfo &partStyleInfo)
{
	if (!partTableStyle)
	{
		return;
	}

	if(!partStyleInfo.fontInfo.fontColor.isEmpty())
	{
		drawing::FontReference* pFontRef = new drawing::FontReference;
		pFontRef->setColor(partStyleInfo.fontInfo.fontColor);
		partTableStyle->setFontRef(pFontRef);
	}
	partTableStyle->setBold(partStyleInfo.fontInfo.bBold);
	partTableStyle->setItalic(partStyleInfo.fontInfo.bItalic);
	partTableStyle->setUnderline(partStyleInfo.fontInfo.underLine);
	//设置fill
	if(!partStyleInfo.fillColor.isNull())
		partTableStyle->setFill(partStyleInfo.fillColor);
	//设置边框
	for (const auto& borderType : partStyleInfo.borderInfo.keys())
	{
		drawing::Outline pLine = partStyleInfo.borderInfo[borderType];
		switch (borderType)
		{
		case KxCommonTableStyleInfo::Left:
			partTableStyle->setLeftBorder(pLine);
			break;
		case KxCommonTableStyleInfo::Right:
			partTableStyle->setRightBorder(pLine);
			break;
		case KxCommonTableStyleInfo::Top:
			partTableStyle->setTopBorder(pLine);
			break;
		case KxCommonTableStyleInfo::Bottom:
			partTableStyle->setBottomBorder(pLine);
			break;
		case KxCommonTableStyleInfo::InsideH:
			partTableStyle->setInsideHBorder(pLine);
			break;
		case KxCommonTableStyleInfo::InsideV:
			partTableStyle->setInsideVBorder(pLine);
			break;
		default:
			break;
		}
	}
}

bool KxWppTableStyleHelper::applyStyle(const KOTSResourceInfo& resourceInfo, const QByteArray& data)
{
	QString guid;
	KxCommonTableStyleParser parser;
	parser.setCustomColor(resourceInfo.themeColorKey, resourceInfo.customColor);
	if (!parser.parserContent(QString::fromUtf8(data)))
		return false;

	if (!addStyle(parser, guid))
		return false;

	if (!applyStyle(guid, parser.getStyleOptionList()))
		return false;

	return true;
}

bool KxWppTableStyleHelper::applyStyleBySystemIndex(int index)
{
	if (index < 0)
		return false;
	QByteArray byteArray;
	if (m_systemStyleByteArray.find(index) != m_systemStyleByteArray.end())
	{
		byteArray = m_systemStyleByteArray[index];
	}
	else
	{
		QString systemStyleFilePath = docer::base::KCurrentModule::getModuleResourceDirPath()
			+ "/wpp_system_style/system_"
			+ QString::number(index)
			+ ".tablestyle";
		QFile file(systemStyleFilePath);
		if (!file.open(QIODevice::ReadOnly))
		{
			return false;
		}
		byteArray = file.readAll();
		m_systemStyleByteArray[index] = byteArray;
	}
	return applyStyle(KOTSResourceInfo(), byteArray);
}

WpViewType KxWppTableStyleHelper::getWppViewType()
{
	WpViewType viewType = wpViewInvalid;
	KxMainWindow* mw = kxApp->currentMainWindow();
	if (!mw)
		return viewType;

	ks_stdptr<Pane> spApiView = mw->getActiveCoreView();
	if (!spApiView)
		return viewType;

	spApiView->get_ViewType(&viewType);
	return viewType;
}

bool KxWppTableStyleHelper::addStyle(const KxCommonTableStyleParser& parser, QString& outGuid)
{
	const KxCommonTableStyleInfo::StyleInfo& styleInfo = parser.getTableStyle();
	outGuid = styleInfo.guid;
	GUID guid;
	_MakeGUID(styleInfo.guid.toLatin1(), guid);
	objtable::TablePartStyle* pPartStyle = NULL;
	objtable::TableStyle* pStyle = objtable::TableStyleManager::getSingletonPtr()->getStyle(guid);
	if (pStyle)
	{
		//已经插入过该样式到管理器
		return true;
	}
	pStyle = objtable::TableStyleManager::getSingletonPtr()->addStyle(guid);
	if (pStyle == NULL)
	{
		return false;
	}
	pStyle->m_guid = guid;
	pStyle->m_styleName = styleInfo.name;
	
	const QList<KxCommonTableStyleInfo::PartStyleInfo>& partStyleList = parser.getStyleList();
	for (const auto& styleType : styleInfo.partStyleIndexMap.keys())
	{
		int xfIndex = styleInfo.partStyleIndexMap[styleType];
		if (xfIndex < 0 && xfIndex >= partStyleList.count())
			continue;
		pPartStyle = new objtable::TablePartStyle;
		setPartStyleInfo(pPartStyle, partStyleList[xfIndex]);
		switch (styleType)
		{
		case KxCommonTableStyleInfo::WholeTable:
			pStyle->setWholeTable(pPartStyle);
			break;
		case KxCommonTableStyleInfo::FirstRow:
			pStyle->setFirstRow(pPartStyle);
			break;
		case KxCommonTableStyleInfo::LastRow:
			pStyle->setLastRow(pPartStyle);
			break;
		case KxCommonTableStyleInfo::FirstCol:
			pStyle->setFirstCol(pPartStyle);
			break;
		case KxCommonTableStyleInfo::LastCol:
			pStyle->setLastCol(pPartStyle);
			break;
		case KxCommonTableStyleInfo::Band1H:
			pStyle->setBand1H(pPartStyle);
			break;
		case KxCommonTableStyleInfo::Band2H:
			pStyle->setBand2H(pPartStyle);
			break;
		case KxCommonTableStyleInfo::Band1V:
			pStyle->setBand1V(pPartStyle);
			break;
		case KxCommonTableStyleInfo::Band2V:
			pStyle->setBand2V(pPartStyle);
			break;
		case KxCommonTableStyleInfo::TLCELL:
			pStyle->setNwCell(pPartStyle);
			break;
		case KxCommonTableStyleInfo::TRCELL:
			pStyle->setNeCell(pPartStyle);
			break;
		case KxCommonTableStyleInfo::BLCELL:
			pStyle->setSwCell(pPartStyle);
			break;
		case KxCommonTableStyleInfo::BRCELL:
			pStyle->setSeCell(pPartStyle);
			break;
		default:
			delete pPartStyle;
			break;
		}
	}
	objtable::TableStyleManager::getSingletonPtr()->setTextStyleTextStyleByGuid(guid);
	objtable::TableStyleManager::getSingletonPtr()->updatePropertyBag();
	return true;
}

bool KxWppTableStyleHelper::applyStyle(const QString& strGuid, const QList<KxCommonTableStyleInfo::PartStyleType> optionList)
{
	GUID guid;
	_MakeGUID(strGuid.toLatin1(), guid);
	objtable::TableStyle* pStyle = objtable::TableStyleManager::getSingletonPtr()->getStyle(guid);
	if (!pStyle)
	{
		return false;
	}
	CTableStyle tableStyle;
	tableStyle.applyStyle(strGuid);
	//重新设置WPP表格样式的填充勾选项，以正确显示样式
	if (ITableStyleCtrller* ctrl = tableStyle.getCtrller())
	{
		auto _covertToTri = [](bool b) {
			return b ? ksoTrue : ksoFalse;
		};
		ctrl->Set_TableStyleOptions(tsoBandedRows, _covertToTri(optionList.contains(KxCommonTableStyleInfo::Band1H)));
		ctrl->Set_TableStyleOptions(tsoBandedColumns, _covertToTri(optionList.contains(KxCommonTableStyleInfo::Band1V)));
		ctrl->Set_TableStyleOptions(tsoHeaderRow, _covertToTri(optionList.contains(KxCommonTableStyleInfo::FirstRow)));
		ctrl->Set_TableStyleOptions(tsoTotalRow, _covertToTri(optionList.contains(KxCommonTableStyleInfo::LastRow)));
		ctrl->Set_TableStyleOptions(tsoFirstColumn, _covertToTri(optionList.contains(KxCommonTableStyleInfo::FirstCol)));
		ctrl->Set_TableStyleOptions(tsoLastColumn, _covertToTri(optionList.contains(KxCommonTableStyleInfo::LastCol)));
	}
	return true;
}

/*********************** KxWppTableStylePopupHelper *****************************/

struct KxWppTableStylePopupHelper::LastAutoPopupInfo
{
	QDateTime m_lastTime = QDateTime();
	int m_autoPopupCounts = 0;
	int m_autoPopupFrequencyOneDay = 1;
	bool m_bNotAutoPopupWithinSevenDays = false;
	QDateTime m_notAutoPopupWithinSevenDaysSetTime = QDateTime();
};

KxWppTableStylePopupHelper::KxWppTableStylePopupHelper()
{
	setAutoPopupFrequencyOneDay("tablesmartstyle", 0);
	m_bShouldAutoPopup = false;
}

QString KxWppTableStylePopupHelper::getDefaultQhInfoDirPath()
{
	QString path = krt::dirs::officeData().append("\\kdocercommon");
	QDir dir(path);
	if (!dir.exists())
		dir.mkpath(dir.path());
	path.append("\\default.ini");
	return path;
}

bool KxWppTableStylePopupHelper::canAutoPopupQhSinceLastTime(const QString& group, const QString& action)
{
	resetAutoPopupCountsIfNeeded(group, action);

	auto info = getLastAutoPopupInfo(group, action);
	if (info.m_autoPopupFrequencyOneDay <= 0)
		return false;

	if (info.m_bNotAutoPopupWithinSevenDays)
		return info.m_notAutoPopupWithinSevenDaysSetTime.daysTo(QDateTime::currentDateTime()) >= ToleranceDaysForNotAutoPopup;

	return info.m_autoPopupCounts < info.m_autoPopupFrequencyOneDay;
}

void KxWppTableStylePopupHelper::setLastAutoPopupTime(const QString& group, const QString& action)
{
	QString qhInfoDirPath = getDefaultQhInfoDirPath();
	if (qhInfoDirPath.isEmpty())
		return;

	QSettings sets(qhInfoDirPath, QSettings::IniFormat);
	beginSettingsGroup(sets, group);
	sets.setValue(getSettingsName(action, LastTime), QDateTime::currentDateTime());
	endSettingsGroup(sets);
}

void KxWppTableStylePopupHelper::setAutoPopupFrequencyOneDay(const QString& group, int frequency)
{
	QString qhInfoDirPath = getDefaultQhInfoDirPath();
	if (qhInfoDirPath.isEmpty())
		return;

	QSettings sets(qhInfoDirPath, QSettings::IniFormat);
	beginSettingsGroup(sets, group);
	sets.setValue(AutoPopupFrequencyOneDay, frequency);
	endSettingsGroup(sets);
}

void KxWppTableStylePopupHelper::increaseAutoPopupCounts(const QString& group, const QString& action)
{
	QString qhInfoDirPath = getDefaultQhInfoDirPath();
	if (qhInfoDirPath.isEmpty())
		return;

	QSettings sets(qhInfoDirPath, QSettings::IniFormat);
	QString actionPopupCounts = getSettingsName(action, AutoPopupCounts);
	beginSettingsGroup(sets, group);
	sets.setValue(actionPopupCounts, sets.value(actionPopupCounts, 0).toInt() + 1);
	endSettingsGroup(sets);
}

void KxWppTableStylePopupHelper::resetAutoPopupCountsIfNeeded(const QString& group, const QString& action)
{
	auto info = getLastAutoPopupInfo(group, action);
	QString qhInfoDirPath = getDefaultQhInfoDirPath();
	if (qhInfoDirPath.isEmpty())
		return;

	QSettings sets(qhInfoDirPath, QSettings::IniFormat);
	beginSettingsGroup(sets, group);
	if (info.m_lastTime.daysTo(QDateTime::currentDateTime()))
	{
		sets.setValue(getSettingsName(action, AutoPopupCounts), 0);
	}
	if (info.m_notAutoPopupWithinSevenDaysSetTime.daysTo(QDateTime::currentDateTime()) >= ToleranceDaysForNotAutoPopup)
	{
		sets.setValue(getSettingsName(action, NotAutoPopupWithinSevenDays), false);
	}
	endSettingsGroup(sets);
}

void KxWppTableStylePopupHelper::setNotAutoPopupWithinSevenDays(const QString& group, const QString& action, bool value)
{
	QString qhInfoDirPath = getDefaultQhInfoDirPath();
	if (qhInfoDirPath.isEmpty())
		return;

	QSettings sets(qhInfoDirPath, QSettings::IniFormat);
	beginSettingsGroup(sets, group);
	sets.setValue(getSettingsName(action, NotAutoPopupWithinSevenDays), value);
	sets.setValue(getSettingsName(action, NotAutoPopupWithinSevenDaysSetTime), QDateTime::currentDateTime());
	endSettingsGroup(sets);
}

bool KxWppTableStylePopupHelper::isNotAutoPopupWithinSevenDays(const QString& group, const QString& action)
{
	auto info = getLastAutoPopupInfo(group, action);
	if (!info.m_bNotAutoPopupWithinSevenDays)
		return false;

	if (info.m_notAutoPopupWithinSevenDaysSetTime.isNull())
		return false;

	return info.m_notAutoPopupWithinSevenDaysSetTime.daysTo(QDateTime::currentDateTime()) < ToleranceDaysForNotAutoPopup;
}

void KxWppTableStylePopupHelper::updateLastAutoPopupInfo(const QString& group, const QString& action)
{
	setLastAutoPopupTime(group, action);
	increaseAutoPopupCounts(group, action);
}

void KxWppTableStylePopupHelper::tabIndexChanged(QObject* sender, int index, const QString& targetId)
{
	if (!sender || index < 0 || index > 3)
		return;

	KxOnlineTableStyleJsApi* jsApi = sender->findChild<KxOnlineTableStyleJsApi*>();
	if (jsApi)
	{
		jsApi->tabIndexChanged(index);
	}
	else
	{
		m_initTabIndex = index;
		m_initTargetId = targetId;
	}
}

int KxWppTableStylePopupHelper::getInitTabIndex() const
{
	return m_initTabIndex;
}

QString KxWppTableStylePopupHelper::getInitTabTargetId() const
{
	return m_initTargetId;
}

void KxWppTableStylePopupHelper::setOpenType(const QString& openType)
{
	m_openType = openType;
}

bool KxWppTableStylePopupHelper::shouldAutoPopup() const
{
	return m_bShouldAutoPopup;
}

KxWppTableStylePopupHelper::LastAutoPopupInfo
KxWppTableStylePopupHelper::getLastAutoPopupInfo(const QString& group, const QString& action)
{
	LastAutoPopupInfo info;
	QString qhInfoDirPath = getDefaultQhInfoDirPath();
	if (qhInfoDirPath.isEmpty())
		return info;

	QSettings sets(qhInfoDirPath, QSettings::IniFormat);
	beginSettingsGroup(sets, group);
	info.m_lastTime = sets.value(getSettingsName(action, LastTime), QDateTime()).toDateTime();
	info.m_autoPopupCounts = sets.value(getSettingsName(action, AutoPopupCounts), 0).toInt();
	info.m_autoPopupFrequencyOneDay = sets.value(AutoPopupFrequencyOneDay, 1).toInt();
	info.m_bNotAutoPopupWithinSevenDays = sets.value(getSettingsName(action, NotAutoPopupWithinSevenDays), false).toBool();
	info.m_notAutoPopupWithinSevenDaysSetTime = sets.value(getSettingsName(action, NotAutoPopupWithinSevenDaysSetTime), QDateTime()).toDateTime();
	endSettingsGroup(sets);
	return info;
}
