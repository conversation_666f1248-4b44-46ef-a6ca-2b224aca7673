﻿#pragma once

#include <functional>
#include <xmlFx_v2/xmlMapper.h>

class KxlslParser
{
public:
	typedef std::function<bool(XmlName xn, const QDomAttr& attr)> CollectAttributeFunc;
	typedef std::function<bool(XmlName xn, const QDomElement& element)> CollectElementFunc;

	virtual bool parserContent(const QString& content);
protected:
	virtual bool parserElement(const QDomElement& element) = 0;
	bool parserAttr(const QDomElement& element, CollectAttributeFunc func);
	bool parserChild(const QDomElement& element, CollectElementFunc func);

	XmlName getXmlName(const ks_wstring& xmlTag);
	bool onOff(const QString& attr);
	UINT32 hexLong(const QString& val);
};