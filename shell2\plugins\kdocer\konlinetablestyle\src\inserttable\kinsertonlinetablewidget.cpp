﻿#include <stdafx.h>
#include "kinsertonlinetablewidget.h"
#include <kcomctl/kpluginloadwidget.h>
#include <public_header/kliteui/uicontrol/kseparatorline.h>
#include <public_header/drawing/view/theme_agent.h>
#include <kcomctl/kdrawhelpfunc.h>
#include <QSvgRenderer>
#include <public_header/kliteui/uicontrol/klitelabel.h>
#include <kcomctl/kgallerypopupwidgetex.h>
#include <kxshare/kxcolorgallery.h>
#include <kxshare/kxcolorgallery_v2.h>
#include <kcomctl/tik.h>
#include <kcomctl/kpopupcefwidget.h>
#include "kinsertonlinetablemodel.h"
#include <common/widget/docerlitetoast/kdocerlisttoasthelper.h>
#include <kdocerfunctional.h>
#include <src/ktablestyleinfocollect.h>
#include <kliteui/commonui/klitetooltip.h>
#include <kdocertoolkit/kdocerutils.h>
#include <public_header/kliteui/kliteshadowborder.h>
#include <kcomctl/kpluginloadwidget.h>
#include <src/kxonlinereshelper.h>
#include "common/base/kapplyfuncwrapper.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
class KxInsertColorModel : public KxColorModel
{
	Q_OBJECT
public:
	explicit KxInsertColorModel(IKApplication* coreApp, QObject* parent)
		:KxColorModel(coreApp, parent) {};
	~KxInsertColorModel() {};

	virtual bool prepareItems() override
	{
		KxColorModel::_addRecentClrs();
		KxColorModel::_addThemeClrs();
		KxColorModel::_addStandardClrs();

		KxSeparatorItem* sepItem = new KxSeparatorItem(this);
		addElement(sepItem);

		addSpacing(1);
		KxMoreColorItem* moreColorItem = new KxMoreColorItem(tr("&More Color..."), this);
		addElement(moreColorItem);

		addSpacing(1);
		KxSnapperColorItem* snapItem = new KxSnapperColorItem(tr("Snapper Color(&E)"), this);
		addElement(snapItem);
		return true;
	}

	void checkColor(const QColor& clr)
	{
		for (int i = 0; i < count(); i++)
		{
			KxColorItem* item = qobject_cast<KxColorItem*>(element(i));
			if (!item)
				continue;

			if (clr == item->getColor())
			{
				setCurrentIndex(i);
			}
			else
			{
				item->setChecked(false);
			}
		}
	}
};

KOnlineTableThemeColorButton::KOnlineTableThemeColorButton(const drawing::Color& color, const QString& text, QWidget* parent)
	: QAbstractButton(parent)
	, m_themeColor(color)
{
	setText(text);
	setFocusPolicy(Qt::NoFocus);
	connect(this, &QAbstractButton::clicked, this, &KOnlineTableThemeColorButton::onClick);

	QSvgRenderer render(QLatin1String(":/icons_svg/24x24/cancel.svg"));
	QPixmap pixmap(KLiteStyle::dpiScaledSize(render.defaultSize()));
	pixmap.fill(Qt::transparent);
	QPainter painter(&pixmap);
	render.render(&painter);
	m_selectedIcon = QIcon(pixmap);
}

void KOnlineTableThemeColorButton::setSelectedState(bool isSelected)
{
	m_isSelected = isSelected;
}

void KOnlineTableThemeColorButton::setHoverState(bool state)
{
	m_isHovered = state;
}

void KOnlineTableThemeColorButton::setIsCustom(bool bCustom)
{
	m_bCustom = bCustom;
}

bool KOnlineTableThemeColorButton::isCustom()
{
	return m_bCustom;
}

const drawing::Color& KOnlineTableThemeColorButton::getColor()
{
	return m_themeColor;
}

void KOnlineTableThemeColorButton::setColor(const drawing::Color& clr)
{
	m_themeColor = clr;
}

void KOnlineTableThemeColorButton::paintEvent(QPaintEvent* event)
{
	QPainter p(this);
	p.save();
	p.setRenderHints(QPainter::Antialiasing);
	p.setPen(Qt::NoPen);

	QColor themeColor;
	QPointF center = rect().center() + QPointF(1., 1.);
	if (!m_themeColor.isEmpty())
	{
		themeColor = getColorFromThemeColor();
		p.setBrush(QBrush(themeColor));
		p.drawEllipse(center, KWPSStyle::dpiScaled(10.), KWPSStyle::dpiScaled(10.));
	}
	else
	{
		QPointF pos = center - QPointF(KLiteStyle::dpiScaled(12), KLiteStyle::dpiScaled(12));
		QIcon::Mode mode = QIcon::Mode::Normal;
		if (isDown())
			mode = QIcon::Selected;
		else if (m_isHovered)
			mode = QIcon::Mode::Active;
		p.drawPixmap(pos, icon().pixmap(KLiteStyle::dpiScaledSize(24, 24), mode));
	}

	if(!m_bCustom || !m_themeColor.isEmpty())
	{
		QPen pen;
		QColor penColor = KDrawHelpFunc::getColorFromTheme(this, "CommonThemePalette", "kd-color-line-regular");
		penColor.setAlpha(30);
		pen.setColor(penColor);
		pen.setWidth(KLiteStyle::dpiScaled(2));
		p.setPen(pen);
		p.setBrush(Qt::NoBrush);
		p.drawEllipse(center, KWPSStyle::dpiScaled(9.), KWPSStyle::dpiScaled(9.));
		p.restore();
	}

	if ((m_isSelected || isDown()) && (!m_bCustom || !m_themeColor.isEmpty()))
	{
		int h, s, l = 0;
		themeColor.getHsl(&h, &s, &l);
		// 从0~360的范围 转换成0~255
		h = (h / 360.0) * 255;
		QIcon icon;
		QSize iconSize;
		if (m_isSelected && m_bCustom && (isDown() || m_isHovered))
		{
			icon = m_selectedIcon;
			iconSize = KLiteStyle::dpiScaledSize(24, 24);
		}
		else
		{
			icon = drawing::ColorHelper::isDeepColor(h, s, l) ? kxApp->loadIcon("kso-themeColorPane-select-white")
				: kxApp->loadIcon("kso-themeColorPane-select-dark");
			iconSize = KLiteStyle::dpiScaledSize(20, 20);
		}
		QPixmap pixmap = icon.pixmap(iconSize);
		QPoint start = center.toPoint() - QPoint(iconSize.width() / 2, iconSize.height() / 2);
		p.drawPixmap(start, pixmap);
	}

	if (m_isHovered || m_isSelected)
	{
		p.setRenderHints(QPainter::Antialiasing);

		QColor color = KDrawHelpFunc::getColorFromTheme(this, "CommonThemePalette", "kd-color-line-medium");
		QPen pen;
		pen.setColor(color);
		p.setPen(pen);

		p.drawEllipse(center, KWPSStyle::dpiScaled(12.), KWPSStyle::dpiScaled(12.));
	}
}

void KOnlineTableThemeColorButton::enterEvent(QEvent* event)
{
	emit previewBegin(m_themeColor);
}

void KOnlineTableThemeColorButton::leaveEvent(QEvent* event)
{
	emit previewEnd();
}

QSize KOnlineTableThemeColorButton::sizeHint() const
{
	return KWPSStyle::dpiScaledSize(QSize(26, 26));
}

void KOnlineTableThemeColorButton::onClick()
{
	m_isHovered = false;
	m_isSelected = true;
	emit previewBegin(m_themeColor);
	emit colorChange(m_themeColor);
	emit colorBtnClicked(m_themeColor);
}

QColor KOnlineTableThemeColorButton::getColorFromThemeColor()
{
	QColor color;

	IKApplication* pApp = KDocerUtils::getCoreApplication(this);
	if (!pApp)
		return color;

	IKDocument* pDoc = pApp->GetActiveDocument();
	if (!pDoc)
		return color;

	const drawing::IThemeAgent* pIThemeAgent = pDoc->GetActiveTheme();

	if (pIThemeAgent)
	{
		const drawing::SchemeColorInterpreter schemeInterpreter = pIThemeAgent->getColorSchemeInterpreter();
		color = m_themeColor.toRgb(&schemeInterpreter);
	}

	return color;
}

KOnlineTableThemeColorPane::KOnlineTableThemeColorPane(QWidget* parent)
	: QWidget(parent)
{
	m_previewEndTimer = new QTimer(this);
	m_previewBeginTimer = new QTimer(this);
	m_customPreviewBeginTimer = new QTimer(this);
	m_delayClosePopupTimer = new QTimer(this);
	m_delayClosePopupTimer->setInterval(100);
	m_delayClosePopupTimer->setSingleShot(true);

	setFocusPolicy(Qt::NoFocus);

	connect(m_previewEndTimer, &QTimer::timeout, this, &KOnlineTableThemeColorPane::onPreviewEndTimeOut);
	connect(m_previewBeginTimer, &QTimer::timeout, this, &KOnlineTableThemeColorPane::onPreviewBeginTimeOut);
	connect(m_customPreviewBeginTimer, &QTimer::timeout, this, &KOnlineTableThemeColorPane::onCustomPreviewBeginTimeOut);
	connect(m_delayClosePopupTimer, &QTimer::timeout, this, &KOnlineTableThemeColorPane::onDelayClosePopupWidget);
	
	m_popupWidget = new KGalleryPopupWidgetEx(this);
	m_popupWidget->installEventFilter(this);
	if (auto containter = KTik::findParentByType<KxInsertTableStyleContainer>(this))
		containter->installEventFilter(this);

	QHBoxLayout* layout = new QHBoxLayout(this);
	layout->setAlignment(Qt::AlignRight);
	layout->setSpacing(KWPSStyle::dpiScaled(4));
	layout->setContentsMargins(0, 0, 0, 0);

	auto addButton = [layout, this](KOnlineTableThemeColorButton* pButton)
	{
		pButton->setProperty("qtspyName", pButton->text());
		layout->addWidget(pButton);
		connect(pButton, &KOnlineTableThemeColorButton::colorChange, this, &KOnlineTableThemeColorPane::onColorChange);
		connect(pButton, &KOnlineTableThemeColorButton::colorBtnClicked, this, &KOnlineTableThemeColorPane::colorBtnClicked);
		connect(pButton, &KOnlineTableThemeColorButton::previewBegin, this, &KOnlineTableThemeColorPane::onPreViewBegin);
		connect(pButton, &KOnlineTableThemeColorButton::previewEnd, this, &KOnlineTableThemeColorPane::onPreviewEnd);
		connect(pButton, &KOnlineTableThemeColorButton::colorBtnClicked, this, &KOnlineTableThemeColorPane::onColorButtonClicked);
		this->m_buttons.append(pButton);
	};

	addButton(new KOnlineTableThemeColorButton(drawing::Color::PresetBlack, "PresetBlack", this));

	addButton(new KOnlineTableThemeColorButton(drawing::Color::Accent1, "Accent1", this));

	addButton(new KOnlineTableThemeColorButton(drawing::Color::Accent2, "Accent2", this));

	addButton(new KOnlineTableThemeColorButton(drawing::Color::Accent3, "Accent3", this));

	addButton(new KOnlineTableThemeColorButton(drawing::Color::Accent4, "Accent4", this));

	addButton(new KOnlineTableThemeColorButton(drawing::Color::Accent5, "Accent5", this));

	addButton(new KOnlineTableThemeColorButton(drawing::Color::Accent6, "Accent6", this));

	KSeparatorLine* sep = new KSeparatorLine(this, Qt::Vertical);
	sep->setMargin(KLiteStyle::dpiScaledMargins(0, 5, 0, 5));
	layout->addWidget(sep);

	m_customButton = new KOnlineTableThemeColorButton(drawing::Color(), "Custom Color", this);
	m_customButton->setIsCustom(true);
	QIcon customIcon;
	auto loadIcon = [&](const QString& filePath, QIcon::Mode mode) {
		QSvgRenderer render(filePath);
		QPixmap customBtnPixmap(KLiteStyle::dpiScaledSize(render.defaultSize()));
		customBtnPixmap.fill(Qt::transparent);
		QPainter painter(&customBtnPixmap);
		render.render(&painter);
		customIcon.addPixmap(customBtnPixmap, mode);
	};
	QString btnPrefix = KDocerUtils::isDarkSkin() ? QLatin1String("custom_dark") : QLatin1String("custom");
	QString iconPath = QLatin1String(":/icons_svg/24x24/%1%2.svg");
	loadIcon(iconPath.arg(btnPrefix).arg(""), QIcon::Mode::Normal);
	loadIcon(iconPath.arg(btnPrefix).arg("_hover"), QIcon::Mode::Active);
	loadIcon(iconPath.arg(btnPrefix).arg("_pressed"), QIcon::Mode::Selected);
	m_customButton->setIcon(customIcon);
	connect(m_customButton, &KOnlineTableThemeColorButton::colorChange, this, &KOnlineTableThemeColorPane::onCustomBtnClick);
	connect(m_customButton, &KOnlineTableThemeColorButton::previewBegin, this, &KOnlineTableThemeColorPane::onCustomPreviewBegin);
	connect(m_customButton, &KOnlineTableThemeColorButton::previewEnd, this, &KOnlineTableThemeColorPane::onCustomPreviewEnd);
	m_customButton->setProperty("qtspyName", m_customButton->text());
	layout->addWidget(m_customButton);
	m_buttons.append(m_customButton);

	setLayout(layout);
}

KOnlineTableThemeColorPane::~KOnlineTableThemeColorPane()
{
	emit previewEnd();
	emit themeColorPaneStatus(false);
}

void KOnlineTableThemeColorPane::init(const drawing::Color& color)
{
	onColorChange(color);
}

void KOnlineTableThemeColorPane::leaveEvent(QEvent* event)
{
	m_previewEndTimer->stop();
	m_previewBeginTimer->stop();
	m_previewEndTimer->stop();
	m_customPreviewBeginTimer->stop();
	if (!m_popupWidget || !m_popupWidget->isVisible())
	{
		m_hoverColor = drawing::Color::PhColor;
		updateHoverState(false);
		emit previewEnd();
	}
}

void KOnlineTableThemeColorPane::showEvent(QShowEvent* event)
{
	QWidget::showEvent(event);

	auto reportInfo = getReportInfo(false);
	reportInfo.elementName = QLatin1String("0");
	reportInfo.elementType = QLatin1String("module");
	KTableStyleInfoCollect::postInsertDisplayEvent(reportInfo);
}

bool KOnlineTableThemeColorPane::eventFilter(QObject* watched, QEvent* event)
{
	if (event->type() == KEvent::Completed && watched == m_popupWidget)
	{
		event->setAccepted(true);
	}
	else if (m_popupWidget && m_popupWidget->isVisible())
	{
		bool bUnsetPopup = false;
		if (QEvent::MouseMove == event->type()
			|| QEvent::MouseButtonPress == event->type()
			|| QEvent::MouseButtonDblClick == event->type()
			|| QEvent::MouseButtonRelease == event->type())
		{
			QMouseEvent* me = static_cast<QMouseEvent*>(event);
			bool moveOverButton = false;
			auto mousePos = me->globalPos();
			if (m_customButton)
			{
				auto geo = m_customButton->geometry();
				geo.moveTo(m_customButton->mapToGlobal(QPoint(0, 0)));
				moveOverButton = geo.contains(mousePos);
			}
			if (moveOverButton)
			{
				m_customButton->setDown(me->type() == QEvent::MouseButtonPress);
				if (me->type() == QEvent::MouseButtonRelease && me->button() == Qt::LeftButton)
					m_customButton->onClick();
				event->accept();
				return true;
			}
			bUnsetPopup = true;
		}
		else if (QEvent::Leave == event->type() && watched == m_popupWidget)
			bUnsetPopup = true;
		if (bUnsetPopup)
			m_delayClosePopupTimer->start();
	}
	return QWidget::eventFilter(watched, event);
}

void KOnlineTableThemeColorPane::onColorChange(const drawing::Color& color)
{
	if (m_clickColor != color)
		m_clickColor = color;
	else
		m_clickColor = drawing::Color();
	m_previewBeginTimer->stop();
	updateSelectIndex(m_clickColor);
	update();
	emit colorChange(m_clickColor);
}

void KOnlineTableThemeColorPane::onPreViewBegin(const drawing::Color& color)
{
	auto reportInfo = getReportInfo(false);
	QString colorType = "dark1";
	if (color.type() == drawing::Color::Scheme)
		colorType = "color" % QString::number(color.getScheme() - drawing::Color::SchemeColor::Accent1 + 1);
	reportInfo.args["color_type"] = colorType;
	reportInfo.args["color_hex"] = KThemeColorItem(color).toQColor().name().toUpper();
	KTableStyleInfoCollect::postInsertButtonHover(reportInfo);
	if (m_clickColor == color)
	{
		m_hoverColor = color;
		updateHoverState(false);
		return;
	}
	m_hoverColor = color;
	m_previewBeginTimer->start(300);
	m_previewEndTimer->stop();
}

void KOnlineTableThemeColorPane::onPreviewEnd()
{
	m_previewEndTimer->start(150);
	m_previewBeginTimer->stop();
}

void KOnlineTableThemeColorPane::onPreviewBeginTimeOut()
{
	m_previewBeginTimer->stop();
	m_previewEndTimer->stop();
	updateHoverState(false);
	emit previewBegin(m_hoverColor);
}

void KOnlineTableThemeColorPane::onColorButtonClicked(const drawing::Color& color)
{
	auto reportInfo = getReportInfo(false);
	QString colorType = "dark1";
	if (color.type() == drawing::Color::Scheme)
		colorType = "color" % QString::number(color.getScheme() - drawing::Color::SchemeColor::Accent1 + 1);
	reportInfo.args["color_type"] = colorType;
	reportInfo.args["color_hex"] = KThemeColorItem(color).toQColor().name().toUpper();
	reportInfo.args["result"] = m_clickColor.isEmpty() ? QLatin1String("cancel") : QLatin1String("use");
	KTableStyleInfoCollect::postInsertButtonClick(reportInfo);
}

void KOnlineTableThemeColorPane::onCustomBtnClick(const drawing::Color& color)
{
	auto reportInfo = getReportInfo(true);
	if (!color.isEmpty())
	{
		bool bCancel = m_clickColor == color;
		reportInfo.args["result"] = bCancel ? QLatin1String("cancel") : QLatin1String("use");
		reportInfo.args["color_hex"] = KxOnlineTableResHelper::qColorToHex(color.toRgb());
		KTableStyleInfoCollect::postInsertButtonClick(reportInfo);
		onColorChange(color);
	}
	else
	{
		reportInfo.args["result"] = QLatin1String("open_panel");
		KTableStyleInfoCollect::postInsertButtonClick(reportInfo);
	}
}

void KOnlineTableThemeColorPane::onCustomPreviewBegin(const drawing::Color& color)
{
	if (m_popupWidget && m_popupWidget->isVisible())
		return;
	auto reportInfo = getReportInfo(true);
	KTableStyleInfoCollect::postInsertButtonHover(reportInfo);
	m_customPreviewBeginTimer->start(300);
}

void KOnlineTableThemeColorPane::onCustomPreviewEnd()
{
	m_customPreviewBeginTimer->stop();
}

void KOnlineTableThemeColorPane::onCustomPreviewBeginTimeOut()
{
	updateHoverState(true);
	popupCustomWidget();
}

void KOnlineTableThemeColorPane::onDelayClosePopupWidget()
{
	if (!m_popupWidget)
		return;

	QPoint pos = QCursor().pos();
	QRect btnGeometry;
	if (m_customButton)
	{
		btnGeometry = m_customButton->geometry();
		btnGeometry.moveTo(m_customButton->mapToGlobal(QPoint(0, 0)));
	}
	if (m_popupWidget->geometry().contains(pos) || 
		(btnGeometry.isValid() && btnGeometry.contains(pos)))
		return;

	m_popupWidget->close();
}

void KOnlineTableThemeColorPane::onCustomItemClick(int idx)
{
	KGalleryModelAbstractItem* item = m_customModel->element(idx);
	if (!item)
		return;
	m_customModel->clearSelectStatus();
	if (KxColorItem* colorItem = qobject_cast<KxColorItem*>(item))
	{
		drawing::Color clr = drawing::Color::fromRgb(colorItem->getColor().rgb());
		m_customButton->setColor(clr);
		onColorChange(clr);

		QString type = QLatin1String("colorpanel");
		if (colorItem->getColorItemType() == KxColorItemType::None)
			type = QLatin1String("recent");
		KTableStyleInfoCollect::postInsertCustomColor(getReportInfo(true), type, colorItem->getColor());
	}
	else if (KxMoreColorItem* moreItem = dynamic_cast<KxMoreColorItem*>(item))
		emit sigMoreColorClicked();
	else if (KxSnapperColorItem* napItem = dynamic_cast<KxSnapperColorItem*>(item))
		emit sigSnapColorClicked();
}

void KOnlineTableThemeColorPane::onPreviewEndTimeOut()
{
	m_previewEndTimer->stop();
	m_previewBeginTimer->stop();
	m_hoverColor = drawing::Color::PhColor;
	updateHoverState(false);
	emit previewEnd();
}

void KOnlineTableThemeColorPane::updateSelectIndex(const drawing::Color& color)
{
	if (color.type() != drawing::Color::Scheme)
	{
		if (color.type() == drawing::Color::Preset)
			m_selectButtonIndex = 0;
		else if (color.isEmpty())
			m_selectButtonIndex = -1;
		else
		{
			m_customButton->setColor(color);
			m_selectButtonIndex = 7;
		}
	}
	else
	{
		switch (color.getScheme())
		{
		case drawing::Color::Accent1:
			m_selectButtonIndex = 1;
			break;
		case drawing::Color::Accent2:
			m_selectButtonIndex = 2;
			break;
		case drawing::Color::Accent3:
			m_selectButtonIndex = 3;
			break;
		case drawing::Color::Accent4:
			m_selectButtonIndex = 4;
			break;
		case drawing::Color::Accent5:
			m_selectButtonIndex = 5;
			break;
		case drawing::Color::Accent6:
			m_selectButtonIndex = 6;
			break;
		default:
			m_selectButtonIndex = 0;
			break;
		}
	}

	for (int i = 0; i < m_buttons.size(); ++i)
	{
		m_buttons[i]->setSelectedState(i == m_selectButtonIndex);
	}
}

void KOnlineTableThemeColorPane::updateHoverState(bool bCustom)
{
	for (int i = 0; i < m_buttons.size(); ++i)
	{
		if (bCustom)
			m_buttons[i]->setHoverState(m_buttons[i]->isCustom());
		else
			m_buttons[i]->setHoverState(m_hoverColor == m_buttons[i]->getColor() && (!m_buttons[i]->isCustom() || !m_buttons[i]->getColor().isEmpty()));
		m_buttons[i]->update();
	}
}

void KOnlineTableThemeColorPane::clearShortCut()
{
	auto shortCuts = m_shortcuts;
	for (auto item : shortCuts)
	{
		if(item)
			delete item;
	}
	m_shortcuts.clear();
}

void KOnlineTableThemeColorPane::resetShortCut()
{
	clearShortCut();

	QShortcut* moreShortCut = new QShortcut(m_popupWidget);
	moreShortCut->setKey(Qt::Key_M);
	connect(moreShortCut, &QShortcut::activated, this, &KOnlineTableThemeColorPane::sigMoreColorClicked);
	m_shortcuts.append(moreShortCut);

	QShortcut* snapShortCut = new QShortcut(m_popupWidget);
	snapShortCut->setKey(Qt::Key_E);
	connect(snapShortCut, &QShortcut::activated, this, &KOnlineTableThemeColorPane::sigSnapColorClicked);
	m_shortcuts.append(snapShortCut);
}

void KOnlineTableThemeColorPane::popupCustomWidget()
{
	m_customPreviewBeginTimer->stop();

	if (!m_popupWidget || !m_customButton || m_popupWidget->isVisible())
		return;

	if (KApplyFuncWrapper::isWppPlaying(this))
		return;

	if (m_customView)
	{
		m_popupWidget->setContentWidget(nullptr);
		delete m_customView;
		m_customView = nullptr;
	}

	if (m_customModel)
	{
		delete m_customModel;
		m_customModel = nullptr;
	}

	KxInsertColorModel* model = new KxInsertColorModel(KDocerUtils::getCoreApplication(this), this);
	connect(model, &KGalleryAbstractModel::indexClicked, this, &KOnlineTableThemeColorPane::onCustomItemClick);
	model->fetchItems();
	if (!m_customButton->getColor().isEmpty())
		model->checkColor(m_customButton->getColor().toRgb());
	m_customModel = model;

	m_customView = new KScrollGalleryView(m_customModel, this);
	m_customView->hide();
	m_popupWidget->setContentWidget(m_customView);
	KLiteShadowBorder* border = m_popupWidget->findChild<KLiteShadowBorder*>(QString(), Qt::FindDirectChildrenOnly);
	if (border)
		border->installEventFilter(this);

	if (model && !m_customButton->getColor().isEmpty())
	{
		auto item = model->getItemByThemeColor(KThemeColorItem(m_customButton->getColor()));
		if (!item)
		{
			model->setCurrentIndex(model->indexOf(item));
		}
	}

	m_popupWidget->contentWidget()->setAttribute(Qt::WA_Resized, false);
	if (m_customView)//ones:647451
	{
		m_customView->setAttribute(Qt::WA_Resized, false);
	}
	m_popupWidget->setAttribute(Qt::WA_Resized, false);		//更新GalleryView时，需把m_popupWidget的WA_Resized初始化,保证弹出控件弹出位置计算不出错。

	KGalleryView* pGalleryView = nullptr;
	if (m_customView && (pGalleryView = m_customView->galleryView()))
	{
		pGalleryView->setAttribute(Qt::WA_Resized, false);
		pGalleryView->setColumnNum(Theme_Default_Col);
		pGalleryView->renderWidgets();
	}

	resetShortCut();
	QRect rect = m_customButton->rect();
	QPoint pos = QPoint(m_customButton->mapToGlobal(rect.bottomRight()).x() - m_popupWidget->getPreferSize().width(), m_customButton->mapToGlobal(rect.bottomLeft()).y());
	auto cefPopup = KTik::findParentByType<KPopupCefWidget>(parent());
	if (cefPopup)
		cefPopup->setProperty("forceStay", true);
	emit sigCustomColorPopup();
	m_popupWidget->exec(this, pos);
	emit sigCustomColorHide();
	if (cefPopup)
		cefPopup->setProperty("forceStay", false);
	updateHoverState(false);
	clearShortCut();
}

KTableStyleInfoCollect::KTableReportInfo KOnlineTableThemeColorPane::getReportInfo(bool bCustomColor)
{
	KTableStyleInfoCollect::KTableReportInfo reportInfo;
	reportInfo.func = QLatin1String("docer_inserttable");
	reportInfo.firstEntry = QLatin1String("insert_table");
	reportInfo.pageName = QLatin1String("inserttable_dropdown_page");
	reportInfo.moduleName = QLatin1String("choose_color");
	reportInfo.elementName = bCustomColor ? QLatin1String("costumize_color") : QLatin1String("theme_color");
	reportInfo.elementType = QLatin1String("button");
	return reportInfo;
}

KxInsertTableStyleContainer::KxInsertTableStyleContainer(QWidget* parent, KGalleryCommand* styleCmd, KxListCommand* listCmd)
	:QWidget(parent),
	m_menuCmd(listCmd),
	m_styleCmd(styleCmd)
{
	init();
	connect(m_styleCmd, SIGNAL(sigInsertFinished(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)),
		this, SLOT(onInsertFinished(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)));
}

KxInsertTableStyleContainer::~KxInsertTableStyleContainer()
{
}

QSize KxInsertTableStyleContainer::sizeHint() const
{
	QSize fixSize;
	auto mainWnd = kxApp->findRelativeMainWindowX((QObject*)this);
	if (mainWnd)
		fixSize = KPluginLoadWidget::getDocerWidgetAdaptiveSize(mainWnd, "konlinetablestyle_insert");
	return fixSize;
}

void KxInsertTableStyleContainer::closePopupWidget()
{
	KPopupWidget* popupWidget = nullptr;
	QWidget* widget = parentWidget();
	if (widget)
	{
		if (popupWidget = KTik::findParentByType<KPopupWidget>(widget))
		{
			KEvent event(KEvent::Completed);
			sendPropagatedEvent(popupWidget, &event);
		}
	}
}

QWidget* KxInsertTableStyleContainer::createGalleryWidget()
{
	QWidget* galleryWidget = new QWidget(this);

	QVBoxLayout* vLayout = new QVBoxLayout(galleryWidget);
	vLayout->setSpacing(0);
	vLayout->setContentsMargins(0, 0, 0, 0);

	vLayout->addSpacing(KLiteStyle::dpiScaled(11));
	KSeparatorLine* pLine = new KSeparatorLine(galleryWidget);
	pLine->setMargin(KLiteStyle::dpiScaledMargins(15, 0, 16, 0));
	vLayout->addWidget(pLine);
	vLayout->addSpacing(KLiteStyle::dpiScaled(15));

	auto colorPane = createThemeColorPane(galleryWidget);
	colorPane->setContentsMargins(KLiteStyle::dpiScaledMargins(2, 0, 2, 0));
	vLayout->addWidget(colorPane);
	vLayout->addSpacing(KLiteStyle::dpiScaled(6));

	if (m_styleCmd->dataSource())
	{
		KxInsertTableModel* model = qobject_cast<KxInsertTableModel*>(m_styleCmd->dataSource());
		if (model)
		{
			m_styleCmd->dataSource()->setColumns(4);
			connect(model, &KxInsertTableModel::stateChanged, this, &KxInsertTableStyleContainer::onStateChanged);
			connect(m_styleCmd, SIGNAL(sendInsertInfo(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&))
				, this, SLOT(onInsertFinished(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)));
			connect(model, &KxInsertTableModel::sigClosePopupWidget, this, &KxInsertTableStyleContainer::onClosePopupWidget);

			KxInsertScrollView* galleryView = new KxInsertScrollView(model, galleryWidget);
			vLayout->addWidget(galleryView);
			if (galleryView->scrollBar())
			{
				m_scrollMaxValue = galleryView->scrollBar()->value();
				connect(galleryView->scrollBar(), &QScrollBar::valueChanged,
					this, [=](int value) {
						if(value > m_scrollMaxValue)
						{
							m_scrollMaxValue = value;
							m_scrollCount++;
						}
					});
			}
		}
	}
	vLayout->addSpacing(KLiteStyle::dpiScaled(8));
	KInsertTableMenuWidget* menuWidget = new KInsertTableMenuWidget(m_menuCmd, galleryWidget);
	menuWidget->setProperty("qtspyName", "qn_insert_insertContentTableMenu");
	menuWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
	menuWidget->setFixedHeight(KLiteStyle::dpiScaled(32));
	menuWidget->setContentsMargins(KLiteStyle::dpiScaledMargins(6, 1, 12, 1));
	vLayout->addWidget(menuWidget);
	vLayout->addSpacing(KLiteStyle::dpiScaled(8));
	galleryWidget->setLayout(vLayout);

	return galleryWidget;
}

QWidget* KxInsertTableStyleContainer::createLoadingWidget(QWidget* parent)
{
	return KPluginLoadWidget::createPluginLoadWidget(this, "konlinetablestyle_insert");
}

QWidget* KxInsertTableStyleContainer::createErrorWidget(QWidget* parent, const QString& text)
{
	auto _getTextRectWidth = [=](const QString& text) -> int {
		QFont fnt;
		fnt.setPixelSize(KLiteStyle::dpiScaled(14));
		QFontMetrics metrics(fnt);
		return metrics.width(text);
	};

	QWidget* widget = new QWidget(parent);
	QHBoxLayout* hLayout = new QHBoxLayout(widget);
	hLayout->setContentsMargins(0, 0, 0, 0);
	hLayout->setSpacing(KLiteStyle::dpiScaled(4));

	KLiteLabel* textLabel = new KLiteLabel(text, parent);
	textLabel->setFixedHeight(KLiteStyle::dpiScaled(20));
	textLabel->applyThemeClass("KxDocerTableStyleWidget-EmptyState-Text");

	QString btnText = tr("retry now");
	KLiteButton* btnRetry = new KLiteButton(btnText, parent);
	btnRetry->setFixedSize(KLiteStyle::dpiScaledSize(_getTextRectWidth(btnText) + 8 * 2, 26));
	btnRetry->setCursor(Qt::PointingHandCursor);
	btnRetry->applyThemeClass("KLiteButton-ShallowLink");
	btnRetry->setContentsMargins(0, 0, 0, 0);
	connect(btnRetry, &KLiteButton::clicked, this, &KxInsertTableStyleContainer::onRetryClicked);

	hLayout->addStretch();
	hLayout->addWidget(textLabel, 0, Qt::AlignCenter);
	hLayout->addWidget(btnRetry, 0, Qt::AlignCenter);
	hLayout->addStretch();

	QWidget* w = new QWidget(parent);
	QVBoxLayout* layout = new QVBoxLayout(w);
	layout->setContentsMargins(0, 0, 0, 0);
	layout->setSpacing(0);
	layout->addStretch();
	layout->addWidget(widget);
	layout->addStretch();
	return w;
}

QWidget* KxInsertTableStyleContainer::createThemeColorPane(QWidget* parentWidget)
{
	QWidget* pWidget = new QWidget(parentWidget);

	KLiteLabel* label = new KLiteLabel(pWidget);

	label->setText(tr("select Scheme Color"));
	label->setFocusPolicy(Qt::NoFocus);

	KOnlineTableThemeColorPane* pThemeColorPane = new KOnlineTableThemeColorPane(pWidget);
	m_styleCmd->setThemeColorPaneStatus(true);
	connect(pThemeColorPane, &KOnlineTableThemeColorPane::colorBtnClicked, m_styleCmd, &KGalleryCommand::onColorBtnClicked);

#ifdef Q_OS_MACOS
	connect(pThemeColorPane, SIGNAL(colorChange(const ksodrawing::Color&)), m_styleCmd, SLOT(onColorChange(const ksodrawing::Color&)));
#else
	connect(pThemeColorPane, &KOnlineTableThemeColorPane::colorChange, m_styleCmd, &KGalleryCommand::onColorChange);
#endif

	connect(pThemeColorPane, &KOnlineTableThemeColorPane::previewBegin, m_styleCmd, &KGalleryCommand::onPreviewBegin);
	connect(pThemeColorPane, &KOnlineTableThemeColorPane::previewEnd, m_styleCmd, &KGalleryCommand::onPreviewEnd);
	connect(pThemeColorPane, &KOnlineTableThemeColorPane::themeColorPaneStatus, m_styleCmd, &KGalleryCommand::setThemeColorPaneStatus);
	connect(pThemeColorPane, &KOnlineTableThemeColorPane::sigMoreColorClicked, this, &KxInsertTableStyleContainer::sigMoreColorClicked);
	connect(pThemeColorPane, &KOnlineTableThemeColorPane::sigSnapColorClicked, this, &KxInsertTableStyleContainer::sigSnapColorClicked);

	pThemeColorPane->init(m_styleCmd->getInitThemeColorPaneColor());
	pThemeColorPane->setObjectName("themeColorPane");

	pThemeColorPane->layout()->setContentsMargins(0, 0, 0, 0);
	pThemeColorPane->setFixedHeight(KWPSStyle::pixelMetric(style(), KWPSStyle::PM_WpsTableStylePopupColorPaneHeight));
	QHBoxLayout* Hlayout = new QHBoxLayout(pWidget);
	Hlayout->setContentsMargins(KLiteStyle::dpiScaledMargins(14, 0, 12, 0));
	Hlayout->addWidget(label, Qt::AlignVCenter | Qt::AlignLeft);
	Hlayout->addWidget(pThemeColorPane, Qt::AlignVCenter | Qt::AlignRight);

	return pWidget;
}

void KxInsertTableStyleContainer::showEvent(QShowEvent* event)
{
	QWidget::showEvent(event);
	if(!m_bReportShowed)
	{
		KTableStyleInfoCollect::postInsertDisplayEvent(getReportInfo());
		m_showTime = QDateTime::currentDateTime();
		m_bReportShowed = true;
	}
}

void KxInsertTableStyleContainer::hideEvent(QHideEvent* event)
{
	QWidget::hideEvent(event);
	if(m_bReportShowed)
	{
		if (m_showTime.isValid())
		{
			auto info = getReportInfo();
			info.args["duration"] = QString::number(QDateTime::currentMSecsSinceEpoch() - m_showTime.toMSecsSinceEpoch());
			KTableStyleInfoCollect::postKLMEvent(QLatin1String("docer_inserttable_stay"), info, true, true, true);

			info = getReportInfo();
			info.args["scroll_number"] = QString::number(m_scrollCount);
			KTableStyleInfoCollect::postKLMEvent(QLatin1String("docer_inserttable_scroll"), info, true, true, true);
		}
		m_showTime = QDateTime();
	}
}

void KxInsertTableStyleContainer::keyPressEvent(QKeyEvent* event)
{
	Qt::KeyboardModifiers modifiers = event->modifiers();
	modifiers |= Qt::AltModifier;
	auto galleryWidget = KTik::findParentByType<KGallery>(parent());
	if (galleryWidget)
	{
		QKeyEvent e(event->type(), event->key(), modifiers, event->text());
		QCoreApplication::sendEvent(galleryWidget, &e);
	}
	QWidget::keyPressEvent(event);
}

KTableStyleInfoCollect::KTableReportInfo KxInsertTableStyleContainer::getReportInfo()
{
	KTableStyleInfoCollect::KTableReportInfo reportInfo;
	reportInfo.func = QLatin1String("docer_inserttable");
	reportInfo.firstEntry = QLatin1String("insert_table");
	reportInfo.pageName = QLatin1String("inserttable_dropdown_page");
	reportInfo.moduleName = QLatin1String("0");
	reportInfo.elementType = QLatin1String("page");
	return reportInfo;
}

void KxInsertTableStyleContainer::onRetryClicked()
{
	if(m_styleCmd->dataSource())
	{
		m_styleCmd->dataSource()->setDirty(true);
		m_styleCmd->dataSource()->fetchItems();
		kxApp->ForceIdle();
	}
}

void KxInsertTableStyleContainer::onStateChanged(int state)
{
	if (state == KxInsertTableModel::State::Loading)
	{
		m_loadTime = QDateTime::currentDateTime();
	}
	else
	{
		if (m_loadTime.isValid())
		{
			bool bSuccess = state == KxInsertTableModel::State::GalleryItem;
			auto info = getReportInfo();
			info.args["duration"] = QString::number(QDateTime::currentMSecsSinceEpoch() - m_loadTime.toMSecsSinceEpoch());
			info.args["load_status"] = bSuccess ? QLatin1String("success") : QLatin1String("fail");
			QString errorCode;
			if (state == KxInsertTableModel::State::NoInternet)
				errorCode = "no internet";
			else if (state == KxInsertTableModel::State::NoResource)
				errorCode = "no resource";
			info.args["error_code"] = errorCode;
			KTableStyleInfoCollect::postKLMEvent(QLatin1String("docer_inserttable_load"), info, true, true, true);
			m_loadTime = QDateTime();
		}
	}
	m_resourceLayout->setCurrentIndex(state);
	kxApp->ForceIdle();
}

void KxInsertTableStyleContainer::onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc)
{
	KDocerLiteToastHelper::KDocerLiteToastParam toastParam;
	toastParam.host = this;
	toastParam.align = Qt::AlignCenter | Qt::AlignTop;
	toastParam.bDefaultCloseBtnVisible = false;
	toastParam.stayDuration = 1500;

	switch (errorCode)
	{
	case Success:
		toastParam.msgType = KLiteToastWidget::MessageType_Success;
		toastParam.text = tr("Apply success");
		break;
	case InsertOpNoActiveTable:
		toastParam.msgType = KLiteToastWidget::MessageType_Warning;
		toastParam.text = tr("Please select the table area and try again");
		break;
	case DownloadError:
		toastParam.msgType = KLiteToastWidget::MessageType_Warning;
		toastParam.text = tr("Network exception, please try again later");
		break;
	default:
		toastParam.msgType = KLiteToastWidget::MessageType_Warning;
		toastParam.text = tr("Application failed. Please try again later");
	}

	KDocerLiteToastHelper::showTip(toastParam);
	if (errorCode == Success)
		closePopupWidget();
}

void KxInsertTableStyleContainer::onClosePopupWidget()
{
	closePopupWidget();
}

void KxInsertTableStyleContainer::init()
{
	m_resourceLayout = new QStackedLayout(this);
	m_resourceLayout->insertWidget(KxInsertTableModel::State::Loading, createLoadingWidget(this));
	m_resourceLayout->insertWidget(KxInsertTableModel::State::GalleryItem, createGalleryWidget());
	m_resourceLayout->insertWidget(KxInsertTableModel::State::NoResource, createErrorWidget(this, tr("no resource")));
	m_resourceLayout->insertWidget(KxInsertTableModel::State::NoInternet, createErrorWidget(this, tr("resource load failed")));

	auto state = KxInsertTableModel::State::Loading;
	if (m_styleCmd->dataSource())
	{
		KxInsertTableModel* model = qobject_cast<KxInsertTableModel*>(m_styleCmd->dataSource());
		if (model)
			state = model->getStatus();
	}
	onStateChanged(state);

	setLayout(m_resourceLayout);
}

KInsertTableMenuWidget::KInsertTableMenuWidget(KCommand* cc, QWidget* parent, bool isVLayout /*= true*/)
	: KMenuWidget(cc, parent, isVLayout)
{
	connect(this, &KInsertTableMenuWidget::showPopups, this, &KInsertTableMenuWidget::onPopup);
	connect(&m_delayUnsetPopupTimer, &QTimer::timeout, this, &KInsertTableMenuWidget::delayUnsetPopup);
	connect(this, &KInsertTableMenuWidget::hidePopups, this, &KInsertTableMenuWidget::unsetPopup, Qt::QueuedConnection);
	connect(this, &KInsertTableMenuWidget::itemTriggered, this, &KInsertTableMenuWidget::onItemTrigger);
	connect(&m_timer, &QTimer::timeout, this, &KInsertTableMenuWidget::onHoverTimerOut);
	m_delayUnsetPopupTimer.setInterval(100);
	m_delayUnsetPopupTimer.setSingleShot(true);
}

KInsertTableMenuWidget::~KInsertTableMenuWidget()
{
	int cnt = count();
	for (int i = 0; i < cnt; i++)
	{
		auto item = itemAt(i);
		if(!item)
			continue;
		KMenuWidgetPopupItem* popupItem = qobject_cast<KMenuWidgetPopupItem*>(item);
		if(!popupItem || !popupItem->getPopupWidget())
			continue;
		popupItem->getPopupWidget()->removeEventFilter(this);
	}
}

void KInsertTableMenuWidget::setVisible(bool visible)
{
	if (!m_bInstallFilter) {
		if (auto popup = KTik::findParentByType<KPopupWidget>(this))
		{
			auto lstChildren = popup->findChildren<QWidget*>();
			foreach(auto w, lstChildren)
			{
				if (qobject_cast<KGalleryView*>(w) ||
					qobject_cast<KxInsertTableStyleContainer*>(w) ||
					qobject_cast<KMenuWidget*>(w))
					w->installEventFilter(this);
			}
		}
		m_bInstallFilter = true;
		KTableStyleInfoCollect::postKLMEvent("docer_inserttable_display", getReportInfo(), false, false, true);
	};
	KMenuWidget::setVisible(visible);
}

bool KInsertTableMenuWidget::eventFilter(QObject* watched, QEvent* e)
{
	if (isPopuping())
	{
		bool bUnsetPopup = false;
		if(KEvent::BlockedMouseEvent == e->type())
		{
			do 
			{
				KBlockedMouseEvent* bme = static_cast<KBlockedMouseEvent*>(e);
				if (!bme)
					break;
				auto me = bme->mouseEvent();
				if (!me)
					break;
				if (me->type() != QEvent::MouseMove)
					break;
				bool moveOverMenu = false;
				auto mousePos = me->globalPos();
				if (auto container = KTik::findParentByType<KxInsertTableStyleContainer>(this))
				{
					auto geo = geometry();
					geo.moveTo(mapToGlobal(QPoint(0, 0)));
					//PopupWidget 左右边距
					geo.adjust(KLiteStyle::dpiScaled(-7), 0, KLiteStyle::dpiScaled(6), 0);
					moveOverMenu = geo.contains(mousePos);
				}

				bool clickOnMenu = (QEvent::MouseButtonRelease == me->type() && watched == this);
				if (moveOverMenu || clickOnMenu)
				{
					e->accept();
					return true;
				}
				bUnsetPopup = true;
			} while (false);
			
		}
		else if(QEvent::Leave == e->type())
		{
			KMenuWidgetPopupItem* popupItem = qobject_cast<KMenuWidgetPopupItem*>(m_popupItem);
			if (popupItem && popupItem->getPopupWidget() && watched == popupItem->getPopupWidget())
				bUnsetPopup = true;
		}
		else if (QEvent::Enter == e->type())
		{
			KMenuWidgetPopupItem* popupItem = qobject_cast<KMenuWidgetPopupItem*>(m_popupItem);
			if (popupItem &&
				popupItem->getPopupWidget() &&
				(watched != this && watched != popupItem->getPopupWidget()))
				bUnsetPopup = true;
		}

		if (bUnsetPopup == true)
			m_delayUnsetPopupTimer.start();
	}
	return KMenuWidget::eventFilter(watched, e);
}

void KInsertTableMenuWidget::hideEvent(QHideEvent* e)
{
	unsetPopup();
	KMenuWidget::hideEvent(e);
}

KTableStyleInfoCollect::KTableReportInfo KInsertTableMenuWidget::getReportInfo()
{
	KTableStyleInfoCollect::KTableReportInfo reportInfo;
	reportInfo.func = QLatin1String("docer_inserttable");
	reportInfo.firstEntry = QLatin1String("insert_table");
	reportInfo.elementName = QLatin1String("content_table_entry");
	reportInfo.pageName = QLatin1String("inserttable_dropdown_page");
	reportInfo.moduleName = QLatin1String("resource_list");
	reportInfo.elementType = QLatin1String("button");
	return reportInfo;
}

void KInsertTableMenuWidget::onPopup()
{
	if (!m_popupItem)
		return;
	KMenuWidgetPopupItem* popupItem = qobject_cast<KMenuWidgetPopupItem*>(m_popupItem);
	if (popupItem && popupItem->getPopupWidget())
		popupItem->getPopupWidget()->installEventFilter(this);
}

void KInsertTableMenuWidget::onHoverTimerOut()
{
	KTableStyleInfoCollect::postKLMEvent("docer_inserttable_hover", getReportInfo(), false, false, true);
}

void KInsertTableMenuWidget::onItemTrigger(KMenuWidgetItem* item)
{
	KTableStyleInfoCollect::postKLMEvent("docer_inserttable_click", getReportInfo(), false, false, true);
}

bool KInsertTableMenuWidget::isPopuping()
{
	return m_popupItem != nullptr;
}

void KInsertTableMenuWidget::unsetPopup()
{
	if (m_popupItem)
		m_popupItem->unsetPopupItem();
	setCurrentItem(NULL);

	//ones:710221 
	//二级菜单隐藏后，Qt内部会将SetCapture重置为上一个Popup，导致上个Popup的CEF窗口无法收到任何鼠标消息
#ifdef Q_OS_WIN
	QMetaObject::invokeMethod(this, []() {
		::ReleaseCapture();
		}, Qt::QueuedConnection);
#endif
}

void KInsertTableMenuWidget::delayUnsetPopup()
{
	auto popup = findChild<KPopupWidget*>();
	if (!popup)
	{
		unsetPopup();
		return;
	}

	QRect menuGeometry = geometry();
	menuGeometry.moveTo(mapToGlobal(QPoint(0, 0)));

	QPoint pos = QCursor().pos();
	if (popup->geometry().contains(pos) || menuGeometry.contains(pos))
		return;

	unsetPopup();
}

#include "kinsertonlinetablewidget.moc"
