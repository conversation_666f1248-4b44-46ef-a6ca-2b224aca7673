﻿#include "stdafx.h"
#include "kxtponlinetablestylewidget.h"
#include "kxonlinetablestylejsapi.h"
#include "kxonlinereshelper.h"
#include <kxshare/kxdocerunifypaydlgproxy.h>
#include <kcomctl/kquickhelpbarwidget.h>
#include <ksolite/kcommonwebwidget.h>
#include <ksolite/kxjsonhelper.h>
#include <ksolite/kdocer/kdocerjsapihost.h>
#include <ksolite/kwebview/jsapi/kxwebviewjsapidef.h>
#include <krt/product.h>
#include <auth/productinfo.h>
#include <krt/dirs.h>
#include <ksolite/kdcinfoc.h>
#include "kdocertoolkit/docerfeature/kxdocertpfeatureutil.h"
#include "kdocertoolkit/kdocerutils.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include "kdocertoolkit/wpp/kxdocerwppapi.h"
#include "common/base/algorithm/kdocerdschartrule.h"
#include "kdocerbasehelper.h"
#include "qttools/ksafe_process.h"
#include "kso/api/apiex_old.h"
#include "kdocerencryptdef.h"
#include <ksolite/kcrypt/krsaencrypt.h>
#include "common/toolkit/algorithm/kxdocershapenotify.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "ksolite/kdocer/kdocerjsapiserviceloader.h"

namespace
{
#ifdef WPP_PROJECT
	const QString kPasteAction("paste");
	const QString kTableSmartStyle("tablesmartstyle");
#endif
	const QString strPluginName("konlinewpptablestyle");

	QVariantList toVariantList(const QStringList& list)
	{
		QVariantList variantList;
		foreach (const QString& item, list)
			variantList.append(item);

		return variantList;
	}

	bool isPluginVersionNew(const QString& pluginName, const QString& function, bool isPluginDs, const QString& relativeCfgPath = "./cfg.ini")
	{
#ifdef Q_OS_WIN
		QString pluginPath;
		if (isPluginDs)
		{
			pluginPath = KDocerJsServiceLoader::instance().dllPath();
			QFileInfo fileInfo(pluginPath);
			if (!fileInfo.isDir())
				pluginPath = fileInfo.absolutePath();
		}
		if (!pluginPath.isEmpty())
		{
			pluginPath.append(QDir::separator());
			pluginPath.append(pluginName);
			pluginPath.append(".dll");
		}
		else if (!KDocerUtils::findLocalPlugin(pluginName, pluginPath))
		{
			pluginPath.clear();
		}

		if (!pluginPath.isEmpty())
		{
			QFileInfo fileInfo(pluginPath);
			pluginPath = fileInfo.absolutePath();
			pluginPath.append(QDir::separator());
			QString cfgPath = pluginPath + relativeCfgPath;
			if (QFileInfo(cfgPath).exists())
			{
				QSettings settings(cfgPath, QSettings::IniFormat);
				QString value = settings.value(function, "0").toString();
				if (value == "1")
					return true;
			}
		}
		return false;
#else
		return false; // TODO linux
#endif
	}
}

using namespace KxOnlineTableResHelper;

const char* KOnlineTableStyleMainResuorce = "style.tablestyle";

ksolite::KxCommonJsApi* createJsApiFunc(KxWebViewContainer* webView)
{
	return new KxOnlineTableStyleJsApi(webView, NULL);
}

KxOnlineTableStyleJsApi::~KxOnlineTableStyleJsApi()
{
	if (kxApp)
	  kxApp->removeEventFilter(this);
	KxOnlineTableStyleJsApiHelper::Instance().removeApi(this);
	UnRegisterNotifyFilter();
}

void KxOnlineTableStyleJsApi::RegisterNotifyFilter()
{
	listenSelectionChanged();
	if (!kxApp->isCoreAppInited())
		return;

	IKApplication* pApp = KDocerUtils::getCoreApplication(this);
	if (!pApp)
		return;

	pApp->RegisterNotifyFilter(this);
	ks_stdptr<oldapi::_ApplicationEx> spAppEx = pApp;
	if (spAppEx)
		spAppEx->RegisterNotifyFilter(this);

	IKDocuments* pDocuments = pApp->GetDocuments();
	if (pDocuments)
		pDocuments->RegisterNotifyFilter(this);
}

void KxOnlineTableStyleJsApi::UnRegisterNotifyFilter()
{
	if (!kxApp)
		return;

	IKApplication* pApp = KDocerUtils::getCoreApplication(this);
	if (!pApp)
		return;

	pApp->UnRegisterNotifyFilter(this);
	ks_stdptr<oldapi::_ApplicationEx> spAppEx = pApp;
	if (spAppEx)
		spAppEx->UnRegisterNotifyFilter(this);

	IKDocuments* pDocuments = pApp->GetDocuments();
	if (pDocuments)
		pDocuments->UnRegisterNotifyFilter(this);
}

void KxOnlineTableStyleJsApi::canHover(KxWebViewJSContext& context)
{
	setResult(context, QVariant(canHover()));
}

void KxOnlineTableStyleJsApi::webLoadFinish(KxWebViewJSContext& context)
{
	QWidget* pWidget = widgetParent();
	if (pWidget)
	{
		emit showView();
	}
}

void KxOnlineTableStyleJsApi::getAppInfo(KxWebViewJSContext& context)
{
	setResult(context, KxCommonJsApi::getAppInfo());
}

void KxOnlineTableStyleJsApi::openTaskPane(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	QString param = args.value("param").toString();

	if (m_onlineWidget)
		m_onlineWidget->openTaskPane(param);
}

void KxOnlineTableStyleJsApi::insertOnlineResource(KxWebViewJSContext& context)
{
	endPreview();

	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	const QString& strResourceId = QString::fromUtf8(args.value("id").toString().toUtf8().data());
	if (strResourceId.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "input json invalid. no id.";
		return ;
	}

	const QString& strResourceUrl = QUrl::fromPercentEncoding(args.value("resourceUrl").toString().toUtf8().data());
	if (strResourceUrl.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "input json invalid. no url.";
		return ;
	}

	const QString& strTableName = QString::fromUtf8(args.value("name").toString().toUtf8().data());
	if (strTableName.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "no table name.";
		return ;
	}

	const QString& strArea = QString::fromUtf8(args.value("area").toString().toUtf8().data());
	if (strArea.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "no area.";
		return ;
	}

	QString strTableColor = QString::fromUtf8(args.value("color").toString().toUtf8().data());
	if (!strTableColor.isEmpty())
		strTableColor.replace("#", "0x");
	
	QVariantMap broadCastInfo = args.value("boardcastParams").toMap();
	if (!broadCastInfo.isEmpty())
		m_broadcastInfo[strResourceId] = broadCastInfo;

	const QString& strXmlPath = getFileSavePath(strResourceId, QFileInfo(QUrl(strResourceUrl).path()).baseName());
	QFileInfo file(strXmlPath);

	KOTSResourceInfo resourceInfo;
	resourceInfo.id = strResourceId;
	resourceInfo.name = strTableName;
	resourceInfo.color = strTableColor;
	resourceInfo.customColor = QString::fromUtf8(args.value("customColor").toString().toUtf8().data());
	resourceInfo.themeColorKey = args.value("themeColorKey").toInt();
	resourceInfo.area = args.value("area");
	resourceInfo.bHover = false; 
	resourceInfo.bApplyAll = args.value("isApplyAll", false).toBool();
	resourceInfo.bBatchApply = args.value("isBatchApply", false).toBool();
	ReportInfo reportInfo;
	reportInfo.click = true;
	reportInfo.tablestyleId = QString::fromUtf8(args.value("id").toString().toUtf8().data());
	reportInfo.from = QString::fromUtf8(args.value("from").toString().toUtf8().data());
	reportInfo.belongPage = QString::fromUtf8(args.value("belong_page").toString().toUtf8().data());
	reportInfo.userStatusId = QString::fromUtf8(args.value("user_status_id").toString().toUtf8().data());
	reportInfo.sourceStatusId = QString::fromUtf8(args.value("source_staus_id").toString().toUtf8().data());
	reportInfo.downloadKey = QString::fromUtf8(args.value("download_key").toString().toUtf8().data());
	if (file.exists())
	{
		emit insertOnlineResource(resourceInfo, reportInfo, file.absoluteFilePath(), false);
	}
	else
	{
		downloadResouce(resourceInfo, strResourceUrl, strXmlPath, reportInfo);
	}
}

void KxOnlineTableStyleJsApi::hoverOnlineResource(KxWebViewJSContext& context)
{
	endPreview();

	if (!canHover())
	{
		Json::Value res;
		res["isOk"] = false;
		res["nextOp"] = "stop";
		setResult(context, res);
		return;
	}

	if (!KDocerUtils::isEtApp(this) && !KxOnlineTableResHelper::hasActiveTable(this))
	{
		Json::Value res;
		res["isOk"] = false;
#ifdef WPP_PROJECT
		res["nextOp"] = getPreviewNextOp().toStdString().c_str();
#else
		res["nextOp"] = "continue";
#endif
		setResult(context, res);
		return;
	}

	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	const QString& strResourceId = QString::fromUtf8(args.value("id").toString().toUtf8().data());
	if (strResourceId.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "input json invalid. no id.";
		return ;
	}

	const QString& strTableName = QUrl::fromPercentEncoding(args.value("name").toString().toUtf8().data());
	if (strTableName.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "no table name.";
		return ;
	}

	const QString& strPreviewUrl = QString::fromUtf8(args.value("preview_url").toString().toUtf8().data());
	if (strPreviewUrl.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "input json invalid. no url.";
		return;
	}

	QString strTableColor = QString::fromUtf8(args.value("color").toString().toUtf8().data());
	if (!strTableColor.isEmpty())
		strTableColor.replace("#", "0x");
		
	m_hoverOnlineResId = strResourceId;

	KOTSResourceInfo resourceInfo;
	resourceInfo.id = strResourceId;
	resourceInfo.name = strTableName;
	resourceInfo.color = strTableColor;
	resourceInfo.customColor = QString::fromUtf8(args.value("customColor").toString().toUtf8().data());
	resourceInfo.themeColorKey = args.value("themeColorKey").toInt();
	resourceInfo.bHover = true;
	resourceInfo.bApplyAll = args.value("isApplyAll", false).toBool();
	resourceInfo.bBatchApply = args.value("isBatchApply", false).toBool();
	resourceInfo.bHoverReturnResult = args.value("isHoverReturnResult", false).toBool();

	const QString& strXmlPath = getFileSavePath(strResourceId, QFileInfo(QUrl(strPreviewUrl).path()).baseName());
	QFileInfo file(strXmlPath);
	if (file.exists())
	{
		emit insertOnlineResource(resourceInfo, ReportInfo(), file.absoluteFilePath(), true);
	}
	else
	{
		ReportInfo reportInfo;
		reportInfo.click = false;
		reportInfo.tablestyleId = QString::fromUtf8(args.value("id").toString().toUtf8().data());
		reportInfo.from = QString::fromUtf8(args.value("from").toString().toUtf8().data());
		reportInfo.belongPage = QString::fromUtf8(args.value("belong_page").toString().toUtf8().data());
		reportInfo.userStatusId = QString::fromUtf8(args.value("user_status_id").toString().toUtf8().data());
		reportInfo.sourceStatusId = QString::fromUtf8(args.value("source_staus_id").toString().toUtf8().data());

		downloadResouce(resourceInfo, strPreviewUrl, strXmlPath, reportInfo);
	}

	Json::Value res;
	res["isOk"] = true;
	res["nextOp"] = "endPreview";
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::leaveOnlineResource(KxWebViewJSContext& context)
{
	m_hoverOnlineResId.clear();
	emit leaveOnlineResource();
}

void KxOnlineTableStyleJsApi::canHoverOnlineResource(KxWebViewJSContext& context)
{
	setResult(context, QVariant(canHover() && KxOnlineTableResHelper::hasActiveTable(this)));
}

void KxOnlineTableStyleJsApi::wpsInsertChart(KxWebViewJSContext& context)
{
	//规避KxWebExtensionView::load重入问题
	if (KPopupWidget* popUpWidget = KTik::findParentByType<KPopupWidget>(m_hostWidget))
		popUpWidget->close();

	const QString& method = "docer.chart.wpsInsertChart";
	QVariant ret = KDocerJsApiHost::instance().callToPlugin(nullptr, "docerjsapiservice", method, context.convertToMap());
	if (ret.toMap().value(g_str_callstatus).toString() == g_str_ok)
	{
		KxMainWindow* mw = kxApp->findRelativeMainWindowX(m_hostWidget);
		if (!mw)
			mw = kxApp->currentMainWindow();

		if (KDocerUtils::isPluginConfiguredOrCanUse("konlinechart"))
		{
			QVariantMap params = {
				{ "pluginName", "konlinechart"},
				{ "targetId", "TpDocerChartProcess" },
				{ "func", "docer_infeature" },
				{ "second_func", "chart" },
				{ "first_entry", "proce" },
				{ "second_entry", "proce_diag_set" }
			};
			KxDocerTpFeatureUtil::showTpObjectBeautify(mw, params);
		}
	}
}

void KxOnlineTableStyleJsApi::isDsDataFitRule(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	const QString& rule = args.value("rule_new").toString();
	bool bIsFitRule = isDsDataFitDataTypeRule(rule);
	setResult(context, QVariant(bIsFitRule));
}

void KxOnlineTableStyleJsApi::getTableTextAsRule(KxWebViewJSContext& context)
{
	QVariantList tableArray;
	if (KDocerUtils::isWppApp(this))
		tableArray = KxDocerWppApi::getTableTextAsArray();

	docer::base::KDataSourceRuleMatcher ruleMatcher;
	setResult(context, toVariantList(ruleMatcher.dataRule(tableArray)));
}

void KxOnlineTableStyleJsApi::getCurrentGridlineStatues(KxWebViewJSContext& context)
{
	context.Result[g_str_callstatus] = g_str_ok;
	QVariantMap result;
	result["status"] = KDocerUtils::isKsoCmdChecked("FTB_GridSwitch");
	setResult(context, result);
}

void KxOnlineTableStyleJsApi::querySupportInterface(KxWebViewJSContext& context)
{
	static QList<QVariant> s_SupprtInterface;
	if (s_SupprtInterface.isEmpty())
	{
		if (KDocerUtils::isWppApp(this) && isPluginVersionNew("kdocerjscore", "dsConvertTable", true))
			s_SupprtInterface << "dsConvertTable";

		if (KDocerUtils::isWppApp(this))
			s_SupprtInterface << "dsChangeFont";
	}
	
	setResult(context, s_SupprtInterface);
}

void KxOnlineTableStyleJsApi::registerTablestyleThemeChange(KxWebViewJSContext& context)
{
	if (m_bRegisterTablestyleThemeChange)
		return;

	RegisterNotifyFilter();
	m_bRegisterTablestyleThemeChange = true;
}

ksolite::KxCommonWebDialog* KxOnlineTableStyleJsApi::createNewUrlWidget(QWidget* parent,
int nWidth, int nHeight, bool bModal, bool bCloseBtn)
{
	return new ksolite::KxCommonWebDialog(createJsApiFunc, parent, nWidth, nHeight, bModal, bCloseBtn);
}

QString KxOnlineTableStyleJsApi::getSkinConfigPath() const
{
	return getSkinFilePath();
}

void KxOnlineTableStyleJsApi::onInsertSuccess(QVariantMap& result, const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo)
{
	result["errorCode"] = Success;
	result["description"] = getErrorDesc(Success);
	if (KDocerUtils::isEtApp(this))
	{
		result["hasMultiChildTable"] = reportInfo.hasMultiChildTable;
		result["hasNormalTable"] = reportInfo.hasNormalTable;
		result["hasTableApply"] = reportInfo.hasTableApply;
		result["isAreaApply"] = reportInfo.isAreaApply;
	}
	callbackToJS(QString("onInsertSuccess"), formatResult(result));

	if (m_broadcastInfo.contains(resourceInfo.id))
	{
		const QVariantMap& broadcastInfo = m_broadcastInfo[resourceInfo.id];
		KxWebViewJSContext ctxt;
		ctxt.parse(JsonHelper::convertVariantToString(broadcastInfo));
		boardcastCustomMessage(ctxt);
	}

	if (m_downloadTasks.size() == 0)
		emit closeWindow();

#ifdef WPP_PROJECT
	KxOnlineTableStyleJsApiHelper::Instance().onCallBackToJs("slideChanged", "");
#endif
}

void KxOnlineTableStyleJsApi::onInsertError(QVariantMap& result, const KOTSResourceInfo& resourceInfo, OperatorErrorCode errorCode, const QString& errorDesc)
{
	result["errorCode"] = errorCode;
	result["description"] = getErrorDesc(errorCode);

	callbackToJS("onInsertError", formatResult(result));
}

void KxOnlineTableStyleJsApi::onInsertSkip(QVariantMap& result, const KOTSResourceInfo& resourceInfo, OperatorErrorCode errorCode)
{
	result["errorCode"] = errorCode;
	result["description"] = getErrorDesc(errorCode);

	callbackToJS("onInsertSkip", formatResult(result));
}

void KxOnlineTableStyleJsApi::onInsertBatchStart(QVariantMap& result, OperatorErrorCode errorCode)
{
	result["errorCode"] = errorCode;
	result["description"] = getErrorDesc(errorCode);

	callbackToJS("onInsertBatchStart", formatResult(result));
}

void KxOnlineTableStyleJsApi::onInsertBatchFinish(QVariantMap& result, const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode)
{
	result["errorCode"] = errorCode;
	result["description"] = getErrorDesc(errorCode);
	result["tableNumber"] = reportInfo.tableNumber;

	if(errorCode != BatchApplyFinished)
		result["currentTableNumber"] = reportInfo.currentTableNumber;

	callbackToJS("onInsertBatchFinish", formatResult(result));
}

void KxOnlineTableStyleJsApi::onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc)
{
	if (reportInfo.from == "oneKeyBeautify")
	{
		onekeyBeautifyFinished(errorCode == Success);
		return;
	}

	QVariantMap result;
	result["id"] = resourceInfo.id;
	result["area"] = resourceInfo.area;
	result["applyType"] = resourceInfo.bApplyAll ? (resourceInfo.bBatchApply ? "apply_all" : "apply_current") : "apply_single";
	result["isHoverReturnResult"] = resourceInfo.bHoverReturnResult;

	if (KDocerUtils::isEtApp(this)
		&& errorCode != BatchApplyStart
		&& errorCode != BatchApplyFinished
		&& errorCode != BatchOneSheetProtectedOrHided)
	{
		QVariantMap performanceMap;
		performanceMap["nonempty_cell"] = reportInfo.tableApplyInfo.nonemptyCellCnt;
		performanceMap["area_cell"] = reportInfo.tableApplyInfo.areaCellCnt;
		performanceMap["extract_time"] = reportInfo.tableApplyInfo.extractTime;
		performanceMap["identify_time"] = reportInfo.tableApplyInfo.identifyTime;
		performanceMap["beautify_time"] = reportInfo.tableApplyInfo.beautifyTime;
		performanceMap["total_time"] = reportInfo.tableApplyInfo.totalTime;
		performanceMap["select_cell"] = reportInfo.tableApplyInfo.selectCell;
		performanceMap["is_huge"] = reportInfo.tableApplyInfo.isHuge;
		result["performanceMap"] = performanceMap;
	}

	if (errorCode == Success)
	{
		onInsertSuccess(result, resourceInfo, reportInfo);
	}
	else if (errorCode == LastApplyItemChanged ||
		errorCode == TableRecognizeCancel ||
		errorCode == TableRecognizeFailed)
		onInsertSkip(result, resourceInfo, errorCode);
	else if(errorCode == BatchApplyStart)
		onInsertBatchStart(result, errorCode);
	else if (errorCode == BatchApplyFinished ||
		errorCode == BatchOneSheetFinished ||
		errorCode == BatchOneSheetProtectedOrHided)
		onInsertBatchFinish(result, resourceInfo, reportInfo, errorCode);
	else
	{
		onInsertError(result, resourceInfo, errorCode, errorDesc);
	}
}

void KxOnlineTableStyleJsApi::downloadResouce(const KOTSResourceInfo& resourceInfo, const QString& strDownloadUrl, const QString& strSavePath, const ReportInfo& reportInfo)
{
	if(reportInfo.click)
		m_downloadInfoMap[resourceInfo.id].append(qMakePair(resourceInfo, reportInfo));
	else
		m_downloadInfoMap[resourceInfo.id].prepend(qMakePair(resourceInfo, reportInfo));
	if (m_downloadId2ResId.values().contains(resourceInfo.id))
		return;

	DownloadArgs downloadData;
	downloadData.commonArgs.plgName = "konlinetablestyle";
	downloadData.resourceArgs.id = resourceInfo.id;
	downloadData.resourceKey = "tablestyle";
	downloadData.resourceArgs.downloadKey = reportInfo.downloadKey;

	KDocerDownloader* downloader = new KDocerDownloader(downloadData, this);
	connect(downloader, &KDocerDownloader::onProgress,
		this, &KxOnlineTableStyleJsApi::onDownloadProgress);
	connect(downloader, &KDocerDownloader::onSuccess,
		this, &KxOnlineTableStyleJsApi::onDownloadSuccess);
	connect(downloader, &KDocerDownloader::onError,
		this, &KxOnlineTableStyleJsApi::onDownloadError);

	auto taskId = downloader->download({ strDownloadUrl }, strSavePath, QString());

	m_downloadId2ResId.insert(taskId, resourceInfo.id);
	m_downloadTasks.push_back(downloader);
}

void KxOnlineTableStyleJsApi::removeDownloadTask(const QString& id)
{
	for (auto it = m_downloadTasks.begin(); it != m_downloadTasks.end(); ++ it)
	{
		auto task =*it;
		if (task->id() == id)
		{
			delete task;
			m_downloadTasks.erase(it);
			break;
		}
	}
}

void KxOnlineTableStyleJsApi::clearAllDownloadTask()
{
	for (auto it = m_downloadTasks.begin(); it != m_downloadTasks.end(); ++ it)
	{
		auto task =*it;
		task->cancel();
		delete task;
	}

	m_downloadTasks.clear();
}

bool KxOnlineTableStyleJsApi::canHover()
{
#ifdef Q_OS_WIN
	BOOL bActive = FALSE;
	HRESULT hr = kxApp->Get_Active(&bActive);
	if (FAILED(hr) || !bActive)
		return false;
#endif // Q_OS_WIN

	KxMainWindow* mw = kxApp->currentMainWindow();
	if (kxApp->findRelativeMainWindowX(this) != mw)
		return false;

	return true;
}

QString KxOnlineTableStyleJsApi::getExternalName()
{
	return QString("onlinetablestyle");
}

void KxOnlineTableStyleJsApi::onDownloadProgress(const QString& id, int nPercent)
{
	auto resIt = m_downloadId2ResId.constFind(id);
	if (resIt != m_downloadId2ResId.constEnd() && !m_downloadInfoMap.value(resIt.value()).isEmpty())
	{
		auto resourceInfo = m_downloadInfoMap.value(resIt.value()).first().first;
		QVariantMap result;
		result["id"] = resourceInfo.id;
		result["percent"] = nPercent;
		result["area"] = resourceInfo.area;

		callbackToJS("onDownloadProgress", formatResult(result));
	}
}

void KxOnlineTableStyleJsApi::onDownloadSuccess(const QString& id, const QString& savePath)
{
	removeDownloadTask(id);
	auto resIt = m_downloadId2ResId.constFind(id);
	if (resIt != m_downloadId2ResId.constEnd())
	{
		QString resId = resIt.value();
		ReportInfo downloadReportInfo;
		KOTSResourceInfo downloadResourceInfo;
		for (const auto& applyInfo : m_downloadInfoMap.value(resId))
		{
			ReportInfo reportItem = applyInfo.second;
			KOTSResourceInfo applyItem = applyInfo.first;

			if (downloadReportInfo.tablestyleId.isEmpty() || reportItem.click)
			{
				downloadReportInfo = reportItem;
				downloadResourceInfo = applyItem;
			}
			
			const QString& itemId = applyItem.id;
			const KOTSResourceInfo& resourceInfo = applyItem;

			bool isHover = !reportItem.click;
			if ((!isHover) || (isHover && m_hoverOnlineResId == itemId))
				emit insertOnlineResource(resourceInfo, reportItem, savePath, isHover);
		}
		if (!downloadReportInfo.tablestyleId.isEmpty())
		{
			emit sendDownloadInfo(downloadReportInfo.tablestyleId, downloadReportInfo, 0, "");
			QVariantMap result;
			result["id"] = downloadResourceInfo.id;
			result["area"] = downloadResourceInfo.area;

			callbackToJS("onDownloadSuccess", formatResult(result));
		}
		m_downloadId2ResId.remove(id);
		m_downloadInfoMap.remove(resId);
	}
}

void KxOnlineTableStyleJsApi::onDownloadError(const QString& id, DownloadFailInfo info)
{
	removeDownloadTask(id);
	auto resIt = m_downloadId2ResId.constFind(id);
	if (resIt != m_downloadId2ResId.constEnd())
	{
		QString resId = resIt.value();
		ReportInfo downloadReportInfo;
		KOTSResourceInfo downloadResourceInfo;
		for (const auto& applyInfo : m_downloadInfoMap.value(resId))
		{
			ReportInfo reportItem = applyInfo.second;
			KOTSResourceInfo applyItem = applyInfo.first;

			if (downloadReportInfo.tablestyleId.isEmpty() || reportItem.click)
			{
				downloadReportInfo = reportItem;
				downloadResourceInfo = applyItem;
			}
		}
		if (!downloadReportInfo.tablestyleId.isEmpty())
		{
			emit sendDownloadInfo(downloadReportInfo.tablestyleId, downloadReportInfo, info.errorCode, info.description);
			QVariantMap result;
			result["id"] = downloadResourceInfo.id;
			result["errorCode"] = info.errorCode;
			result["description"] = info.description;
			result["area"] = downloadResourceInfo.area;
			callbackToJS("onDownloadError", formatResult(result));
		}
		m_downloadId2ResId.remove(id);
		m_downloadInfoMap.remove(resId);
	}
}

void KxOnlineTableStyleJsApi::boardcastCustomMessage(KxWebViewJSContext& context)
{
	KxOnlineTableStyleJsApiHelper::Instance().boardcastCustomMessage(this, context);
}

void KxOnlineTableStyleJsApi::isShowTableStyleQhTab(KxWebViewJSContext& context)
{
	Json::Value res;
	res["visible"] = false;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::isMoreSettingVisible(KxWebViewJSContext& context)
{
	bool canOpen = KDocerUtils::isPluginConfiguredOrCanUse(strPluginName);
	Json::Value res;
	res["visible"] = canOpen;
	res["canFeatureAssociate"] = KxDocerTpFeatureUtil::isSupportQhFeatureAssociate(strPluginName);
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::isHasMuiltiChildTable(KxWebViewJSContext& context)
{
	QVariantMap args = parseContextArgs(context);
	bool bApplyAll = args.value("isApplyAll", false).toBool();
	emit sigGetHasMultiChildTable(bApplyAll);
}

void KxOnlineTableStyleJsApi::onGetHasMultiChildTable(bool bSuccess, bool bHasMultiChildTable)
{
	QVariantMap result;
	result["isAnalyzed"] = bSuccess;
	result["hasMultiChildTable"] = bHasMultiChildTable;
	callbackToJS("onGetHasMultiChildTable", formatResult(result));
}

void KxOnlineTableStyleJsApi::getCustomColor(KxWebViewJSContext& context)
{
	QColor qColor = QColor();
	qColor = kxApp->getColor(qColor, "");
	bool ret = qColor.isValid();

	if (!ret)
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "user cancel";
		return;
	}

	QVariantMap result;
	result["rgb"] = KxOnlineTableResHelper::qColorToHex(qColor);
	setResult(context, result);
}

void KxOnlineTableStyleJsApi::getReplaceColorList(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QString errMsg;
	QVariantList result;
	if (args.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "input json invalid";
		return;
	}

	if (!KxOnlineTableResHelper::getReplaceColorList(args, result, errMsg))
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = errMsg.toStdString();
		return;
	}
	
	setResult(context, result);
}

void KxOnlineTableStyleJsApi::getThemeColorList(KxWebViewJSContext& context)
{
	setResult(context, KxOnlineTableResHelper::getThemeColorList());
}

void KxOnlineTableStyleJsApi::testInsertOnlineResource(KxWebViewJSContext& context)
{
	endPreview();

	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	QString filePath = args.value("filePath").toString();
	if (!QFileInfo(filePath).exists())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "file not exist";
		return;
	}
	KOTSResourceInfo resourceInfo;
	resourceInfo.name = QFileInfo(filePath).baseName();
	emit insertOnlineResource(resourceInfo, ReportInfo(), filePath, false);
}

void KxOnlineTableStyleJsApi::rsaEncrypt(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QVariantMap data = args.value("data").toMap();
	if (data.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "input json invalid";
		return;
	}

	QString encryptToken;
	KRsaEncrypt::rsaEncrypt(QLatin1String(docer::SIGN_FILE_RSA_KEY_PUBLIC), JsonHelper::variantMapSerialize(data).toUtf8(), encryptToken);
	encryptToken.remove(QRegularExpression("[\\r\\n]"));

	setResult(context, QVariantMap({ {"encrypted", encryptToken} }));
}

void KxOnlineTableStyleJsApi::getResourceFilePath(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QString id = args.value("id").toString();
	QString md5 = args.value("md5").toString();
	if (id.isEmpty() || md5.isEmpty())
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "param id or md5 is empty";
		return;
	}
	setResult(context, QVariantMap({ {"filePath", QDir::fromNativeSeparators(KxOnlineTableResHelper::getFileSavePath(id, md5))}}));
}

void KxOnlineTableStyleJsApi::pannelEnable(PanelEnableStatus status)
{
	QVariantMap result;
	result["enable"] = status == PanelEnableStatus::Enable;
	result["status"] = (int)status;
	callbackToJS("pannelEnable", formatResult(result));
}

void KxOnlineTableStyleJsApi::tabIndexChanged(int index)
{
	QVariantMap result;
	result["index"] = index;
	callbackToJS("tabIndexChanged", formatResult(result));
}

void KxOnlineTableStyleJsApi::notifyDisplay(const QString& entrance, const QString& detailEntrance, const QString& paySource)
{
	QVariantMap result;
	result["p1"] = entrance;
	result["p4"] = detailEntrance;
	result["paysource"] = paySource;

	callbackToJS("showWindow", formatResult(result));
}

void KxOnlineTableStyleJsApi::gridlineChckedChnage(bool bChecked)
{
	QVariantMap result;
	result["status"] = bChecked;
	callbackToJS("gridlineChange", formatResult(result));
}

void KxOnlineTableStyleJsApi::identifyResultNotify(TableRecoStaus status, TableAnalyzeFrom from)
{
	QVariantMap result;
	result["from"] = (int)from;
	QString eventName;
	switch (status)
	{
	case TableRecoStaus::TableRecogizing:
		eventName = "tableAnalyzing";
		break;
	case TableRecoStaus::TableRecogizeSuucess:
		eventName = "tableAnalyzeSuccess";
		break;
	case TableRecoStaus::TableRecogizeFailed:
		eventName = "tableAnalyzeFailed";
		break;
	case TableRecoStaus::TableRecogizeNoUpdate:
		eventName = "tableAnalyzeNoUpdate";
		break;
	case TableRecoStaus::TableRecogizeTooLarge:
		eventName = "tableAnalyzeTooLarge";
		break;
	default:
		break;
	}
	if(!eventName.isEmpty())
		callbackToJS(eventName, JsonHelper::variantMapSerialize(result));
}

void KxOnlineTableStyleJsApi::arrangeResultNotify(bool bSuccess, TableArrangeResult errorType, qint64 totalTimes, TableApplyInfo tableApplyInfo)
{
	QVariantMap result;
	result["success"] = bSuccess;
	result["errorType"] = (int)errorType;
	result["total_time"] = totalTimes;
	result["nonempty_cell"] = tableApplyInfo.nonemptyCellCnt;
	result["area_cell"] = tableApplyInfo.areaCellCnt;
	result["is_huge"] = tableApplyInfo.isHuge;
	result["is_auto_fit"] = tableApplyInfo.isAutoFit;
	callbackToJS("tableArrangeResult", JsonHelper::variantMapSerialize(result));
}

QWidget* KxOnlineTableStyleJsApi::widgetParent()
{
	if (kxApp)
		return kxApp->findRelativeMainWindowX(this);
	else
		return NULL;
}

void KxOnlineTableStyleJsApi::onApplyTableStyle(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, const QString& filePath, bool hover, ksolite::KxCommonJsApi* jsApi)
{
	if (jsApi == this)
		emit insertOnlineResource(resourceInfo, reportInfo, filePath, hover);
}

void KxOnlineTableStyleJsApi::notifyJsHidePreview()
{
	m_curPreivewPicId = "";
	if (m_bShowPreview)
	{
		QVariantMap result;

		if (m_previewWidget)
		{
			m_bShowPreview = false;
			m_previewWidget->hide();
			result["id"] = m_previewWidget->getResId();
		}
		else if(m_quickBarPreviewWidget)
		{
			m_bShowPreview = false;
			m_quickBarPreviewWidget->hide();
			result["id"] = m_quickBarPreviewWidget->getResId();
		}

		callbackToJS("hiddenPreview", formatResult(result));
	}
}

BOOL KxOnlineTableStyleJsApi::OnFilterNotify(ksoNotify* notify)
{
	if (!notify)
		return TRUE;
	
	switch (notify->notify)
	{
	case ksoNotify::ksoActiveItem:
	case ksoNotify::ksoWebExtensionThemeChange:
		callbackToJS("tablestyleThemeChange", formatResult(KxOnlineTableResHelper::getThemeColorList()));
		break;
	}
	return TRUE;
}

void KxOnlineTableStyleJsApi::listenSelectionChanged()
{
	KxDocerShapeNotify* notify = nullptr;

	if (KDocerUtils::isWpsApp(this))
		notify = KxWpsDocerShapeNotify::instance();
	else if (KDocerUtils::isEtApp(this))
		notify = KxEtDocerShapeNotify::instance(this, this);
	else if(KDocerUtils::isWppApp(this))
		notify = KxWppDocerShapeNotify::instance();

	if (notify)
	{
		notify->start();
		connect(notify, &KxDocerShapeNotify::selectionChange, this, &KxOnlineTableStyleJsApi::onSelectionChange);
	}

#ifdef WPP_PROJECT
	KxWppDocerShapeNotify::instance()->start();
	connect(KxWppDocerShapeNotify::instance(), &KxWppDocerShapeNotify::selectionSlideChange,
		this, [&]() {
			callbackToJS("slideTablestyleThemeChange", formatResult(KxOnlineTableResHelper::getThemeColorList()));
		});
#endif
}

void KxOnlineTableStyleJsApi::showPreview(KxWebViewJSContext& context)
{
	bool ret = false;
	QString errorMsg;
	do
	{
		KxMainWindow* mw = kxApp->findRelativeMainWindowX(this);
		if (kxApp->currentMainWindow() != mw)
		{
			errorMsg = "no current active";
			break;
		}

		const QVariantMap& args = parseContextArgs(context);
		if (args.isEmpty())
		{
			errorMsg = "input json invalid";
			break;
		}

		QString strDownloadUrl = args.value("url").toString();
		if (strDownloadUrl.isEmpty())
		{
			errorMsg = "input json invalid. no url.";
			break;
		}
		QString label = args.value("label").toString();
		QString pictureId = args.value("id").toString();
		if (pictureId.isEmpty())
		{
			errorMsg = "input json invalid. no pictureId.";
			break;
		}
		QString pictureName = args.value("name").toString();
		if (pictureName.isEmpty())
		{
			errorMsg = "input json invalid. no pictureName.";
			break;
		}
		bool bFreePicture = args.value("free").toBool();

		//QString picFrom = args.value("from", tr("Kingsoft Office")).toString();
		QString picFrom = tr("Kingsoft Office");
		QString picSavePath = KxOnlineTableStyleJsApiHelper::Instance().getResSavePath(strDownloadUrl, pictureId);
		m_curPreivewPicId = pictureId;
		m_hidePreviewId = "";
		m_bShowPreview = true;

		if (!isQuickBar())
		{
			if (!m_previewWidget)
				initPreviewDlg();
			if (!label.isEmpty())
				m_previewWidget->setTitleText(label);
			m_previewWidget->showPreview(strDownloadUrl, pictureId, picFrom, bFreePicture ?
				KDocerCommonPreviewDlg::HRT_Free : KDocerCommonPreviewDlg::HRT_DocerVip, label, picSavePath, pictureName);
		}
		else
		{
			if (!m_quickBarPreviewWidget)
				initQuickBarPreviewDlg();
			if (!label.isEmpty())
				m_quickBarPreviewWidget->setTitleText(label);
			m_quickBarPreviewWidget->showPreview(strDownloadUrl, pictureId, picFrom, bFreePicture ?
				KDocerCommonPreviewDlg::HRT_Free : KDocerCommonPreviewDlg::HRT_DocerVip, label, picSavePath, pictureName);
		}
		
		ret = true;
	} while (0);

	if (ret)
		context.Result["callstatus"] = "ok";
	else
		context.Result["callstatus"] = "error";

	context.Result["errormsg"] = errorMsg.toStdString();
}

void KxOnlineTableStyleJsApi::hidePreview(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty())
		m_hidePreviewId = args.value("id").toString();

	if (m_previewWidget)
		m_previewWidget->hide();

	if (m_quickBarPreviewWidget)
		m_quickBarPreviewWidget->hide();
}

void KxOnlineTableStyleJsApi::getShowPreviewStatus(KxWebViewJSContext& context)
{
	context.Result["result"] = canHover() ? "true" : "false";
}

void KxOnlineTableStyleJsApi::insertSystemResource(KxWebViewJSContext& context)
{
	endPreview();

	if (!KxOnlineTableResHelper::hasActiveTable(this))
		return;

	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	int resourceIndex = args.value("index").toInt();
	if (resourceIndex < 0)
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "input json invalid. no index.";
		return;
	}
	emit insertSystemResource(resourceIndex, false);
}

void KxOnlineTableStyleJsApi::hoverSystemResource(KxWebViewJSContext& context)
{
	endPreview();

	if (!KxOnlineTableResHelper::hasActiveTable(this))
		return;

	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	int resourceIndex = args.value("index").toInt();
	if (resourceIndex < 0)
	{
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "input json invalid. no index.";
		return;
	}
	emit insertSystemResource(resourceIndex, true);
}

void KxOnlineTableStyleJsApi::leaveSystemResource(KxWebViewJSContext& context)
{
	emit leaveSystemResource();
}

void KxOnlineTableStyleJsApi::hasActiveTable(KxWebViewJSContext& context)
{
	if (KDocerUtils::isEtApp(this))
	{
		setResult(context, QVariant(true));
		return;
	}
	setResult(context, QVariant(KxOnlineTableResHelper::hasActiveTable(this)));
}

bool KxOnlineTableStyleJsApi::isQuickBar()
{
	return property("quickBar").toBool();
}

void KxOnlineTableStyleJsApi::initPreviewDlg()
{
	QSize imageSize = KWPSStyle::dpiScaledSize(QSize(354, 200));
	QSize widgetSize = KWPSStyle::dpiScaledSize(QSize(370, 216));
	QString commandName = "TpWpsTableStyle";
	if (KDocerUtils::isEtApp(this))
	{
		commandName = "TpEtTableStyle";
	}
	else if (KDocerUtils::isWppApp(this))
	{
		commandName = "TpWppTableStyle";
	}
	auto mw = kxApp->findRelativeMainWindowX(this);
	if (!mw)
		mw = kxApp->currentMainWindow();

	m_previewWidget = new KDocerCommonPreviewDlg(mw, new KDocerCommonJsApiPreviewHost(m_onlineWidget, this),
		commandName, imageSize, widgetSize, KDocerCommonPreviewDlg::HPT_QuickHelpBar);
	m_previewWidget->setTitleText(tr("TableStyle Preview"));
}

void KxOnlineTableStyleJsApi::initQuickBarPreviewDlg()
{
	QSize imageSize = KWPSStyle::dpiScaledSize(QSize(354, 200));
	QSize widgetSize = KWPSStyle::dpiScaledSize(QSize(370, 216));
	QString commandName = "TpWpsTableStyle";
	if (KDocerUtils::isEtApp(this))
	{
		commandName = "TpEtTableStyle";
	}
	else if (KDocerUtils::isWppApp(this))
	{
		commandName = "TpWppTableStyle";
	}
	auto mw = kxApp->findRelativeMainWindowX(this);
	if (!mw)
		mw = kxApp->currentMainWindow();
	m_quickBarPreviewWidget = new KDocerCommonPreviewDlg(mw, new KDocerCommonJsApiPreviewHost(m_onlineWidget, this),
		commandName, imageSize, widgetSize, KDocerCommonPreviewDlg::HPT_QuickHelpBar);
	m_quickBarPreviewWidget->setTitleText(tr("TableStyle Preview"));
}

bool KxOnlineTableStyleJsApi::isDsDataFitDataTypeRule(const QString& rule)
{
	const QString& method = "docer.chart.wpsIsSourceDataRuleMatch";
	QVariantMap args;
	args["rule"] = rule;
	QVariantMap params;
	params["params"] = args;
	QVariantMap ret = KDocerJsApiHost::instance().callToPlugin(NULL, "docerjsapiservice", method, params).toMap();

	QVariantList dataList;
	if (!ret.isEmpty())
	{
		QVariantMap result = ret.value("result").toMap();
		if (!result.isEmpty())
		{
			return result.value("data", true).toBool();
		}
	}

	return true;
}

void KxOnlineTableStyleJsApi::onSystemInsertFinished(int resourceId, OperatorErrorCode errorCode, const QString& errorDesc)
{
	if (errorCode == Success)
	{
		onSystemInsertSuccess(resourceId);
	}
	else
	{
		onSystemInsertError(resourceId, errorCode, errorDesc);
	}
}

void KxOnlineTableStyleJsApi::onSystemInsertSuccess(int resourceId)
{
	QVariantMap result;
	result["id"] = resourceId;
	callbackToJS(QString("onSystemInsertSuccess"), formatResult(result));

	emit closeWindow();
}

void KxOnlineTableStyleJsApi::onSystemInsertError(int resourceId, OperatorErrorCode errorCode, const QString& errorDesc)
{
	QVariantMap result;
	result["id"] = resourceId;
	result["errorCode"] = errorCode;
	result["description"] = getErrorDesc(errorCode);

	callbackToJS("onInsertError", formatResult(result));
}

void KxOnlineTableStyleJsApi::onSelectionChange(KxMainWindow*)
{
	callbackToJS("tablestyleThemeChange", formatResult(KxOnlineTableResHelper::getThemeColorList()));
}

void KxOnlineTableStyleJsApi::canHoverEx(KxWebViewJSContext& context)
{
	canHover(context);
}

void KxOnlineTableStyleJsApi::getEntrance(KxWebViewJSContext& context)
{
	if (!m_onlineWidget)
		return;
	QString res;
	if (m_onlineWidget->getIsQuickHelpWidget())
		res.append("beautify");
	else if (m_onlineWidget->getIsTopstyleshow())
		res.append("restab");
	else if (m_onlineWidget->getIsInTaskPane())
		res.append("task");
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::switchAffectType(KxWebViewJSContext& context)
{
	QVariantMap args = parseContextArgs(context);
	QString type = args.value("arrangeType").toString();
	if (type.isEmpty())
	{
		arrangeResultNotify(false, TableArrangeResult::ArrangeFailed, 0, TableApplyInfo());
		return;
	}
	bool bApplyAll = args.value("isApplyAll", false).toBool();
	emit sigApplyArrange(type, bApplyAll);
}

void KxOnlineTableStyleJsApi::triggerGridLine(KxWebViewJSContext& context)
{
	KDocerUtils::triggerKsoCmd("FTB_GridSwitch");
	kxApp->ForceIdle();
}

void KxOnlineTableStyleJsApi::doTableAnalyze(KxWebViewJSContext& context)
{
	QVariantMap args = parseContextArgs(context);
	TableAnalyzeFrom from = (TableAnalyzeFrom)args["from"].toInt();
	emit sigAnalyzeTable(from);
}

void KxOnlineTableStyleJsApi::cancelAnalyze(KxWebViewJSContext& context)
{
	emit sigCancelAnalyze();
}

void KxOnlineTableStyleJsApi::updateTableView(KxWebViewJSContext& context)
{
	emit sigUpdateTableView();
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

void KxOnlineTableStyleJsApiHelper::onCallBackToJs(const QString& method, const QString& param)
{
	QVariantMap paramsMap;
	paramsMap["msgProc"] = method;
	paramsMap["msgProcArgs"] = param;

	KxWebViewJSContext context;
	context.parse(ConvertJsonHelper::variantMapSerialize(paramsMap));
	boardcastCustomMessage(nullptr, context);
}

QString KxOnlineTableStyleJsApiHelper::getResSavePath(const QString& picUrl, const QString& picId)
{
	QString picName;
	int beginOfPicture = picUrl.lastIndexOf("?");
	if (beginOfPicture != -1)
	{
		QString relativePicUrl = picUrl.mid(0, beginOfPicture);
		picName = relativePicUrl.right(relativePicUrl.size() - beginOfPicture - 1);
	}
	else
	{
		beginOfPicture = picUrl.lastIndexOf("/");
		picName = picUrl.right(picUrl.size() - beginOfPicture - 1);
	}
	QString ext = "." + picName.split('.').last();
	int sum = getResIdSum(picId);
	QString encryptName = "&pky" + QString::number(picId.length()) + QString::number(sum) + picId + "&";
	encryptName += ext;
	QString savePath = downloadPath();
	savePath.append(encryptName);
	return savePath;
}

int KxOnlineTableStyleJsApiHelper::getResIdSum(const QString& picId)
{
	bool isInt = false;
	picId.toInt(&isInt);
	int sum = 0;
	if (isInt)
	{
		for (int i = 0; i < picId.length(); i++)
		{
			sum += picId.mid(i, 1).toInt();
		}
		sum = sum % 10;
	}
	return sum;
}

QString KxOnlineTableStyleJsApiHelper::downloadPath()
{
	QString appdataPath = krt::dirs::appData();

	QString download_folder_name = "/kingsoft/office6/onlinetablestyle/download/";
	appdataPath = appdataPath.append(download_folder_name);

	return appdataPath;
}