#pragma once

#include "kxonlinetablestyledefine.h"
#include "common/jsapi/command/kdocercommoncommand.h"

class KxTableStyleGalleryCommand
	: public KDocerCommonCommand<KxGalleryCommand>
{
	Q_OBJECT
public:
	KxTableStyleGalleryCommand(KxMainWindow * host, QObject * parent);
	~KxTableStyleGalleryCommand();
	virtual QWidget* createExtendedWidget(QWidget* parent) override;
	virtual QWidget* createWidget(QWidget* parent, KCommand::WidgetPurpose wp) override;
	virtual KCommand* clone(QObject* host, QObject* parent) override;
	STDPROC Get(KSO_Group, KSO_DataID id, void*, void* val);
	OperatorErrorCode activeEtTableStyle(const TableInfo& tableInfo);
private:
	QString getDetailEntrance(const QString& cmdId = QString());
	bool isCompatibilityMode();
	DWORD tableStyleOptToPivotTableStyleOpt(const DWORD opt);
protected:
	TableInfo				m_curTableInfo;
	KCommand*				m_featureCmd = nullptr;
};

///////////////////////////////////////////////////////////////////
class KxWppTableStyleGalleryCommand : public KxTableStyleGalleryCommand {
	Q_OBJECT
public:
	KxWppTableStyleGalleryCommand(KxMainWindow* host, QObject* parent);
};