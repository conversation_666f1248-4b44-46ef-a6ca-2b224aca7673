﻿#include "stdafx.h"
#include "kinsertonlinetablemodel.h"
#include <src/kxonlinereshelper.h>
#include <kdocertoolkit/kdocerutils.h>
#include <kdocerfunctional.h>
#include <src/resmgr/konlinetableresmgr.h>
#include <src/ktablestyleinfocollect.h>
#include <kcomctl/kcolorcombobox.h>
#include <kdocertoolkit/helper/kdocercommonhelper.h>
#include <public_header/kliteui/koperatorlib.h>
#include <common/request/network/knetworkrequest.h>
#include "kdocertoolkitlite/kdocerutilslite.h"
#define MAX_PAGE_COUNT 10
#define LOGIN_SRC "docer_et_infeature"
#define ICON_OFFSET 4

namespace {
	void fillRect(QPainter& dc, const QRect& rc, const QBrush& color, int radius, int round)
	{
		dc.save();
		dc.setPen(Qt::NoPen);
		dc.setBrush(color);
		dc.setRenderHint(QPainter::Antialiasing, true);
		dc.drawPath(KDrawHelpFunc::getBoundPath(rc, radius, round));
		dc.restore();
	}
}

KxInsertScrollView::KxInsertScrollView(KGalleryAbstractModel* model, QWidget* parent)
	:KScrollGalleryView(model, parent),
	m_model(model)
{
	connect(model, SIGNAL(sigRetryButtonHoverd(bool)), this, SLOT(onRetryButtonHoverd(bool)));
	if (galleryView())
	{
		QMargins unExtendMargins = galleryView()->getUnExtendItemMargins();
		unExtendMargins += KLiteStyle::dpiScaledMargins(1 + ICON_OFFSET, 0, -1 + ICON_OFFSET, 0);
		unExtendMargins.setTop(0);
		unExtendMargins.setBottom(0);
		galleryView()->setUnExtendItemMargins(unExtendMargins);
		connect(galleryView(), &KGalleryView::sigItemCountChanged, this, &KxInsertScrollView::onInsertItem);
	}
}

KxInsertScrollView::~KxInsertScrollView()
{
}

void KxInsertScrollView::customEvent(QEvent* event)
{
	if (event->type() == KEvent::Completed)
	{
		event->setAccepted(true);
	}
	KScrollGalleryView::customEvent(event);
}

void KxInsertScrollView::onInsertItem(const int count)
{
	if (!m_model)
		return;
	auto item = m_model->element(count - 1);
	if (!item)
		return;
	KxInsertTableItem* insertItem = qobject_cast<KxInsertTableItem*>(item);
	if (insertItem)
		insertItem->reportDisplayInfo();
}

void KxInsertScrollView::onRetryButtonHoverd(bool bHoverd)
{
	setCursor(bHoverd ? Qt::PointingHandCursor : Qt::ArrowCursor);
}

KxInsertTableItem::KxInsertTableItem(KGalleryAbstractModel* model, KTableStyleResourceInfo resourceInfo, const drawing::Color& color, KCommand* tableStyleCmd, int index)
	:KxDocerTableStyleGalleryItem(model, resourceInfo, color, tableStyleCmd, "InsertStyleTable", true, ThumbType::Medium),
	m_index(index)
{
	m_currentColor = drawing::Color::PhColor;
	onColorChange(color);
	QString thumbPath = KxOnlineTableResHelper::getThumbCachePath(m_resourceInfo.previewUrlMap[m_thumbType], m_resourceInfo.id);
	if (QFile::exists(thumbPath))
		changeIconColor(m_currentColor);
}

QSize KxInsertTableItem::sizeHint() const
{
	return KLiteStyle::dpiScaledSize(118, 72);
}

void KxInsertTableItem::reportDisplayInfo()
{
	KTableStyleInfoCollect::postInsertDisplayEvent(getTableReportInfo());
}

void KxInsertTableItem::prepareDrawItem(QPainter& dc, const QRect& rc, KGalleryView::ViewType type)
{
	KxDocerTableStyleGalleryItem::prepareDrawItem(dc, rc, type);
	if (m_bColorChanged)
		changeIconColor(m_currentColor);
}

void KxInsertTableItem::setHovered(bool bHovered)
{
	KxDocerTableStyleGalleryItem::setHovered(bHovered);
	if(bHovered)
		KTableStyleInfoCollect::postKLMEvent("docer_inserttable_hover", getTableReportInfo(), true, true, true);
}

void KxInsertTableItem::onColorChange(const drawing::Color& color)
{
	m_bColorChanged = true;
	m_bColorful = color.isEmpty();
	if (m_bColorful)
	{
		static QList<drawing::Color> cycleColors = QList<drawing::Color>()
			<< drawing::Color(drawing::Color::PresetBlack)
			<< drawing::Color(drawing::Color::Accent1)
			<< drawing::Color(drawing::Color::Accent2)
			<< drawing::Color(drawing::Color::Accent3)
			<< drawing::Color(drawing::Color::Accent4)
			<< drawing::Color(drawing::Color::Accent5)
			<< drawing::Color(drawing::Color::Accent6);
		int colorIndex = m_index / 4 % cycleColors.count();
		m_currentColor = cycleColors[colorIndex];
	}
	else
		m_currentColor = color;
	emit changed();
}

void KxInsertTableItem::onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc)
{
	KxDocerTableStyleGalleryItem::onInsertFinished(resourceInfo, reportInfo, errorCode, errorDesc);
	if (resourceInfo.id != m_resourceInfo.id)
		return;

	auto klmReportInfo = getTableReportInfo();
	klmReportInfo.args.insert("act", "use");
	klmReportInfo.args.insert("download_key", m_resourceInfo.downloadKey);

	auto style = KOnlineTableResManager::getInstance().getButtonStyle(m_resourceInfo.bFree ? QStringList() : m_resourceInfo.privileges);
	QString skuType = style.skuKey;
	klmReportInfo.args.insert("pay_type", skuType.isEmpty() ? "open_vip_pro" : ("open_" + skuType));
	klmReportInfo.args.insert("download_type", m_resourceInfo.bFree ? "free_download" : "vip_download");
	if (errorCode != OperatorErrorCode::Success)
	{
		klmReportInfo.args["erro_code"] = QString::number(int(errorCode));
		klmReportInfo.args["erro_reason"] = errorDesc;
	}
	KTableStyleInfoCollect::postInsertButtonClick(klmReportInfo);
}

TableDownloadReportInfo KxInsertTableItem::getTableDownloadInfo()
{
	TableDownloadReportInfo downloadInfo;
	downloadInfo.id = m_resourceInfo.id;
	downloadInfo.downloadKey = m_resourceInfo.downloadKey;
	downloadInfo.md5 = m_resourceInfo.md5;
	downloadInfo.channel = "newest_0_resourcebtn";
	downloadInfo.subChannel = KOnlineTableResManager::getInstance().getPayCsourcePrefix() % "tbtype";
	downloadInfo.clientType = "table_insertion";
	downloadInfo.component = QString("%1_menu_celldrop").arg(KDocerUtils::isWpsApp(this) ? "writer" : "wpp");
	return downloadInfo;
}

KTableStyleInfoCollect::KTableReportInfo KxInsertTableItem::getTableReportInfo()
{
	KTableStyleInfoCollect::KTableReportInfo reportInfo;
	reportInfo.func = QLatin1String("docer_inserttable");
	reportInfo.firstEntry = QLatin1String("insert_table");
	reportInfo.pageName = QLatin1String("inserttable_dropdown_page");
	reportInfo.moduleName = QLatin1String("resource_list");
	reportInfo.elementName = QLatin1String("resource");
	reportInfo.elementType = QLatin1String("resource");

	reportInfo.args["resource_name"] = m_resourceInfo.name;
	reportInfo.args["rid"] = m_resourceInfo.rid;
	reportInfo.args["resource_key"] = m_resourceInfo.resourceKey;
	reportInfo.args["payment_type"] = m_resourceInfo.bFree ? "free" : "pay";
	reportInfo.args["load_status"] = m_loadState != KxDocerTableStyleGalleryItem::LoadState::IconLoadError ? "success" : "fail";
	QString colorType = "dark1";
	if (m_bColorful)
		colorType = QLatin1String("colorful");
	else if (m_currentColor.type() == drawing::Color::Scheme)
		colorType = "color" % QString::number(m_currentColor.getScheme() - drawing::Color::SchemeColor::Accent1 + 1);
	else if (m_currentColor.type() == drawing::Color::SRgb)
		colorType = QLatin1String("customize-color");
	reportInfo.args.insert("color_type", colorType);
	if(!m_bColorful)
		reportInfo.args.insert("color_hex", KThemeColorItem(m_currentColor).toQColor().name().toUpper());
	else
		reportInfo.args.insert("color_hex",QLatin1String("colorful"));
	return reportInfo;
}

void KxInsertTableItem::click()
{
	if (m_loadState == IconLoadError)
	{
		if (m_bRetryHover)
			downloadResource();
		return;
	}

	auto klmReportInfo = getTableReportInfo();

	klmReportInfo.args.insert("act", "download");
	QString payKey = KDocerUtils::generatePayKey();
	klmReportInfo.args.insert("pay_key", payKey);
	klmReportInfo.args.insert("download_key", m_resourceInfo.downloadKey);

	auto style = KOnlineTableResManager::getInstance().getButtonStyle(m_resourceInfo.bFree ? QStringList() : m_resourceInfo.privileges);
	QString skuType = style.skuKey;
	klmReportInfo.args.insert("pay_type", skuType.isEmpty() ? "open_vip_pro" : ("open_" + skuType));
	klmReportInfo.args.insert("download_type", m_resourceInfo.bFree ? "free_download" : "vip_download");

	if (!KxOnlineTableResHelper::checkLogin(LOGIN_SRC))
	{
		klmReportInfo.args["act"] = "login";
		klmReportInfo.args["pay_key"] = "";
		klmReportInfo.args["download_type"] = "";
		KTableStyleInfoCollect::postInsertButtonClick(klmReportInfo);
		emit sigClosePopupWidget();
		return;
	}

	m_loadState = ResourceLoading;
	setLoading(true);
	KOnlineTableResManager::getInstance().downloadResFile(getTableDownloadInfo(), true,
		bindContext(this, [=](DownloadResult result, const QString& savePath) {
			if (!KDocerUtils::isCoreAppMatch(this))
			{
				emit sigDownloadError(getResourceInfo(false));
				return;
			}

			auto _klmReportInfo = klmReportInfo;
			if (result != DownloadResult::Success)
			{
				setLoading(false);
				m_loadState = KxDocerTableStyleGalleryItem::ResourceLoadFinish;
				if (result == DownloadResult::PrivilegeError)
				{
					_klmReportInfo.args["act"] = "pay";
					KTableStyleInfoCollect::postInsertButtonClick(_klmReportInfo);
					KxOnlineTableResHelper::openDocerUnifyPayDialog(getTableDownloadInfo(), payKey);
					emit sigClosePopupWidget();
				}
				else
				{
					_klmReportInfo.args["download_status"] = "fail";
					_klmReportInfo.args["erro_code"] = QString::number((int)result);
					KTableStyleInfoCollect::postInsertButtonClick(_klmReportInfo);
					emit sigDownloadError(getResourceInfo(false));
				}
				return;
			}
			_klmReportInfo.args["download_status"] = "success";
			_klmReportInfo.args["pay_key"] = "";
			KTableStyleInfoCollect::postInsertButtonClick(_klmReportInfo);
			emit insertOnlineResource(getResourceInfo(false), getReportInfo(true), savePath, false);
		}));
}

void KxInsertTableItem::changeIconColor(const drawing::Color& color)
{
	KxDocerTableStyleGalleryItem::changeIconColor(m_currentColor);
	m_bColorChanged = false;
}

KxInsertFootItem::KxInsertFootItem(KGalleryAbstractModel* model)
	:KGalleryModelAbstractItem(model)
{

}

KxInsertFootItem::~KxInsertFootItem()
{
	if (m_loadingTimerId != -1)
		killTimer(m_loadingTimerId);
}

QSize KxInsertFootItem::sizeHint() const
{
	return KLiteStyle::dpiScaledSize(489, 32);
}

bool KxInsertFootItem::hitTestSubIcon(const QPoint& itemPos, const QPoint& mousePos)
{
	if (m_bLoading)
		return false;
	QRect itemRect = QRect(itemPos, sizeHint());

	QFont font;
	font.setPixelSize(KLiteStyle::dpiScaled(14));
	QRect hintRect, retryRect;
	getErrorAreaRect(itemRect, font, hintRect, retryRect);
	if (retryRect.contains(mousePos))
		return true;

	return false;
}

void KxInsertFootItem::setHoveredOnSubIcon(const bool bStatus)
{
	m_bRetryButtonHoverd = bStatus;
	emit sigRetryButtonHoverd(bStatus);
}

bool KxInsertFootItem::hoveredOnSubIcon() const
{
	return m_bRetryButtonHoverd;
}

bool KxInsertFootItem::isClickable() const
{
	return true;
}

void KxInsertFootItem::click()
{
	if (m_bLoading)
		return;
	if (m_bRetryButtonHoverd)
		emit sigLoadNextPage();
}

void KxInsertFootItem::setLoading(bool bLoading)
{
	m_bLoading = bLoading;
	stopLoading();
	if (bLoading)
		m_bSenddedLoad = false;
	emit changed();
}

void KxInsertFootItem::drawItem(QPainter& dc, const QRect& rc, KGalleryView::ViewType type) const
{
	if (m_bLoading)
		drawLoading(dc, rc);
	else
		drawError(dc, rc);
}

void KxInsertFootItem::prepareDrawItem(QPainter& dc, const QRect& rc, KGalleryView::ViewType type)
{
	KGalleryModelAbstractItem::prepareDrawItem(dc, rc, type);
	if (!m_bSenddedLoad && m_bLoading)
	{
		m_bSenddedLoad = true;
		startLoading();
		emit sigLoadNextPage();
	}
}

void KxInsertFootItem::drawLoading(QPainter& dc, const QRect& rc) const
{
	QFont fnt;
	fnt.setPixelSize(KLiteStyle::dpiScaled(12));
	QFontMetrics metrics(fnt);
	int textWidth = metrics.width(tr("Trying to load..."));
	QSize loadingSize = QSize(textWidth, 0) + KLiteStyle::dpiScaledSize(24, 20);

	QRect rect = kuiopt::calcCenterRect(rc, loadingSize);
	drawLoadingRolling(dc, QRect(rect.topLeft(), KLiteStyle::dpiScaledSize(20, 20)));

	QRect textRect = QRect(rect.topLeft() + KLiteStyle::dpiScaledPoint(24, 0), KLiteStyle::dpiScaledSize(textWidth, 20));

	dc.save();

	dc.setFont(fnt);
	dc.setPen(KDocerUtils::getCommonThemeColor("kd-color-text-tertiary"));
	dc.drawText(textRect, Qt::AlignLeft | Qt::AlignVCenter, tr("Trying to load..."));

	dc.restore();
}

void KxInsertFootItem::drawLoadingRolling(QPainter& dc, const QRect& rc) const
{
	QSize rollingSize = KLiteStyle::dpiScaledSize(14, 14);
	QRect rect = kuiopt::calcCenterRect(rc, rollingSize);

	int startAngle = m_loadingRotation + 90;
	QConicalGradient gradient(rect.center(), startAngle);
	gradient.setColorAt(0, QColor(10, 108, 255, 255));
	gradient.setColorAt(0.5, QColor(10, 108, 255, 122));
	gradient.setColorAt(1, QColor(10, 108, 255, 0));

	dc.save();

	qreal penWidth = KLiteStyle::dpiScaled(1.5);
	dc.setRenderHint(QPainter::Antialiasing);
	dc.setPen(QPen(QBrush(gradient), penWidth, Qt::SolidLine, Qt::RoundCap));
	dc.drawArc(rect, (startAngle + 30) * 16, 330 * 16);

	dc.setPen(QPen(QColor(10, 108, 255), penWidth, Qt::SolidLine, Qt::RoundCap));
	dc.drawArc(rect, startAngle * 16, 30 * 16);

	dc.restore();
}

void KxInsertFootItem::drawError(QPainter& dc, const QRect& rc) const
{
	dc.save();

	QFont fnt;
	fnt.setPixelSize(KLiteStyle::dpiScaled(12));

	QRect hintRect, retryRect;
	getErrorAreaRect(rc, fnt, hintRect, retryRect);

	dc.setFont(fnt);
	dc.setPen(KDocerUtils::getCommonThemeColor("kd-color-text-tertiary"));
	dc.drawText(hintRect, Qt::AlignCenter, tr("Network Error"));

	QColor color;
	if (m_bRetryButtonHoverd)
		color = KDocerUtils::getCommonThemeColor("kd-color-state-hover");

	if (color.isValid())
	{
		dc.setPen(color);
		QPainterPath path;
		path.addRoundedRect(retryRect, KLiteStyle::dpiScaled(6), KLiteStyle::dpiScaled(6));
		dc.fillPath(path, QBrush(color));
	}
	dc.setPen(KDocerUtils::getCommonThemeColor("kd-color-text-public"));
	dc.drawText(retryRect, Qt::AlignCenter, tr("Retry"));

	dc.restore();
}

void KxInsertFootItem::getErrorAreaRect(const QRect& rc, const QFont& font, QRect& hintRect, QRect& btnRect) const
{
	QFontMetrics fontMetrics(font);

	int hintTextWidth = fontMetrics.width(tr("Network Error"));
	int retryTextWidth = fontMetrics.width(tr("Retry"));
	QSize errorSize = KLiteStyle::dpiScaledSize(hintTextWidth + retryTextWidth + 12, 22);
	QRect rect = kuiopt::calcCenterRect(rc, errorSize);

	hintRect = QRect(rect.topLeft() + KLiteStyle::dpiScaledPoint(0, 1), KLiteStyle::dpiScaledSize(hintTextWidth, 20));
	btnRect = QRect(rect.topLeft() + KLiteStyle::dpiScaledPoint(hintTextWidth, 0), errorSize - KLiteStyle::dpiScaledSize(hintTextWidth, 0));
}

void KxInsertFootItem::startLoading()
{
	stopLoading();
	m_loadingTimerId =  startTimer(40);
	emit statusChanged();
}

void KxInsertFootItem::stopLoading()
{
	if (m_loadingTimerId != -1)
		killTimer(m_loadingTimerId);
	m_loadingTimerId = -1;
}

void KxInsertFootItem::timerEvent(QTimerEvent* event)
{
	static const int StepAngle = 20;
	m_loadingRotation = (m_loadingRotation - StepAngle) % 360;
	emit statusChanged();
}

KxInsertTableModel::KxInsertTableModel(IKApplication* coreApp, QObject* parent, KGalleryCommand* galleryCmd, KCommand* applyCmd)
	:KGalleryAbstractModel(coreApp, parent),
	m_galleryCmd(galleryCmd),
	m_applyStyleCmd(applyCmd)
{
	KOnlineTableResManager::getInstance().initStyleConfig();
}

KxInsertTableModel::~KxInsertTableModel()
{
}

bool KxInsertTableModel::prepareItems()
{
	loadNextPage();
	return true;
}

KxInsertTableModel::State KxInsertTableModel::getStatus()
{
	return (KxInsertTableModel::State)m_state;
}

void KxInsertTableModel::setState(State state)
{
	if (m_state != state)
	{
		m_state = state;
		emit stateChanged(state);
	}
}

void KxInsertTableModel::appendItems(const QList<KTableStyleResourceInfo>& resourceList)
{
	if (count() == 0 && resourceList.isEmpty())
	{
		setState(NoResource);
		return;
	}

	if (resourceList.count() != TABLESTYLE_REQUEST_COUNT)
		m_bResComplete = true;

	drawing::Color color;
	if (m_galleryCmd)
		color = m_galleryCmd->getInitThemeColorPaneColor();
	for (auto it = resourceList.cbegin(); it != resourceList.cend(); it++)
	{
		int index = count();
		if (m_footItem)
			index--;
		KxInsertTableItem* item = new KxInsertTableItem(this, *it, color, m_galleryCmd, index);
		item->setProperty("qtspyItemFlag", QString("OnlineTablestyle_%1").arg(index));
		addElement(item);

		connect(item, SIGNAL(insertOnlineResource(const KOTSResourceInfo&, const ReportInfo&, const QString&, bool))
			, m_applyStyleCmd, SLOT(applyTableStyle(const KOTSResourceInfo&, const ReportInfo&, const QString&, bool)));
		connect(m_galleryCmd, SIGNAL(sigInsertFinished(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&))
			, item, SLOT(onInsertFinished(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)));

#ifdef Q_OS_MACOS
		connect(m_galleryCmd, SIGNAL(colorChange(const ksodrawing::Color&)), item, SLOT(onColorChange(const ksodrawing::Color&)), Qt::UniqueConnection);
#else
		connect(m_galleryCmd, SIGNAL(colorChange(const drawing::Color&)), item, SLOT(onColorChange(const drawing::Color&)), Qt::UniqueConnection);
#endif

		connect(item, &KxInsertTableItem::sigDownloadError, this, &KxInsertTableModel::sigDownloadError);
		connect(item, &KxInsertTableItem::sigClosePopupWidget, this, &KxInsertTableModel::sigClosePopupWidget);
	}

	if (count() / TABLESTYLE_REQUEST_COUNT >= MAX_PAGE_COUNT)
		m_bResComplete = true;

	if (!m_footItem)
	{
		m_footItem = new KxInsertFootItem(this);
		connect(m_footItem, &KxInsertFootItem::sigLoadNextPage, this, &KxInsertTableModel::loadNextPage);
		connect(m_footItem, &KxInsertFootItem::sigRetryButtonHoverd, this, &KxInsertTableModel::sigRetryButtonHoverd);
		addElement(m_footItem);
	}
	m_footItem->setLoading(true);
	m_footItem->setVisible(!m_bResComplete);
	moveElement(indexOf(m_footItem), count() - 1);
	setState(State::GalleryItem);
}

void KxInsertTableModel::loadNextPage()
{
	if (m_bRequesting || m_bResComplete)
		return;

	if (count() == 0)
		setState(Loading);

	m_bRequesting = true;

	QString position = "PCWPPBGYSXLMB1001";
	if (KDocerUtils::isWpsApp(this))
		position = "PCWZBGYSXLMB1001";

	TableItemRquestInfo requestInfo;
	requestInfo.position = position;
	requestInfo.offset = count() - (m_footItem ? 1 : 0);
	requestInfo.limit = TABLESTYLE_REQUEST_COUNT;

	KxOnlineTableResHelper::requestPublishResource(requestInfo,
		bindContext(this, [=](const QList<KTableStyleResourceInfo>& list) {
			if (!KDocerUtils::isCoreAppMatch(this))
			{
				m_bRequesting = false;
				return;
			}

			appendItems(list);
			m_bRequesting = false;
		}),
		bindContext(this, [=](const DownloadFailInfo&) {
			if (!KDocerUtils::isCoreAppMatch(this))
			{
				m_bRequesting = false;
				return;
			}

			if (count() == 0)
				setState(State::NoInternet);
			else if (m_footItem)
				m_footItem->setLoading(false);
			m_bRequesting = false;
		}));
	return;
}
