#ifndef KXWPPTABLESTYLERECOMMENDHELPER_H_
#define KXWPPTABLESTYLERECOMMENDHELPER_H_
namespace wppoldapi
{
	struct _Slide;
	struct Selection;
	struct _Presentation;
	struct SlideRange;
};

class KxWppTableStyleRecommendHelper: public QObject
{
	Q_OBJECT

public:
	static KxWppTableStyleRecommendHelper* instance();
	HRESULT getLastSelectedSlide(wppoldapi::_Slide** ppSlide);
	HRESULT getSelectedSlideRange(wppoldapi::SlideRange** ppSlideRng);
	HRESULT getSelection(IKApplication* pCoreApp, wppoldapi::Selection** ppSelection);
	bool isTableAvailable();
	HRESULT getActivePresentation(wppoldapi::_Presentation** ppPres);
	float getImageRate(wppoldapi::_Presentation* spPres, const QSize& imageSize);
	bool isSlideImageChanged();
	QString getLastSlideImgBase64();

private:
	KxWppTableStyleRecommendHelper();
	~KxWppTableStyleRecommendHelper();

	bool getLastSlideImg(QImage* img);

private slots:
	void onAppQuit();

private:
	static bool m_appExited;
	static KxWppTableStyleRecommendHelper* m_ins;
	QByteArray m_curSlideImgMd5;
	QString m_curSlideImgStr;
};
#endif 