﻿#include "stdafx.h"
#include "kcomctl/kcommandfactory.h"
#include "kxetcommonheander.h"
#include <kso/shell/kso/kso_tlbex.h>
#include <security/utils.h>
#include <applogic/etapi_old.h>
using namespace etoldapi;
#include <kxshare/kxsubwindow.h>
#include <kxshare/kxtaskpane.h>
#include "kxtpettablestylecommand.h"
#include "kxtponlinetablestylewidget.h"
#include "kxtablestylegallerycommand.h"
#include "kxonlinereshelper.h"
#include "kundotransaction.h"
#include <kdocertoolkit/kdocerutils.h>
#include <smartidentify.h>
#include <src/etarrange/kaietarrangeprocess.h>
#include <src/etarrange/kaietarrangehelper.h>
#include <kdocerfunctional.h>
#include <common/identifytable.h>
#include <ksolite/kxjsonhelper.h>
#include <kcomctl/ktranspreview.h>
#include <ksolite/kdocer/kdocercoreitef.h>
#include <ksolog/kxloggerlite.h>
#include "kdocercorehelper.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
using namespace docer::base;

namespace
{
	#define S_OnlineTableStyle "OnlineTableStyle"
	#define	S_PivotOnlineTableStyle "PivotOnlineTableStyle"
	#define g_strNonEmptyCellLimit "nonEmptyCellLimit"
	#define g_strIsUseDataArrangement "isUseDataArrangement"
	#define g_intNonEmptyCellLimit 10000
};

bool KxTpETOnlineTableStyleCommand::s_identifyTableContinue = true;

HRESULT getActiveWorkbook(_Workbook** prop)
{
	ks_stdptr<IKApplication> spCoreApp = kxApp->coreApplication();
	if (!spCoreApp)
		return E_FAIL;

	ks_stdptr<_Application> spApp = spCoreApp;
	if (!spApp)
		return E_FAIL;

	return spApp->get_ActiveWorkbook(prop);
}

BOOL IsObjectSelected()
{
	ks_stdptr<IKApplication> spCoreApp = kxApp->coreApplication();
	if (!spCoreApp)
		return false;

	ks_stdptr<IKEtView> pETView = spCoreApp->GetActiveView();
	if (!pETView)
		return FALSE;

	ks_stdptr<IKSelection> spSel = pETView->GetSelection();
	if (!spSel)
		return FALSE;

	KsoSelectionType st = 0;
	spSel->GetType(&st);
	st = KsoSelectionTypeMajor(st);

	switch (st)
	{
	case ksoselectiondrawing:
	case ksoselectionchart:
		return TRUE;
		break;
	default:
		return FALSE;
		break;
	}

	return FALSE;
}

bool getSelectionRange(Range** pRange)
{
	ks_stdptr<IKApplication> spCoreApp = kxApp->coreApplication();
	if (!spCoreApp)
		return false;

	ks_stdptr<IKEtView> pETView = spCoreApp->GetActiveView();
	if (!pETView)
		return false;

	ks_stdptr<IKWorksheet> spWorkSheet = pETView->GetActiveWorksheet();
	if (!spWorkSheet)
		return false;

	ks_stdptr<ISheetWndInfos> pSheetWnd = spWorkSheet->GetWndInfos();
	if (!pSheetWnd)
		return false;

	ks_stdptr<IKEtWindow> pWindowInfo = pETView->GetWindow();
	if (!pWindowInfo)
		return false;

	ks_stdptr<IKRanges> spRangs;
	pSheetWnd->GetSelection(pWindowInfo->GetIndex(), &spRangs);
	if (!spRangs)
		return false;

	spWorkSheet->GetRangeByData(spRangs, pRange);
	return pRange;
}

QRect range2QRect(Range* rg)
{
	if (!rg)
		return QRect();
	QRect rgArea;
	do
	{
		long row = 0;
		long column = 0;
		rg->get_Row(&row);
		rg->get_Column(&column);
		if (row == 0 || column == 0)
			break;
		ks_stdptr<Range> pRows;
		rg->get_Rows(&pRows);
		ks_stdptr<Range> pColumns;
		rg->get_Columns(&pColumns);
		if (!pRows || !pColumns)
			break;
		long rowCnt = 0;
		long columnCnt = 0;
		pRows->get_Count(&rowCnt);
		pColumns->get_Count(&columnCnt);
		if (rowCnt == 0 || columnCnt == 0)
			break;
		KComVariant rowValue;
		rg->get_Item(KComVariant(rowCnt), KComVariant(columnCnt), &rowValue);
		if (rowValue == KComVariant())
			break;
		ks_stdptr<IKCoreObject> ptrRowDispatch = V_DISPATCH(&rowValue);
		if (!ptrRowDispatch)
			break;
		ks_stdptr<Range> lastRange = ptrRowDispatch;
		if (!lastRange)
			break;
		long lastRow = 0;
		long lastColumn = 0;
		lastRange->get_Row(&lastRow);
		lastRange->get_Column(&lastColumn);
		if (lastRow == 0 || lastColumn == 0)
			break;
		rgArea = QRect(QPoint(column, row), QPoint(lastColumn, lastRow));
	} while (false);
	return rgArea;
}


KxTpETOnlineTableStyleCommand::KxTpETOnlineTableStyleCommand(KxMainWindow* host, QObject* parent)
	: KxDocerTpTableStyleCommand(host, parent)
{
	m_bGridLineChecked = KDocerUtils::isKsoCmdChecked("FTB_GridSwitch");
	ks_stdptr<IKEtApplication> pApp = KDocerUtils::getCoreApplication(this);
	if (pApp)
	{
		m_spWorksheet = pApp->GetActiveWorksheet();
		if (m_spWorksheet)
		{
			m_spWorksheet->RegisterNotifyFilter(this);
			RANGE rg(m_spWorksheet->GetSheet()->GetBMP());
			m_spWorksheet->GetUsedRange(&rg);
			ISheet* pSheet = m_spWorksheet->GetSheet();
			if (pSheet)
				pSheet->GetBook(&m_spBook);
			if (m_spBook && m_spBook->LeakOperator())
				m_spBook->LeakOperator()->RegisterRange(rg, &m_hWatchedRegion, this);
			
		}
		IKMainWindows* pMainWindows = pApp->GetMainWindows();
		if (pMainWindows)
			pMainWindows->RegisterNotifyFilter(this);
		ks_stdptr<IEventManager> ptrEventMgr = pApp->GetEventManager();
		if (ptrEventMgr)
			ptrEventMgr->RegisterEventSink(
				EVT_Application | EVT_Edit | EVT_Document | EVT_View | EVT_Render,
				this);
	}
}

KxTpETOnlineTableStyleCommand::~KxTpETOnlineTableStyleCommand()
{
	if (m_spWorksheet)
		m_spWorksheet->UnRegisterNotifyFilter(this);
	if (m_hWatchedRegion && m_spBook && m_spBook->LeakOperator())
	{
		m_spBook->LeakOperator()->UnregisterRange(m_hWatchedRegion);
		m_hWatchedRegion = nullptr;
	}
	ks_stdptr<IKEtApplication> pApp = KDocerUtils::getCoreApplication(this);
	if (pApp)
	{
		IKMainWindows* pMainWindows = pApp->GetMainWindows();
		if (pMainWindows)
			pMainWindows->UnRegisterNotifyFilter(this);
		ks_stdptr<IEventManager> ptrEventMgr = pApp->GetEventManager();
		if (ptrEventMgr)
			ptrEventMgr->RemoveEventSink(this);
	}
}

void KxTpETOnlineTableStyleCommand::update()
{
	if (!KDocerUtils::isCoreAppMatch(this))
		return;

	KxDocerTpTableStyleCommand::update();
	bool gridLineSw = KDocerUtils::isKsoCmdChecked("FTB_GridSwitch");
	if (gridLineSw != m_bGridLineChecked)
	{
		m_bGridLineChecked = gridLineSw;
		emit sigGridLineStatuesChange(m_bGridLineChecked);
	}
}

PanelEnableStatus KxTpETOnlineTableStyleCommand::judgeEnableInsertTable()
{
	ks_stdptr<IKApplication> spCoreApp = KDocerUtils::getCoreApplication(this);
	ks_stdptr<_Application> spApp = spCoreApp;
	if (!spApp)
		return PanelEnableStatus::OtherErrorStatus;
	ks_stdptr<_Workbook> spWorkbook;
	spApp->get_ActiveWorkbook(&spWorkbook);
	if (!spWorkbook)
		return PanelEnableStatus::OtherErrorStatus;

	if (spApp->IsInEditingMode())
		return PanelEnableStatus::EtCellEdit;

	ks_stdptr<_Worksheet> spWorksheet;
	spWorkbook->get_ActiveSheet(&spWorksheet);

	VARIANT_BOOL bProtected = VARIANT_FALSE;
	spWorksheet->get_ProtectContents(&bProtected);
	if (bProtected)
	{
		spWorksheet->get_ProtectDrawingObjects(&bProtected);
		if (bProtected)
			return PanelEnableStatus::EtWorkSheetProtect;
	}

	return PanelEnableStatus::Enable;
}

void KxTpETOnlineTableStyleCommand::tableArrangeReco(TableAnalyzeFrom from, RecoType recoType, const std::function<void(bool)>& callback, int worksheetIdx /*= -1*/)
{
	if (m_bAnalyzing)
	{
		if (callback)
			callback(false);
		return;
	}
	auto callbackTask = [=](TableRecoStaus status, bool bSuccess = false) {
		m_curAnalyzeFrom = TableAnalyzeFrom::FromBegin;
		if (status > TableRecoStaus::TableRecogizeBegin)
			emit sigIndentifyStatus(status, from);
		m_bAnalyzing = false;
		if (callback)
			callback(bSuccess);
	};

	auto updateTableList = [this](const QJsonObject& jsonDataObj, bool bServiceRecoResult, qint64 recoStartTime = 0) {
		m_arrangeAssistant.attach(new KArrangeProcessAssistant(jsonDataObj, m_spWorksheet, bServiceRecoResult));
		kaietrecognize::TableRangeInfoList tableList = m_arrangeAssistant->getRangeInfoList();
		int cnt = tableList.elementCnt();
		m_normalTableList.clear();
		for (int i = 0; i < cnt; i++)
		{
			kaietrecognize::ParentChildTableInfo tableInfo = tableList.item(i);
			const auto& rg = tableInfo.parentRangeInfo.allRangeInfo;
			m_normalTableList.append(qMakePair<QRect, bool>(QRect(QPoint(rg.colFrom + 1, rg.rowFrom + 1), QPoint(rg.colTo + 1, rg.rowTo + 1)), tableInfo.childRangeList().size() > 1));
		}

		updateDataTableRect();
		m_bNeedUpdateTable = false;

		if (!bServiceRecoResult)
		{
			quint64 totalCnt = 0;
			QJsonArray finalTableList = jsonDataObj.value("tableList").toArray();
			if (!finalTableList.isEmpty())
			{
				for (const auto& item : qAsConst(finalTableList))
				{
					if (!item.isObject())
						continue;
					QJsonObject subObj = item.toObject();
					if (!subObj.contains("tableStart") || !subObj.contains("tableEnd"))
						continue;
					QJsonArray tableStart = subObj["tableStart"].toArray();
					QJsonArray tableEnd = subObj["tableEnd"].toArray();
					int rowStart = tableStart[0].toInt();
					int rowEnd = tableEnd[0].toInt();
					int colStart = tableStart[1].toInt();
					int colEnd = tableEnd[1].toInt();
					totalCnt += (rowEnd - rowStart + 1) * (colEnd - colStart + 1);
				}
			}
			qint64 recoEndTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
			m_tableApplyInfo.extractTime = recoEndTime - recoStartTime;
			m_tableApplyInfo.nonemptyCellCnt = totalCnt;
			m_tableApplyInfo.areaCellCnt = totalCnt;
			m_tableApplyInfo.isHuge = true;
			KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle reco:  custom areaCellCnt: %1").arg(totalCnt).toStdWString());
		}
	};
	m_curAnalyzeFrom = from;
	emit sigIndentifyStatus(TableRecoStaus::TableRecogizing, from);
	ks_stdptr<IKEtApplication> spCoreApp = KDocerUtils::getCoreApplication(this);
	if (!spCoreApp)
	{
		callbackTask(TableRecoStaus::TableRecogizeFailed);
		return;
	}
	IKWorksheet* spEtView = spCoreApp->GetActiveWorksheet();
	if (worksheetIdx != -1)
	{
		ks_stdptr<_Workbook> spWorkbook;
		HRESULT hr = getActiveWorkbook(&spWorkbook);
		if (FAILED(hr) || !spWorkbook)
		{
			callbackTask(TableRecoStaus::TableRecogizeFailed);
			return;
		}

		ks_stdptr<IKWorksheets> spWorksheets = spWorkbook->GetWorksheets();
		if (!spWorksheets)
		{
			callbackTask(TableRecoStaus::TableRecogizeFailed);
			return;
		}

		if (worksheetIdx >= m_worksheetCount)
		{
			callbackTask(TableRecoStaus::TableRecogizeFailed);
			return;
		}

		spEtView = spWorksheets->GetSheetItem(worksheetIdx);
		if (!spEtView)
		{
			callbackTask(TableRecoStaus::TableRecogizeFailed);
			return;
		}
	}
	if (!spEtView)
	{
		callbackTask(TableRecoStaus::TableRecogizeFailed);
		return;
	}
	updateRegisterRange(spEtView);
	ks_stdptr<IKSheet> spSheet(spEtView);
	if (!spSheet)
	{
		callbackTask(TableRecoStaus::TableRecogizeBegin);
		return;
	}
	qint64 recoStartTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	m_bAnalyzing = true;
	QJsonObject obj;
	s_identifyTableContinue = true;
	GetTableRecogInfo(spSheet, JsonDataType::JsonDataArrangement, identifyTableContinueProc, obj);
	QString strData = JsonHelper::convertQJsonToString(obj);
	if (!s_identifyTableContinue)
	{
		callbackTask(TableRecoStaus::TableRecogizeBegin);
		return;
	}

	QJsonArray tableList = obj.value("tableList").toArray();
	quint64 cellCnt = 0;
	for (const auto& item : qAsConst(tableList))
	{
		if (!item.isObject())
			continue;
		QJsonObject obj = item.toObject();
		if (!obj.contains("cellCount"))
			continue;
		cellCnt += obj["cellCount"].toInt();
	}
	KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle reco:  dataArrangement areaCellCnt: %1").arg(cellCnt).toStdWString());

	if (cellCnt <= 0)
	{
		m_bNeedUpdateTable = false;
		m_normalTableList.clear();
		updateDataTableRect();
		m_arrangeAssistant.attach(new KArrangeProcessAssistant(QJsonObject(), spEtView));
		callbackTask(TableRecoStaus::TableRecogizeNoUpdate, true);
		return;
	}

	if(tableArrangeRecoByCustom(recoType, obj, cellCnt))
	{
		updateTableList(obj, false, recoStartTime);
		callbackTask(TableRecoStaus::TableRecogizeSuucess, true);
		return;
	}

	KAIEtArrangeHelper::getArrangePostData(obj, bindContext(this, [=](bool bSuccess, bool bTooLarge, const KAIEtArrangeHelper::EtArrangeData& arrangeData) {
		if (!KDocerUtils::isCoreAppMatch(this))
		{
			callbackTask(TableRecoStaus::TableRecogizeFailed);
			return;
		}

		if (!bSuccess)
		{
			QJsonObject retryObj;
			if (RecoType::TableStyle == recoType && tableArrangeRecoByCustom(RecoType::Retry, retryObj, 0))
			{
				updateTableList(retryObj, false, recoStartTime);
				callbackTask(TableRecoStaus::TableRecogizeSuucess, true);
				return;
			}

			callbackTask(bTooLarge ? TableRecoStaus::TableRecogizeTooLarge : TableRecoStaus::TableRecogizeFailed);
			return;
		}
		if (!s_identifyTableContinue)
		{
			callbackTask(TableRecoStaus::TableRecogizeBegin);
			return;
		}
		qint64 recoEndTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
		m_tableApplyInfo.extractTime = recoEndTime - recoStartTime;
		m_tableApplyInfo.nonemptyCellCnt = arrangeData.cellCnt;
		m_tableApplyInfo.areaCellCnt = arrangeData.totalCnt;

		KAIEtArrangeHelper::requestTableRecognize(arrangeData, bindContext(this, [=](bool bSuccess, const QJsonObject& objData) {
			if (!KDocerUtils::isCoreAppMatch(this)|| !spCoreApp)
			{
				callbackTask(TableRecoStaus::TableRecogizeFailed);
				return;
			}

			if (!bSuccess)
			{
				QJsonObject retryObj;
				if (RecoType::TableStyle == recoType && tableArrangeRecoByCustom(RecoType::Retry, retryObj, 0))
				{
					updateTableList(retryObj, false, recoStartTime);
					callbackTask(TableRecoStaus::TableRecogizeSuucess, true);
					return;
				}

				callbackTask(TableRecoStaus::TableRecogizeFailed);
				return;
			}
			if (!s_identifyTableContinue)
			{
				callbackTask(TableRecoStaus::TableRecogizeBegin);
				return;
			}
			if (spEtView != spCoreApp->GetActiveWorksheet() && worksheetIdx == -1)
			{
				callbackTask(TableRecoStaus::TableRecogizeFailed);
				return;
			}
			qint64 recoRequestEndTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
			m_tableApplyInfo.identifyTime = recoRequestEndTime - recoEndTime;

			updateTableList(objData, true);
			callbackTask(TableRecoStaus::TableRecogizeSuucess, true);
		}));
	}));
}

bool KxTpETOnlineTableStyleCommand::tableArrangeRecoByCustom(RecoType recoType, QJsonObject& obj, quint64 cellCnt)
{
	if (!m_spWorksheet)
		return false;

	bool isUseDataArrangement = false;
	if (recoType != RecoType::Retry)
	{
		if (obj.isEmpty() || recoType == RecoType::Service)
			return false;

		QJsonObject fpcCombJsonObj;
		int cellCntMacLimit = g_intNonEmptyCellLimit;
		docer::KDocerCoreLiteInterface* docerCoreLite = docer::KLiteDocerCoreMgr::GetDocerCoreLite();
		if (docerCoreLite && docerCoreLite->isFpcInitFinished())
		{
			fpcCombJsonObj = docerCoreLite->getFpcCombJsonObject(g_strGroupModuleId, g_strNonEmptyCellLimitRecId);
			if(fpcCombJsonObj.contains(g_strNonEmptyCellLimit))
				cellCntMacLimit = fpcCombJsonObj.value(g_strNonEmptyCellLimit).toInt();
			if(fpcCombJsonObj.contains(g_strIsUseDataArrangement))
				isUseDataArrangement = fpcCombJsonObj.value(g_strIsUseDataArrangement).toBool();
		}
		if (cellCnt <= cellCntMacLimit)
			return false;
	}
	
	switch (recoType)
	{
	case RecoType::Effect:
	{
		QJsonArray tableList = obj.value("tableList").toArray();
		if (tableList.count() != 1)
			return false;

		if(isUseDataArrangement)
			break;
	}
	case RecoType::Retry:		//目前重试走兜底机制仅存在于单选的表格样式应用中
	case RecoType::TableStyle:
	{
		QJsonArray jsonTableList;
		QJsonObject jsonTableListItem;

		CELL cellSingle = { 0 };
		ks_stdptr<Range> spSeleRg;
		getSelectionRange(&spSeleRg);

		range_helper::ranges rgs;
		app_helper::GetIRanges(spSeleRg, &rgs);
		if (app_helper::IsSingleCell(rgs, &cellSingle, NULL))
		{
			RANGE rgTbl(m_spWorksheet->GetSheet()->GetBMP());
			appcore_helper::GetContinualRangeMaxEx(m_spWorksheet->GetSheet(), cellSingle.row, cellSingle.col, TRUE, &rgTbl);
			m_spWorksheet->GetUsedInRange(&rgTbl);
			rgs->Clear();
			rgs->Append(alg::STREF_THIS_BOOK, rgTbl);
		}

		ks_stdptr<Range> spExpRg;
		m_spWorksheet->GetRangeByData(rgs, &spExpRg);
		QRect expRect = range2QRect(spExpRg);

		QJsonArray jsonStart;
		jsonStart.append((qint64)(expRect.top() - 1));
		jsonStart.append((qint64)(expRect.left() - 1));
		jsonTableListItem.insert("tableStart", jsonStart);
		QJsonArray jsonEnd;
		jsonEnd.append((qint64)(expRect.bottom() - 1));
		jsonEnd.append((qint64)(expRect.right() - 1));
		jsonTableListItem.insert("tableEnd", jsonEnd);

		jsonTableList.append(jsonTableListItem);

		QJsonObject().swap(obj);
		obj.insert("tableList", jsonTableList);

		break;
	}
	default:
		return false;
	}
	return true;
}

void KxTpETOnlineTableStyleCommand::cancelAnalyze()
{
	s_identifyTableContinue = false;
	if (m_arrangeAssistant && (m_bApplingArrangeStyle || m_bApplingArrage))
		m_arrangeAssistant->stopProcess();
	m_bAnalyzing = false;

	if (isBatchApply())
		m_batchList.clear();
}

void KxTpETOnlineTableStyleCommand::getHasMultiChildTable(bool bApplyAll)
{
	bool bAnalyzed = false;
	bool bHasMultiChildTable = false;
	getHasMultiChildTableResult(bApplyAll, bAnalyzed, bHasMultiChildTable);
	emit sigHasMultiChildTableResult(bAnalyzed, bHasMultiChildTable);
}

void KxTpETOnlineTableStyleCommand::getHasMultiChildTableResult(bool bApplyAll, bool& bAnalyzed, bool& bHasMultiChildTable)
{
	if (!m_bNeedUpdateTable && m_arrangeAssistant && !m_bCancelArrange)
	{
		bAnalyzed = true;
		QList<QPair<QRect, bool>> matchTbls;
		getCurrentMatchNormalTableIdx(bApplyAll, false, &matchTbls);
		for (const auto& item : qAsConst(matchTbls))
		{
			if (item.second)
			{
				bHasMultiChildTable = true;
				break;
			}
		}
	}
}

void KxTpETOnlineTableStyleCommand::doScheduleTasks()
{
	if (m_bApplying || m_bArranging)
		return;
	AnsycTask task;
	if (!m_arrangeScheduleList.isEmpty())
	{
		task = m_arrangeScheduleList.takeFirst();
		m_bArranging = true;
	}
	else if (!m_scheduleList.isEmpty())
	{
		task = m_scheduleList.takeFirst();
		m_bApplying = true;
	}

	if (task)
	{
		KPromise::make(task)->then(bindContext(this, [=](closure reslove, closure reject) {
			if (m_bApplying)
				m_bApplying = false;
			else if (m_bArranging)
				m_bArranging = false;
			QMetaObject::invokeMethod(this, &KxTpETOnlineTableStyleCommand::doScheduleTasks, Qt::QueuedConnection);
			reslove();
		}))->run();
	}
	else
	{
		m_bApplying = false;
		m_bArranging = false;
		if(!m_batchList.isEmpty())
			doBatchTasks();
	}
}

void KxTpETOnlineTableStyleCommand::doBatchTasks(int curIdx /*= 1*/)
{
	if (m_bApplying || m_bArranging)
		return;
	AnsycTask task;
	if (!m_batchList.isEmpty())
	{
		task = m_batchList.takeFirst();
		m_bApplying = true;
	}

	if (task)
	{
		qint64 startTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
		emit sendInsertInfo(m_lastApplyRes, ReportInfo(), BatchApplyStart, getErrorDesc(BatchApplyStart));

		KPromise::make(task)->then(bindContext(this, [=](const closure& reslove, const closure& reject) {
			m_bApplying = false;
			QMetaObject::invokeMethod(this, "doBatchTasks", Qt::QueuedConnection, Q_ARG(int, curIdx + 1));

			ReportInfo reportInfo;
			reportInfo.tableNumber = m_worksheetCount;
			reportInfo.currentTableNumber = curIdx;

			m_tableApplyInfo.totalTime = QDateTime::currentDateTime().toMSecsSinceEpoch() - startTime;
			reportInfo.tableApplyInfo = m_tableApplyInfo;
			emit sendInsertInfo(m_lastApplyRes, reportInfo, BatchOneSheetFinished, getErrorDesc(BatchOneSheetFinished));
			reslove();
		}))
		->run();
	}
	else
	{
		m_bApplying = false;

		ReportInfo reportInfo;
		reportInfo.tableNumber = m_worksheetCount;
		emit sendInsertInfo(m_lastApplyRes, reportInfo, BatchApplyFinished, getErrorDesc(BatchApplyFinished));

		if (!m_arrangeScheduleList.isEmpty() || !m_scheduleList.isEmpty())
			doScheduleTasks();
	}
}

void KxTpETOnlineTableStyleCommand::applyTableStyle(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, const QString& filePath, bool hover)
{
	KxLoggerLite::writeInfo(KPluginNameW, 
		QString("tablestyle apply: enter applyTableStyle; resourceInfoId: %1  hover: %2")
			.arg(resourceInfo.id)
			.arg(hover).toStdWString());
		
		
	s_identifyTableContinue = true;
	m_lastApplyRes = resourceInfo;
	m_bHover = hover;

	int curSheetIndex = -1;
	m_worksheetCount = 0;

	if (isBatchApply())
	{
		ks_stdptr<_Workbook> spWorkbook;
		HRESULT hr = getActiveWorkbook(&spWorkbook);
		if (FAILED(hr) || !spWorkbook)
			return;

		ks_stdptr<IKWorksheets> spWorksheets = spWorkbook->GetWorksheets();
		if (!spWorksheets)
			return;

		curSheetIndex = 0;
		m_worksheetCount = spWorksheets->GetSheetCount();
	}
	int curIdx = 1;
	for (; curSheetIndex < m_worksheetCount; curSheetIndex++)
	{
		if (!canSheetApply(curSheetIndex+1))
		{
			ReportInfo tmpReportInfo;
			tmpReportInfo.tableNumber = m_worksheetCount;
			tmpReportInfo.currentTableNumber = curIdx;

			m_tableApplyInfo = TableApplyInfo();
			tmpReportInfo.tableApplyInfo = m_tableApplyInfo;

			curIdx++;
			emit sendInsertInfo(resourceInfo, tmpReportInfo, BatchOneSheetProtectedOrHided, getErrorDesc(BatchOneSheetProtectedOrHided));
			continue;
		}

		auto task = bindContext(this, [=](const closure& reslove, const closure& reject) {
			if (!KDocerUtils::isCoreAppMatch(this))
			{
				emit sendInsertInfo(resourceInfo, reportInfo, LastApplyItemChanged, getErrorDesc(LastApplyItemChanged));
				reslove();
				return;
			}

			if (this->m_lastApplyRes.id != resourceInfo.id ||
				this->m_lastApplyRes.bHover != resourceInfo.bHover ||
				this->m_lastApplyRes.bApplyAll != resourceInfo.bApplyAll)
			{
				if (!resourceInfo.bHover || resourceInfo.bHoverReturnResult)
					emit sendInsertInfo(resourceInfo, reportInfo, LastApplyItemChanged, getErrorDesc(LastApplyItemChanged));
				reslove();
				return;
			}
			
			KxLoggerLite::writeInfo(KPluginNameW, 
				QString("tablestyle apply: start applyTableStyle; resourceInfoId: %1  hover: %2")
					.arg(resourceInfo.id)
					.arg(hover).toStdWString());

			if ((m_previewTr->isInPreview() || !checkRangeNeedUpdate())
				&& (!isBatchApply() || m_worksheetCount == 1))
			{
				KxLoggerLite::writeInfo(KPluginNameW, 
						QString("tablestyle apply: finish tableArrangeReco, tableAnalyzeNoUpdate; resourceInfoId: %1  hover: %2")
							.arg(resourceInfo.id)
							.arg(hover).toStdWString());
							
				m_tableApplyInfo.clearTime();
				singleApplyTableStyle(resourceInfo, reportInfo, filePath, hover);
				reslove();
				return;
			}
			m_tableApplyInfo = TableApplyInfo();
			TableAnalyzeFrom analyzeFrom = isBatchApply() ? TableAnalyzeFrom::FromBatch :
				(hover ? TableAnalyzeFrom::FromPreview : TableAnalyzeFrom::FromApply);
			RecoType recoType = m_lastApplyRes.bApplyAll ? RecoType::Service : RecoType::TableStyle;
			tableArrangeReco(analyzeFrom, recoType, 
				bindContext(this, [=](bool bSuccess) {
					if (!KDocerUtils::isCoreAppMatch(this))
					{
						emit sendInsertInfo(resourceInfo, reportInfo, LastApplyItemChanged, getErrorDesc(LastApplyItemChanged));
						reslove();
						return;
					}

					if (this->m_lastApplyRes.id != resourceInfo.id ||
						this->m_lastApplyRes.bHover != resourceInfo.bHover ||
						this->m_lastApplyRes.bApplyAll != resourceInfo.bApplyAll)
					{
						if (!resourceInfo.bHover || resourceInfo.bHoverReturnResult)
							emit sendInsertInfo(resourceInfo, reportInfo, LastApplyItemChanged, getErrorDesc(LastApplyItemChanged));
						reslove();
						return;
					}
					
					KxLoggerLite::writeInfo(KPluginNameW, 
						QString("tablestyle apply: finish tableArrangeReco; resourceInfoId: %1  hover: %2 recoResult: %3")
							.arg(resourceInfo.id)
							.arg(hover)
							.arg(bSuccess).toStdWString());
					if (bSuccess)
					{
						singleApplyTableStyle(resourceInfo, reportInfo, filePath, hover);
					}
					else if (!hover || resourceInfo.bHoverReturnResult)
					{
						OperatorErrorCode errorCode = s_identifyTableContinue ? TableRecognizeFailed : TableRecognizeCancel;
						emit sendInsertInfo(resourceInfo, reportInfo, errorCode, getErrorDesc(errorCode));
					}
					reslove();
					}), curSheetIndex);
			});
		if (isBatchApply())
			m_batchList.append(task);
		else
			m_scheduleList.append(task);
	}
	if (isBatchApply())
		doBatchTasks(curIdx);
	else
		doScheduleTasks();
}

void KxTpETOnlineTableStyleCommand::singleApplyTableStyle(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, const QString& filePath, bool hover)
{
	bool bAnalyzed = false;
	bool bHasMultiChildTable = false;
	getHasMultiChildTableResult(resourceInfo.bApplyAll, bAnalyzed, bHasMultiChildTable);
	auto _reportInfo(reportInfo);
	_reportInfo.hasMultiChildTable = bHasMultiChildTable;

	bool bHasNormalTable = false;
	bool bHasTableApply = false;
	getHasTableResult(resourceInfo.bApplyAll, bHasNormalTable, bHasTableApply);
	_reportInfo.hasNormalTable = bHasNormalTable;
	_reportInfo.hasTableApply = bHasTableApply;
	_reportInfo.isAreaApply = m_arrangeAssistant->getIsApplyAreaEnable(resourceInfo.bApplyAll);
	if (resourceInfo.bApplyAll || !m_arrangeAssistant->getIsKFpCcombEnableAreaApply())
		m_tableApplyInfo.selectCell = "other";
	else
		m_tableApplyInfo.selectCell = _reportInfo.isAreaApply ? "multi_cell " : "single_cell";

	_reportInfo.tableApplyInfo = m_tableApplyInfo;

	KxDocerTpTableStyleCommand::applyTableStyle(resourceInfo, _reportInfo, filePath, hover);
}

bool KxTpETOnlineTableStyleCommand::canSheetApply(int curIdx)
{
	if (!isBatchApply())
		return true;

	if (IsSecurityDocReadOnly(KDocerUtils::getCoreApplication(this)))
		return false;

	ks_stdptr<IKApplication> spCoreApp = KDocerUtils::getCoreApplication(this);
	ks_stdptr<_Application> spApp = spCoreApp;
	if (!spApp || spApp->IsInEditingMode())
		return false;

	HRESULT hr = E_FAIL;
	ks_stdptr<_Workbook> spWorkbook;
	hr = spApp->get_ActiveWorkbook(&spWorkbook);
	if (FAILED(hr) || !spWorkbook)
		return false;

	ks_stdptr<Worksheets> spWorksheets;
	hr = spWorkbook->get_Worksheets(&spWorksheets);
	if (FAILED(hr) || !spWorksheets)
		return false;

	ks_stdptr<IKCoreObject> spCoreObj;
	hr = spWorksheets->get_Item(CComVariant(curIdx), &spCoreObj);
	ks_stdptr<_Worksheet> spWorksheet = spCoreObj;
	if (FAILED(hr) || !spWorksheet)
		return false;

	VARIANT_BOOL bProtected = VARIANT_FALSE;
	hr = spWorksheet->get_ProtectContents(&bProtected);
	if (FAILED(hr))
		return false;

	if (bProtected)
	{
		hr = spWorksheet->get_ProtectDrawingObjects(&bProtected);
		if (FAILED(hr) || bProtected)
			return false;
	}

	VARIANT varVisible;
	hr = spWorksheet->get_Visible(&varVisible);
	if (FAILED(hr))
		return false;

	return (VT_I4 == V_VT(&varVisible) && etSheetVisible == V_I4(&varVisible));
}

bool KxTpETOnlineTableStyleCommand::isBatchApply()
{
	return m_lastApplyRes.bApplyAll && m_lastApplyRes.bBatchApply && !m_lastApplyRes.bHover;
}

void KxTpETOnlineTableStyleCommand::getHasTableResult(bool bApplyAll, bool& bHasNormalTable, bool& bHasTableApply)
{
	if (!m_bNeedUpdateTable && m_arrangeAssistant && m_normalTableList.count() > 0)
	{
		QList<QPair<QRect, bool>> matchTbls;
		QList<int> applyIdx = getCurrentMatchNormalTableIdx(bApplyAll, false, &matchTbls);
		bHasNormalTable = !applyIdx.isEmpty();
	}

	bool bSelectAreaMatchDataTable = false;
	do
	{
		if (bApplyAll)
			break;

		QRect curSelectArea = getCurrentSelectArea();
		if (!curSelectArea.isValid())
			break;

		ks_stdptr<_Worksheet> spWorkSheet = m_spWorksheet;
		if (!spWorkSheet)
			break;

		ks_stdptr<ListObjects> spListObjs;
		HRESULT hr = spWorkSheet->get_ListObjects(&spListObjs);
		if (FAILED(hr) || !spListObjs)
			break;

		long lCnt = 0;
		hr = spListObjs->get_Count(&lCnt);
		if (FAILED(hr))
			break;

		for (long i = 1; i <= lCnt; i++)
		{
			VARIANT varIndex;
			V_VT(&varIndex) = VT_I4;
			V_I4(&varIndex) = i;

			ks_stdptr<ListObject> spListObj;
			spListObjs->get_Item(varIndex, &spListObj);
			if (FAILED(hr) || !spListObj)
				continue;

			ks_stdptr<Range> spRange;
			spListObj->get_Range(&spRange);
			if (!spRange)
				continue;

			QRect dataTableRect = range2QRect(spRange);
			if (!bSelectAreaMatchDataTable && dataTableRect.intersects(curSelectArea))
				bSelectAreaMatchDataTable = true;

			if (bHasNormalTable)
			{
				if (!dataTableRect.contains(curSelectArea.topLeft())
					|| !dataTableRect.contains(curSelectArea.topRight())
					|| !dataTableRect.contains(curSelectArea.bottomLeft())
					|| !dataTableRect.contains(curSelectArea.bottomRight()))
					continue;

				bHasNormalTable = false;
			}
			
			if (bSelectAreaMatchDataTable && !bHasNormalTable)
				break;
		}
	} while (false);
	bHasTableApply = bHasNormalTable || bSelectAreaMatchDataTable;
}

void KxTpETOnlineTableStyleCommand::clearArrangeInfo()
{
	if (m_arrangeAssistant && m_arrangeAssistant->isContinue())
		cancelAnalyze();

	m_normalTableList.clear();
	m_dataTableList.clear();
	m_pivotTableList.clear();
	m_bNeedUpdateTable = true;
}

TableArrangeResult KxTpETOnlineTableStyleCommand::doApplyArrange(const QString& applyMode, bool bApplyAll)
{
	checkRangeNeedUpdate();
	ks_stdptr<_Workbook> spWorkbook = m_spWorksheet ? m_spWorksheet->GetWorkbook() : nullptr;
	if (!spWorkbook)
		return TableArrangeResult::ArrangeFailed;

	APP_BeginUndoTrans(spWorkbook, TRUE, NULL);
	TableArrangeResult arrangeResult = TableArrangeResult::ArrangeFailed;
	if (!m_bNeedUpdateTable && m_arrangeAssistant && !m_bCancelArrange)
	{
		if(m_normalTableList.isEmpty())
			arrangeResult = TableArrangeResult::ArrangeSheetInvalid;

		QList<int> applyIdx = getCurrentMatchNormalTableIdx(bApplyAll, true);
		if(!applyIdx.isEmpty())
		{
			arrangeResult = TableArrangeResult::ArrangeFailed;
			m_bApplingArrage = true;
			HRESULT hr = m_arrangeAssistant->doArrangeProcessList(applyMode, applyIdx, m_tableApplyInfo);
			m_bApplingArrage = false;
			if (SUCCEEDED(hr))
				arrangeResult = TableArrangeResult::ArrangeSuccess;
		}
	}

	if (!m_spWorksheet || !m_spWorksheet->GetWorkbook())
		return TableArrangeResult::ArrangeFailed;
	APP_EndUndoTRANS((arrangeResult == TableArrangeResult::ArrangeSuccess && !m_bCancelArrange) ? S_OK : E_FAIL, FALSE, TRUE);
	return arrangeResult;
}

STDMETHODIMP_(BOOL) KxTpETOnlineTableStyleCommand::OnFilterNotify(ksoNotify* notify)
{
	switch (notify->notify)
	{
	case ksoNotify::ksoDestroy:
		if (notify->coreObj->GetType() == etCoWorksheet)
		{
			notify->coreObj->UnRegisterNotifyFilter(this);
			if (m_hWatchedRegion && m_spBook && m_spBook->LeakOperator())
			{
				m_spBook->LeakOperator()->UnregisterRange(m_hWatchedRegion);
				m_hWatchedRegion = nullptr;
			}
			clearArrangeInfo();

			if (m_spWorksheet)
				m_spWorksheet->UnRegisterNotifyFilter(this);
			m_spWorksheet = nullptr;
			m_spBook = nullptr;
		}
	case ksoNotify::ksoRemoveItem:
		if (notify->coreObj->GetType() == etCoMainWindow)
		{
			ks_stdptr<IKEtMainWindow> spMainWindow = notify->coreObj;
			if (spMainWindow)
			{
				clearArrangeInfo();
			}
		}
		break;
	}
	return TRUE;
}

STDMETHODIMP_(void) KxTpETOnlineTableStyleCommand::OnUpdate(int msg, const REGION_OPERATION_PARAM* param)
{
	if (!param || !(param->code == rop_InsertCols || param->code == rop_RemoveCols || param->code == rop_InsertRows || param->code == rop_RemoveRows))
		return;
	updateRegisterRange();
	clearArrangeInfo();
}

QRect KxTpETOnlineTableStyleCommand::getCurrentSelectArea()
{
	QRect area;
	do
	{
		ks_stdptr<Range> curRange;
		if (!getSelectionRange(&curRange) || !curRange)
			break;
		area = range2QRect(curRange);
	} while (false);
	return area;
}

void KxTpETOnlineTableStyleCommand::updateDataTableRect()
{
	if (!m_spWorksheet)
		return;
	ks_stdptr<_Worksheet> spWorkSheet = m_spWorksheet;
	if (!spWorkSheet)
		return;

	m_dataTableList.clear();
	m_pivotTableList.clear();
	do 
	{
		ks_stdptr<ListObjects> spListObjs;
		HRESULT hr = spWorkSheet->get_ListObjects(&spListObjs);
		if (FAILED(hr) || !spListObjs)
			break;;

		long lCnt = 0;
		hr = spListObjs->get_Count(&lCnt);
		if (lCnt > 0)
		{
			for (long i = 1; i <= lCnt; i++)
			{
				KComVariant varI(i, VT_I4);
				ks_stdptr<ListObject> spListObj;
				spListObjs->get_Item(varI, &spListObj);
				if (FAILED(hr) || !spListObj)
					continue;
				ks_stdptr<Range> spRange;
				spListObj->get_Range(&spRange);
				if (!spRange)
					continue;
				m_dataTableList << range2QRect(spRange);
			}
		}
	} while (false);
	

	do 
	{
		KComVariant var;
		ks_stdptr<PivotTables> spPivotTables;
		HRESULT hr = spWorkSheet->PivotTables(var, 0, (IKCoreObject**)&spPivotTables);
		if (FAILED(hr) || !spPivotTables)
			break;
		long cnt = 0;
		hr = spPivotTables->get_Count(&cnt);
		if (FAILED(hr))
			break;
		for (long i = 1; i <= cnt; i++)
		{
			KComVariant varI(i, VT_I4);
			ks_stdptr<PivotTable> spPivotTable;
			hr = spPivotTables->Item(varI, &spPivotTable);
			if (FAILED(hr) || !spPivotTable)
				continue;
			ks_stdptr<Range> spRange;
			spPivotTable->get_TableRange1(&spRange);
			if(!spRange)
				continue;
			m_pivotTableList << range2QRect(spRange);
		}
	} while (false);
}

bool KxTpETOnlineTableStyleCommand::isDataTableChanged()
{
	ks_stdptr<_Worksheet> spWorkSheet = m_spWorksheet;
	if (!spWorkSheet)
		return false;

	do
	{
		ks_stdptr<ListObjects> spListObjs;
		HRESULT hr = spWorkSheet->get_ListObjects(&spListObjs);
		if (FAILED(hr) || !spListObjs)
			break;

		long lCnt = 0;
		hr = spListObjs->get_Count(&lCnt);
		if(FAILED(hr))
			break;

		if (lCnt != m_dataTableList.count())
			return true;

		if (lCnt <= 0)
			break;

		for (long i = 1; i <= lCnt; i++)
		{
			KComVariant varI(i, VT_I4);
			ks_stdptr<ListObject> spListObj;
			spListObjs->get_Item(varI, &spListObj);
			if (FAILED(hr) || !spListObj)
				continue;
			ks_stdptr<Range> spRange;
			spListObj->get_Range(&spRange);
			if (!spRange)
				continue;
			if (!m_dataTableList.contains(range2QRect(spRange)))
				return true;
		}
	} while (false);

	do
	{
		KComVariant var;
		ks_stdptr<PivotTables> spPivotTables;
		HRESULT hr = spWorkSheet->PivotTables(var, 0, (IKCoreObject**)&spPivotTables);
		if (FAILED(hr) || !spPivotTables)
			break;
		long cnt = 0;
		hr = spPivotTables->get_Count(&cnt);
		if (FAILED(hr))
			break;

		if (cnt != m_pivotTableList.count())
			return true;

		if (cnt <= 0)
			break;
		for (long i = 1; i <= cnt; i++)
		{
			KComVariant varI(i, VT_I4);
			ks_stdptr<PivotTable> spPivotTable;
			hr = spPivotTables->Item(varI, &spPivotTable);
			if (FAILED(hr) || !spPivotTable)
				continue;
			ks_stdptr<Range> spRange;
			spPivotTable->get_TableRange1(&spRange);
			if (!spRange)
				continue;
			if (!m_pivotTableList.contains(range2QRect(spRange)))
				return true;
		}
	} while (false);

	return false;
}

QList<int> KxTpETOnlineTableStyleCommand::getCurrentMatchNormalTableIdx(bool bApplyAll, bool bArrange, QList<QPair<QRect, bool>>* outMatchTbl /*= nullptr*/)
{
	QRect selectArea;
	if (!bApplyAll)
		selectArea = getCurrentSelectArea();
	int tableCnt = m_normalTableList.count();
	auto curNormalTableList = m_normalTableList;
	QList<int> applyIdx;
	for (int i = 0; i < tableCnt; i++)
	{
		if (bArrange && m_bCancelArrange)
		{
			applyIdx.clear();
			break;
		}
		if (!bApplyAll && !selectArea.intersects(curNormalTableList[i].first))
			continue;
		applyIdx << i;
		if (outMatchTbl)
			outMatchTbl->append(curNormalTableList[i]);
	}
	return applyIdx;
}

void KxTpETOnlineTableStyleCommand::changeHoverStatus(bool bHover)
{
	static const QStringList tableStyleCmds = QStringList() 
		<< QLatin1String("TableStyleGallery") 
		<< QLatin1String("TableStyleGalleryInHomeTab")
		<< QLatin1String("PivotTableStyleGallery")
		<< QLatin1String("PivotTableStyleGalleryInHomeTab")
		<< QLatin1String("PivotOnlineTableStyle")
		<< QLatin1String("PivotOnlineTableStyleInHomeTab")
		<< QLatin1String("OnlineTableStyle")
		<< QLatin1String("OnlineTableStyleInHomeTab");
	for (const QString& cmdId : tableStyleCmds)
	{
		KCommand* cmd = KDocerUtils::findCommand(cmdId);
		if (cmd)
			cmd->setProperty("isInPreview", bHover);
	}
	ks_stdptr<IKEtApplication> cpEtApp = KDocerUtils::getCoreApplication(this);
	if (cpEtApp)
		cpEtApp->SetTableStylePreviewState(bHover);
}

void KxTpETOnlineTableStyleCommand::tableRecognize(TableAnalyzeFrom from)
{
	if (m_bAnalyzing)
		return;
	if(!checkRangeNeedUpdate())
	{
		emit sigIndentifyStatus(TableRecoStaus::TableRecogizeNoUpdate, from);
		return;
	}

	//与前端确认过目前仅排版前会调用该接口，故使用排版兜底逻辑
	//若后续有其他位置使用，考虑是否需要使用其他识别类型
	RecoType recoType = RecoType::Effect;
	m_tableApplyInfo = TableApplyInfo();
	tableArrangeReco(from, recoType);
}

void KxTpETOnlineTableStyleCommand::onLeaveHoverPreview()
{
	auto task = bindContext(this, [=](closure reslove, closure reject) {
		if (!KDocerUtils::isCoreAppMatch(this))
		{
			reslove();
			return;
		}
		
		KxLoggerLite::writeInfo(KPluginNameW, L"tablestyle apply: onLeaveHoverPreview");

		if (m_previewTr->isInPreview())
			m_previewTr->endPreview();
		changeHoverStatus(false);
		updateSheetView();
		reslove();
	}); 
	m_scheduleList.append(task);
	doScheduleTasks();
}

bool __stdcall KxTpETOnlineTableStyleCommand::identifyTableContinueProc()
{
	return s_identifyTableContinue;
}

void KxTpETOnlineTableStyleCommand::onSheetChange()
{
	if (m_spWorksheet && m_spWorksheet->IsDestroyed())
		cancelAnalyze();

	updateRegisterRange();
	clearArrangeInfo();
}

void KxTpETOnlineTableStyleCommand::updateRegisterRange(IKWorksheet* pWorkSheet /*= nullptr*/)
{
	auto pMainWindow = kxApp->findRelativeMainWindowX(m_host);
	if (pMainWindow != kxApp->currentMainWindow())
		return;

	IKApplication* pApp = KDocerUtils::getCoreApplication(this);
	ks_stdptr<IKEtApplication> spEtApp = pApp;
	if (!spEtApp)
		return;

	IKWorksheet* pActiveSheet = pWorkSheet ? pWorkSheet : spEtApp->GetActiveWorksheet();
	if (pActiveSheet == NULL)
		return;
	if(m_spWorksheet && !m_spWorksheet->IsDestroyed())
		m_spWorksheet->UnRegisterNotifyFilter(this);
	if (m_hWatchedRegion && m_spBook && m_spBook->LeakOperator())
	{
		m_spBook->LeakOperator()->UnregisterRange(m_hWatchedRegion);
		m_hWatchedRegion = nullptr;
	}
	if (pActiveSheet != m_spWorksheet)
		m_spWorksheet = pActiveSheet;
	if (!pActiveSheet->IsDestroyed())
	{
		RANGE rg(pActiveSheet->GetSheet()->GetBMP());
		pActiveSheet->GetUsedRange(&rg);
		ISheet* pSheet = pActiveSheet->GetSheet();
		if (pSheet)
		{
			m_spBook = NULL;
			pSheet->GetBook(&m_spBook);
			if(m_spBook && m_spBook->LeakOperator())
				m_spBook->LeakOperator()->RegisterRange(rg, &m_hWatchedRegion, this);
		}
		m_spWorksheet->RegisterNotifyFilter(this);
	}
	else
	{
		m_spWorksheet = nullptr;
		m_spBook = nullptr;
	}
}

bool KxTpETOnlineTableStyleCommand::checkRangeNeedUpdate()
{
	m_bAnalyzing = true;
	auto guard = qScopeGuard([=]() {m_bAnalyzing = false; });

	if(m_bNeedUpdateTable || !m_arrangeAssistant || !m_arrangeAssistant->isContinue())
		return true;

	IKApplication* pApp = KDocerUtils::getCoreApplication(this);
	ks_stdptr<IKEtApplication> spEtApp = pApp;
	if (!spEtApp)
		return true;

	IKWorksheet* pActiveSheet = spEtApp->GetActiveWorksheet();
	if (pActiveSheet == NULL)
		return true;

	if (pActiveSheet != m_spWorksheet || !m_hWatchedRegion)
	{
		updateRegisterRange();
		return true;
	}

	RANGE rg(pActiveSheet->GetSheet()->GetBMP());
	HRESULT hr = pActiveSheet->GetUsedRange(&rg);
	if (FAILED(hr))
		return true;
	QRect curDataArea(QPoint(rg.ColFrom(), rg.RowFrom()), QPoint(rg.ColTo(), rg.RowTo()));

	hr = E_FAIL;
	if(m_spBook && m_spBook->LeakOperator())
		hr = m_spBook->LeakOperator()->GetRegistedRange(m_hWatchedRegion, rg);
	if (FAILED(hr))
		return true;
	QRect lastRegistedArea(QPoint(rg.ColFrom(), rg.RowFrom()), QPoint(rg.ColTo(), rg.RowTo()));

	if (lastRegistedArea != curDataArea)
	{
		updateRegisterRange();
		return true;
	}

	if (isDataTableChanged())
		return true;

	QRect curSelectArea = getCurrentSelectArea();
	if (!curSelectArea.isValid())
		return true;

	if (m_normalTableList.isEmpty() && !m_bNeedUpdateTable)
		return false;

	bool bMatched = false;
	for (const auto& item : qAsConst(m_normalTableList))
	{
		if(!item.first.intersects(curSelectArea))
			continue;
		bMatched = true;
		break;
	}

	if (bMatched)
		return false;
	bool bResult = false;
	if (auto ikDocerCore = getIKDocerCore())
	{
		if (auto helper = ikDocerCore->getEtOnlineTaskPaneHelper())
		{
			bResult = !(helper->getCurrentSelectRangeText().isEmpty());
		}
	}
	return bResult;
}

void KxTpETOnlineTableStyleCommand::updateSheetView()
{
	kxApp->ForceIdle();
	ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_spWorksheet;
	if(spWorksheet)
		app_helper::KViewBatchUpdate _Update(spWorksheet, ERM_SheetChanged);
}

HRESULT KxTpETOnlineTableStyleCommand::OnEvent(IN ET_Event_ID ID, IN KSO_WParam wParam, IN KSO_LParam lParam)
{
	if (ID == EVN_ActiveSheet || ID == EVN_DeActiveWindow)
		onSheetChange();
	else if (ID == EVN_EnterEdit || ID == EVN_Paste)
	{
		//进入编辑态前，有本身有事务的话，回滚事务，避免用户后续操作在预览结束后被回滚
		if (m_previewTr->isInPreview() && m_bHover)
			m_previewTr->endPreview();
	}
	return S_OK;
}

void KxTpETOnlineTableStyleCommand::updateCurtableTableIndex()
{
	TableInfo tableInfo;
	if (KxOnlineTableResHelper::getActiveTableInfo(tableInfo))
	{
		emit updateTableInfo(tableInfo);
	}
}

OperatorErrorCode KxTpETOnlineTableStyleCommand::applyTableByTableInfo(const TableInfo& tableInfo)
{
	QString cmdName = S_OnlineTableStyle;
	if (KxOnlineTableResHelper::isSelectPivotTable())
		cmdName = S_PivotOnlineTableStyle;

	KCommand* tableStyleCmd = kxApp->findRelativeMainWindowX(this)->commands()->command(cmdName);
	if (KProxyCommand* proxyCmd = qobject_cast<KProxyCommand*>(tableStyleCmd))
	{
		proxyCmd->setDelayLoadLib(false);
		proxyCmd->load();
		tableStyleCmd = proxyCmd->targetCommand();

		KxTableStyleGalleryCommand* galleryCommand = qobject_cast<KxTableStyleGalleryCommand*>(tableStyleCmd);
		if (galleryCommand)
		{
			return galleryCommand->activeEtTableStyle(tableInfo);
		}
	}

	return InsertOpError;
}

OperatorErrorCode KxTpETOnlineTableStyleCommand::newAndApplyTableStyleByCmd(const QString& tableName, KxtableStyleParser& parser, TableInfo& tableInfo)
{
	UINT tableStyleId = 0;
	if (!newEtOnlineTableStyle(tableName, parser, &tableStyleId))
	{
		return InsertOpError;
	}

	tableInfo.id = tableStyleId;
	tableInfo.styleOption = parser.getStyleOption();

	return applyTableByTableInfo(tableInfo);
}

OperatorErrorCode KxTpETOnlineTableStyleCommand::newAndApplyTableStyle(const QString& tableName, KxtableStyleParser& parser, bool bApplyAll, bool& bHasDataTable, const QList<QRect> normalTableMatchResult)
{
	UINT tableStyleId = 0;
	if (!newEtOnlineTableStyle(tableName, parser, &tableStyleId))
	{
		return InsertOpError;
	}
	
	DWORD styleOpt = parser.getStyleOption();

	OperatorErrorCode opErrorCode = InsertOpError;
	auto matchArea = normalTableMatchResult;
	if (!bApplyAll)
	{
		if (m_arrangeAssistant->getIsApplyAreaEnable(bApplyAll))
			matchArea.clear();
		matchArea << getCurrentSelectArea();
	}

	do 
	{
		if(!m_spWorksheet)
			break;
		ks_stdptr<_Worksheet> spWorkSheet = m_spWorksheet;
		if(!spWorkSheet)
			break;

		ks_stdptr<ListObjects> spListObjs;
		HRESULT hr = spWorkSheet->get_ListObjects(&spListObjs);
		if(FAILED(hr) || !spListObjs)
			break;

		long lCnt = 0;
		hr = spListObjs->get_Count(&lCnt);
		if(SUCCEEDED(hr))
		{
			for (long i = 1; i <= lCnt; i++)
			{
				KComVariant varI(i, VT_I4);
				ks_stdptr<ListObject> spListObj;
				spListObjs->get_Item(varI, &spListObj);
				if (FAILED(hr) || !spListObj)
					continue;

				if(!bApplyAll)
				{
					ks_stdptr<Range> spRange;
					hr = spListObj->get_Range(&spRange);
					if (FAILED(hr) || !spRange)
						continue;
					QRect rangeRect = range2QRect(spRange);
					bool bMatch = false;
					for (const auto& rg : qAsConst(matchArea))
					{
						if (rangeRect.intersects(rg))
						{
							bMatch = true;
							break;
						}
					}
					if(!bMatch)
						continue;
				}
				bHasDataTable = true;

				spListObj->SetTableStyle(tableStyleId);
				ks_stdptr<PivotTable> table = spListObj;
				if (styleOpt != TableInfo().styleOption)
				{
					spListObj->put_ShowHeaders(alg::IsBitUsed(styleOpt, TSO_HEADER_ROW) ? VARIANT_TRUE : VARIANT_FALSE);
					spListObj->put_ShowTotals(alg::IsBitUsed(styleOpt, TSO_TOTAL_ROW) ? VARIANT_TRUE : VARIANT_FALSE);
					spListObj->put_ShowTableStyleFirstColumn(alg::IsBitUsed(styleOpt, TSO_FIRST_COL) ? VARIANT_TRUE : VARIANT_FALSE);
					spListObj->put_ShowTableStyleLastColumn(alg::IsBitUsed(styleOpt, TSO_LAST_COL) ? VARIANT_TRUE : VARIANT_FALSE);
					spListObj->put_ShowTableStyleRowStripes(alg::IsBitUsed(styleOpt, TSO_BANDED_ROWS) ? VARIANT_TRUE : VARIANT_FALSE);
					spListObj->put_ShowTableStyleColumnStripes(alg::IsBitUsed(styleOpt, TSO_BANDED_COLS) ? VARIANT_TRUE : VARIANT_FALSE);
				}
			}
		}

		KComVariant var;
		ks_stdptr<PivotTables> spPivotTables;
		hr = spWorkSheet->PivotTables(var, 0, (IKCoreObject**)&spPivotTables);
		if(FAILED(hr) || !spPivotTables)
			break;
		hr = spPivotTables->get_Count(&lCnt);
		if(FAILED(hr))
			break;
		for (long i = 1; i <= lCnt; i++)
		{
			KComVariant varI(i, VT_I4);
			ks_stdptr<PivotTable> spPivotTable;
			hr = spPivotTables->Item(varI, &spPivotTable);
			if(FAILED(hr) || !spPivotTable)
				continue;

			if (!bApplyAll)
			{
				ks_stdptr<Range> spRange;
				hr = spPivotTable->get_TableRange1(&spRange);
				if (FAILED(hr) || !spRange)
					continue;
				QRect rangeRect = range2QRect(spRange);
				bool bMatch = false;
				for (const auto& rg : qAsConst(matchArea))
				{
					if (rangeRect.intersects(rg))
					{
						bMatch = true;
						break;
					}
				}
				if (!bMatch)
					continue;
			}
			bHasDataTable = true;
			spPivotTable->SetTableStyle(tableStyleId);
			if (styleOpt != INV_TABLESTYLE_OPTS)
			{
				spPivotTable->put_ShowTableStyleRowHeaders(alg::IsBitUsed(styleOpt, TSO_FIRST_COL) ? VARIANT_TRUE : VARIANT_FALSE);
				spPivotTable->put_ShowTableStyleColumnHeaders(alg::IsBitUsed(styleOpt, TSO_HEADER_ROW) ? VARIANT_TRUE : VARIANT_FALSE);
				spPivotTable->put_ShowTableStyleRowStripes(alg::IsBitUsed(styleOpt, TSO_BANDED_ROWS) ? VARIANT_TRUE : VARIANT_FALSE);
				spPivotTable->put_ShowTableStyleColumnStripes(alg::IsBitUsed(styleOpt, TSO_BANDED_COLS) ? VARIANT_TRUE : VARIANT_FALSE);
			}
		}
		opErrorCode = Success;
	} while (false);
	
	return opErrorCode;
}

OperatorErrorCode KxTpETOnlineTableStyleCommand::applyTableStyleDocument(const KOTSResourceInfo& resourceInfo, const QByteArray& content, bool hover)
{
	KxLoggerLite::writeInfo(KPluginNameW, 
		QString("tablestyle apply: start applyTableStyleDocument; resourceInfoId: %1  hover: %2")
			.arg(resourceInfo.id)
			.arg(hover).toStdWString());
	if (IsObjectSelected())
	{
		if(!hover)
		{
			QString strInfo = tr("The currently selected object is not a table. Please select a table before applying the table style.");
			kxApp->messageBox(strInfo, MB_ICONEXCLAMATION | MB_OK);
		}
		return InsertOpNoActiveTable;
	}

	ks_stdptr<_Workbook> spWorkbook;
	getActiveWorkbook(&spWorkbook);
	if (!spWorkbook)
		return InsertOpError;

	KxtableStyleParser parser;
	parser.setCustomColor(resourceInfo.themeColorKey, resourceInfo.customColor);
	if (!parser.parserContent(QString::fromUtf8(content)))
		return InsertOpParserFailed;

	bool bResetDataTable = true;
	if (hover)
	{
		if (!m_previewTr->isInPreview())
			changeHoverStatus(true);
		else
			bResetDataTable = false;
	}
	else if (!hover)
	{
		changeHoverStatus(false);
		if(m_previewTr->isInPreview())
			m_previewTr->endPreview();
	}
	if (!m_previewTr->isInPreview())
		m_previewTr->beginPreview(__X("KxTpETOnlineTableStyleCommand"));

	TableInfo tableInfo;
	OperatorErrorCode errorCode = Success;
	
	bool bHasNormalTable = false;
	QList<QRect> normalTableMatch;
	if (!m_bNeedUpdateTable && m_arrangeAssistant && m_normalTableList.count() > 0)
	{
		QList<QPair<QRect, bool>> matchTbls;
		QList<int> applyIdx = getCurrentMatchNormalTableIdx(resourceInfo.bApplyAll, false, &matchTbls);
		bHasNormalTable = !applyIdx.isEmpty();
		if(!resourceInfo.bApplyAll)
		{
			for (const auto& item : qAsConst(matchTbls))
				normalTableMatch << item.first;
		}

		if (!applyIdx.isEmpty())
		{
			m_bApplingArrangeStyle = true;
			HRESULT hr = m_arrangeAssistant->doArrangeSwitchColorList(parser, applyIdx, bResetDataTable, resourceInfo.bApplyAll);
			m_bApplingArrangeStyle = false;
			if (SUCCEEDED(hr))
				errorCode = Success;
		}
	}

	QString styleNameHashData = resourceInfo.name %
		resourceInfo.id %
		(resourceInfo.customColor.isEmpty() ? QString::number(resourceInfo.themeColorKey) : resourceInfo.customColor);
	QString styleName = resourceInfo.name.split("_").first() % QLatin1String("_") % KDocerUtils::md5(styleNameHashData.toUtf8()).left(6);
	bool bHasDataTable = false;
	errorCode = newAndApplyTableStyle(styleName, parser, resourceInfo.bApplyAll, bHasDataTable, normalTableMatch);

	if (!bHasDataTable && !bHasNormalTable && !hover && !isBatchApply())
	{
		TableInfo tmpTableInfo;
		errorCode = newAndApplyTableStyleByCmd(styleName, parser, tmpTableInfo);
		KxLoggerLite::writeInfo(KPluginNameW, 
			QString("tablestyle apply: newAndApplyTableStyleByCmd; resourceInfoId: %1  hover: %2  result: %3")
			.arg(resourceInfo.id)
			.arg(hover)
			.arg(errorCode).toStdWString());
	}

	if (!hover)
		m_previewTr->endPreview(errorCode == Success && s_identifyTableContinue);

	updateSheetView();

	if (errorCode == Success && !hover)
	{
		if (KCommand* etTableStyleCmd = kxApp->findRelativeMainWindowX(this)->commands()->command("TableStyleGallery"))
			QMetaObject::invokeMethod(etTableStyleCmd, "onUpdateModel", Qt::QueuedConnection);
	}
	
	KxLoggerLite::writeInfo(KPluginNameW, 
		QString("tablestyle apply: finish applyTableStyleDocument; resourceInfoId: %1  hover: %2  result: %3")
			.arg(resourceInfo.id)
			.arg(hover)
			.arg(errorCode).toStdWString());
	return errorCode;
}

void KxTpETOnlineTableStyleCommand::applyTableArrange(const QString& applyMode, bool bApplyAll)
{
	if (m_bArranging)
	{
		m_bCancelArrange = true;
		if (m_arrangeAssistant && m_bApplingArrage)
			m_arrangeAssistant->stopProcess();
	}
	auto task = bindContext(this, [=](closure reslove, closure reject) {
		if (!KDocerUtils::isCoreAppMatch(this))
		{
			m_bCancelArrange = false;
			reslove();
			return;
		}

		if(!m_bCancelArrange)
		{
			qint64 arrangeStartTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
			KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle process: start applyTableArrange. applyMode: %1").arg(applyMode).toStdWString());

			TableArrangeResult result = doApplyArrange(applyMode, bApplyAll);
			qint64 totalTimes = QDateTime::currentDateTime().toMSecsSinceEpoch() - arrangeStartTime;
			KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle process: finish applyTableArrange. applyMode: %1, result: %2 , total time: %3")
				.arg(applyMode)
				.arg((int)result)
				.arg(totalTimes).toStdWString());
			if (!m_bCancelArrange)
				emit sigArrangeApplyResult(result == TableArrangeResult::ArrangeSuccess, result, totalTimes, m_tableApplyInfo);
		}
		m_bCancelArrange = false;
		reslove();
	});
	m_arrangeScheduleList.clear();
	m_arrangeScheduleList << task;
	doScheduleTasks();
}