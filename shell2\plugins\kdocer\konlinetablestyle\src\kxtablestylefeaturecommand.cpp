﻿#include "stdafx.h"
#include "kxtablestylefeaturecommand.h"
#include "kcomctl/kcommandfactory.h"
#include "ksolite/kwebview/kwebviewcontainer.h"
#include "kdocerbasehelper.h"
#include "kxonlinetablestylejsapi.h"
#include "kxtponlinetablestylewidget.h"
#include "kxonlinereshelper.h"
#include <kdocertoolkit/kdocerutils.h>

#ifdef WPP_PROJECT
#include "kxtpwpptablestylecommand.h"
#else
#include "kxtptablestylecommand.h"
#include "kxtpettablestylecommand.h"
#include <kdocertoolkit/kdocerutils.h>
#endif

KxTableStyleFeatureCommand::KxTableStyleFeatureCommand(KxMainWindow* host, QObject* parent)
	: KTriggerCommand(host, parent)
	, m_featureWidget(nullptr)
{
	setProperty("featureTableStyle", true);

	if (IDocerUserInfo* userInfo = getDocerUserInfo())
	{
		KUserInfoQuery query;
		query.moduleName = KPluginName;
		query.appId = "tableStyleFeature";
		query.privilegePid = "";
		query.queryUrls = QStringList();
		query.privilegeQueryItems = QStringList();

		userInfo->registerQueryInfo(query);
	}


#ifdef WPP_PROJECT
	m_tableStyleCmd = new KxTpWppOnlineTableStyleCommand(host, parent);
#else
	if (KDocerUtils::isEtApp(this))
	{
		m_tableStyleCmd = new KxTpETOnlineTableStyleCommand(host, parent);
		connect(m_tableStyleCmd, SIGNAL(sigGridLineStatuesChange(bool)),
			this, SIGNAL(sigGridLineStatuesChange(bool)));
		connect(m_tableStyleCmd, SIGNAL(sigIndentifyStatus(TableRecoStaus, TableAnalyzeFrom)),
			this, SIGNAL(sigIndentifyStatus(TableRecoStaus, TableAnalyzeFrom)));
		connect(m_tableStyleCmd, SIGNAL(sigArrangeApplyResult(bool, TableArrangeResult, qint64, TableApplyInfo)),
			this, SIGNAL(sigArrangeApplyResult(bool, TableArrangeResult, qint64, TableApplyInfo)));
		connect(this, SIGNAL(sigApplyArrange(const QString&, bool)),
			m_tableStyleCmd, SLOT(applyTableArrange(const QString&, bool)));
		connect(this, SIGNAL(sigAnalyzeTable(TableAnalyzeFrom)), m_tableStyleCmd, SLOT(tableRecognize(TableAnalyzeFrom)));
		connect(this, SIGNAL(sigCancelAnalyze()), m_tableStyleCmd, SLOT(cancelAnalyze()));
		connect(this, SIGNAL(sigUpdateTableView()), m_tableStyleCmd, SLOT(updateSheetView()));
		connect(this, SIGNAL(sigGetHasMultiChildTable(bool)), m_tableStyleCmd, SLOT(getHasMultiChildTable(bool)));
		connect(m_tableStyleCmd, SIGNAL(sigHasMultiChildTableResult(bool, bool)), this, SIGNAL(sigGetHasMultiChildTableResult(bool, bool)));
	}
	else
		m_tableStyleCmd = new KxTpWpsOnlineTableStyleCommand(host, parent);
#endif
	connect(m_tableStyleCmd, SIGNAL(hoverOnlineResourceSuccess()),
		this, SIGNAL(hoverOnlineResourceSuccess()));
	connect(m_tableStyleCmd, SIGNAL(hoverSystemResourceSuccess()),
		this, SIGNAL(hoverSystemResourceSuccess()));
	connect(m_tableStyleCmd, SIGNAL(sendInsertInfo(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)),
		this, SIGNAL(sendInsertInfo(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)));
	connect(m_tableStyleCmd, SIGNAL(sendSystemInsertInfo(int, OperatorErrorCode, const QString&)),
		this, SIGNAL(sendSystemInsertInfo(int, OperatorErrorCode, const QString&)));
	connect(m_tableStyleCmd, &KxDocerTpTableStyleCommand::pannelEnable, this, &KxTableStyleFeatureCommand::pannelEnable);
	connect(m_tableStyleCmd, SIGNAL(updateTableInfo(const TableInfo&)), this, SIGNAL(updateTableInfo(const TableInfo&)));
	connect(this, &KxTableStyleFeatureCommand::sigLeaverHover, m_tableStyleCmd, &KxDocerTpTableStyleCommand::onLeaveHoverPreview);
}

KxTableStyleFeatureCommand::~KxTableStyleFeatureCommand()
{
	if (m_tableStyleCmd)
	{
		delete m_tableStyleCmd;
		m_tableStyleCmd = nullptr;
	}
}

KCommand* KxTableStyleFeatureCommand::clone(QObject* host, QObject* parent)
{
	KxTableStyleFeatureCommand* pCmd =
		new KxTableStyleFeatureCommand(qobject_cast<KxMainWindow*>(host), parent);
	copyProperties(pCmd);
	return pCmd;
}

void KxTableStyleFeatureCommand::update()
{
	if (!KDocerUtils::isCoreAppMatch(this))
		return;

	if(m_tableStyleCmd)
		m_tableStyleCmd->update();
}

void KxTableStyleFeatureCommand::getHasMultiChildTable(bool bApplyAll, bool& bAnalyzed, bool& bHasMultiChildTable)
{
#ifndef WPP_PROJECT
	if (!KDocerUtils::isEtApp(this))
		return;

	if (KxTpETOnlineTableStyleCommand* cmd = qobject_cast<KxTpETOnlineTableStyleCommand*>(m_tableStyleCmd))
		cmd->getHasMultiChildTableResult(bApplyAll, bAnalyzed, bHasMultiChildTable);
#endif
}

void KxTableStyleFeatureCommand::onOpenTpbyAction(const QVariantMap& params)
{
	if (m_featureWidget)
	{
		m_featureWidget->setKLMinfo(params);
		m_featureWidget->callbackToJS("KLM_Changed", params);
	}
}

void KxTableStyleFeatureCommand::applyTableStyle(const KOTSResourceInfo& resourceInfo,
	const ReportInfo& reportInfo, const QString& filePath, bool hover)
{
	if (m_tableStyleCmd)
		m_tableStyleCmd->applyTableStyle(resourceInfo, reportInfo, filePath, hover);
}

void KxTableStyleFeatureCommand::applySystemTableStyle(int resourceIndex, bool hover)
{
	if (m_tableStyleCmd)
		m_tableStyleCmd->applySystemTableStyle(resourceIndex, hover);
}

QWidget* KxTableStyleFeatureCommand::createExtendedWidget(QWidget* parent)
{
	if (!m_featureWidget)
	{
		QWidget* hostWgt = new QWidget(parent);
		QHBoxLayout* hLayout = new QHBoxLayout(hostWgt);
		hLayout->setContentsMargins(0, 0, 0, 0);
		m_featureWidget = new KxOnlineTableStyleWidget(hostWgt, this, true);
		hLayout->addWidget(m_featureWidget);
#ifdef WPP_PROJECT
		QString onlineExtendURL = KxOnlineTableResHelper::getResourcePath(KxOnlineTableResHelper::KOT_OnlineTableStyleFeature);
#else
		QString onlineExtendURL = KxOnlineTableResHelper::getResourcePath(KxOnlineTableResHelper::KOT_WPSFeature);
#endif
		m_featureWidget->init(onlineExtendURL, "konlinetablestyle_feature");
		QVariantMap klmInfo;
		klmInfo["func"] = "docer_infeature";
		klmInfo["second_func"] = "tablestyle";
		klmInfo["first_entry"] = "task";
		klmInfo["second_entry"] = "task_table";
		m_featureWidget->setKLMinfo(klmInfo);
		return hostWgt;
	}
	
	return nullptr;
}

///////////////////////////////////////////////////////////////////
KxWppTableStyleFeatureCommand::KxWppTableStyleFeatureCommand(KxMainWindow* host, QObject* parent)
	: KxTableStyleFeatureCommand(host, parent)
{
}

#ifdef WPP_PROJECT
DECLARE_COMMAND_FACTORY(KxWppTableStyleFeatureCommand, KxMainWindow)
#else
DECLARE_COMMAND_FACTORY(KxTableStyleFeatureCommand, KxMainWindow)
#endif // WPP_PROJECT