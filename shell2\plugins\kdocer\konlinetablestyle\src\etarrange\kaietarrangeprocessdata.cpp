﻿#include "stdafx.h"

#include "kaietarrangeprocessdata.h"
#include <kxshare/kxapplication.h>
#include <smartidentify.h>
#include "applogic/et_applogic_helper.h"

#define COL_WIDTH_FEW 15
#define MAX_COL_DEFAULT_CHAR_CNT 50 //表示超过此阈值就要考虑折行处理
#define COL_WIDTH_MODERATE 30
#define MAX_TABLEHEAD_COL_MAX_CHAR_CNT 20
#define COL_ALIGN_LEFT_MIN_WIDTH 18

static const LPCWSTR CELLIMG_FMLA_PATTERN = __X("=DISPIMG(\"");

float KArrangeProcessHelper::viewWidth2CharsWidth(int viewWidth, IKWorkbook* pWorkbook,bool bNeedDeviation/* = true*/)
{
	INT poundWidth = viewWidth * 20 + 1;
	float charsWidth = app_helper::GetCharsWithColWidth(pWorkbook, poundWidth);
	if (!bNeedDeviation)
		return charsWidth;
	float deviation = 2;//字符的误差
	if (charsWidth <= 20)
		deviation = 2;
	else if (charsWidth <= 35)
		deviation = 0;
	else if (charsWidth <= 55)
		deviation = 1;
	else if (charsWidth <= 75)
		deviation = -0.5;
	else if (charsWidth <= 90)
		deviation = 0;
	else if (charsWidth <= 110)
		deviation = -1.5;
	else if (charsWidth <= 140)
		deviation = -2;
	else if (charsWidth <= 200)
		deviation = -3.5;
	charsWidth = charsWidth + deviation;
	return charsWidth;
}

TableApplyParam::TableApplyParam(WholeEffectType effectType /*= AdaptEffect*/)
	:m_pWholeEffect(nullptr)
{
	setEffectType(effectType);
	bProcessBlank = true;
}

TableApplyParam::~TableApplyParam()
{
	if (m_pWholeEffect)
		delete m_pWholeEffect;
}

void TableApplyParam::setEffectType(WholeEffectType effectType)
{
	if (m_pWholeEffect)
	{
		delete m_pWholeEffect;
		m_pWholeEffect = nullptr;
	}

	m_effectType = effectType;

	if (effectType == CompactEffect)
		m_pWholeEffect = new CompactWholeEffect();
	else if (effectType == LooseEffect)
		m_pWholeEffect = new LooseWholeEffect();
	else if (effectType == AdaptScreenEffect)
		m_pWholeEffect = new AdaptScreenWholeEffect();
	else
		m_pWholeEffect = new AdaptWholeEffect();
}

WholeEffectType TableApplyParam::getEffectType()
{
	return m_effectType;
}

WholeEffectBase* TableApplyParam::getWholeEffectInfo()
{
	return m_pWholeEffect;
}

bool TableApplyParam::isNeedProcessBlank()
{
	return bProcessBlank;
}

QString TableApplyParam::getEffectTypeName() const
{
	QString effect_type;
	switch (m_effectType)
	{
	case AdaptEffect:
		effect_type = "fit";
		break;
	case CompactEffect:
		effect_type = "retrench";
		break;
	case LooseEffect:
		effect_type = "loose";
		break;
	case AdaptScreenEffect:
		effect_type = "adapt_screen";
		break;
	}
	return effect_type;
}

AtomicRange::AtomicRange()
	: iTop(-1), iLeft(-1), iBottom(-1), iRight(-1)
{

}

AtomicRange::AtomicRange(long nT, long nB, long nL, long nR)
	: iTop(nT), iBottom(nB), iLeft(nL), iRight(nR)
{

}

AtomicRange::AtomicRange(const ES_CUBE& cube)
{
	iTop = cube.rowFrom;
	iBottom = cube.rowTo;
	iLeft = cube.colFrom;
	iRight = cube.colTo;
}

AtomicRange::AtomicRange(etoldapi::Range* pRange)
{
	ResetRange(pRange);
}

AtomicRange::AtomicRange(const AtomicRange& rg)
	: iTop(rg.iTop), iBottom(rg.iBottom), iLeft(rg.iLeft), iRight(rg.iRight)
{

}

AtomicRange::~AtomicRange()
{
}

bool AtomicRange::IsOneCell() const
{
	return (iTop == iBottom && iLeft == iRight);
}

bool AtomicRange::IsInvalid() const
{
	return iTop < 0 || iBottom < 0 || iLeft < 0 || iRight < 0 || iTop > iBottom || iLeft > iRight;
}

bool AtomicRange::IsInRange(long iRow, long iCol) const
{
	return (iRow >= iTop && iRow <= iBottom && iCol >= iLeft && iCol <= iRight);
}

bool AtomicRange::IsEqualRange(AtomicRange* pAtomicRange)
{
	if (!pAtomicRange)
		return false;
	return (iTop == pAtomicRange->iTop && iLeft == pAtomicRange->iLeft && iBottom == pAtomicRange->iBottom && iRight == pAtomicRange->iRight);
}

bool AtomicRange::IsOneRowCell() const
{
	return iTop == iBottom;
}

bool AtomicRange::isOneColCell() const
{
	return iLeft == iRight;
}

int AtomicRange::getRowCnt()
{
	return iBottom - iTop + 1;
}

int AtomicRange::getColCnt()
{
	return iRight - iLeft + 1;
}

void AtomicRange::ResetRange(etoldapi::Range* pRange)
{
	if (!pRange)
		return;
	HRESULT hr = S_CONTINUE;
	pRange->get_Row(&iTop);
	pRange->get_Column(&iLeft);
	iTop -= 1;
	iLeft -= 1;
	iBottom = iTop;
	iRight = iLeft;
	KCOMPTR(etoldapi::Range) spRows;
	hr = pRange->get_Rows(&spRows);
	if (SUCCEEDED(hr) && spRows)
	{
		spRows->get_Count(&iBottom);
		iBottom += iTop - 1;
	}
	KCOMPTR(etoldapi::Range) spColumns;
	hr = pRange->get_Columns(&spColumns);
	if (SUCCEEDED(hr) && spColumns)
	{
		spColumns->get_Count(&iRight);
		iRight += iLeft - 1;
	}
}

AtomicCells* MergeCellsList::FindCellInMergeCells(long iRow, long iCol)
{
	for (auto it = begin(); it != end(); ++it)
	{
		AtomicCells* pCells = *it;
		if (pCells && pCells->IsInRange(iRow, iCol))
			return pCells;
	}
	return NULL;
}


AtomicCells* ImgFmlaCellsList::FindCellInImgFmlaCells(AtomicCells* pAtomicRange)
{
	for (auto it = begin(); it != end(); ++it)
	{
		AtomicCells* pCells = *it;
		if (pCells && pCells->IsEqualRange(pAtomicRange))
			return pCells;
	}
	return NULL;
}

AtomicTable::~AtomicTable()
{
	for (size_t i = 0; i < m_vecTableCells.size(); ++i)
		if (m_vecTableCells[i])
			delete m_vecTableCells[i];

	for (size_t i = 0; i < m_rowList.size(); ++i)
		if (m_rowList[i])
			delete m_rowList[i];

	for (size_t i = 0; i < m_colList.size(); ++i)
		if (m_colList[i])
			delete m_colList[i];

}

HRESULT AtomicTable::BuildTable(etoldapi::Range* pRange, const kaietrecognize::TableRangeInfo& tblInfo, const QSharedPointer<TableApplyParam>& pTableApplyParam, IStopProcess* stopProcess)
{
	m_pTableApplyParam.clear();
	if (!pTableApplyParam)
		return E_FAIL;

	m_tblInfo = tblInfo;
	m_pTableApplyParam = pTableApplyParam;

	HRESULT hr = S_CONTINUE;
	AtomicRange userRange(pRange);

	ks_stdptr<etoldapi::Range> spRows;
	hr = pRange->get_Rows(&spRows);
	if (FAILED(hr) || !spRows)
		return E_FAIL;

	ks_stdptr<etoldapi::Range> spCols;
	hr = pRange->get_Columns(&spCols);
	if (FAILED(hr) || !spCols)
		return E_FAIL;

	VARIANT varEmpty;
	V_VT(&varEmpty) = VT_ERROR;
	V_ERROR(&varEmpty) = DISP_E_PARAMNOTFOUND;

	int count = 0;
	long nRowCount = iBottom - iTop + 1;
	long nColCount = iRight - iLeft + 1;
	for (long i = 0; i < nRowCount; ++i)
	{
		long iRow = iTop + i;
		// todo 这里空行能不能优化？
		et_sptr<AtomicRow> pRow = new AtomicRow(iRow);
		if (!pRow)
			continue;

		//判断是否为隐藏行
		KComVariant varRow(iRow - userRange.iTop + 1, VT_I4);
		KComVariant var;
		hr = spRows->get_Item(varRow, varEmpty, &var);
		if (FAILED(hr))
			continue;
		ks_stdptr<etoldapi::Range> spRow = KSmartParam(var).GetInterfaceValue();
		if (!spRow)
			continue;
		KComVariant varHidden;
		spRow->get_Hidden(&varHidden);
		pRow->setHidden(KSmartParam(varHidden).GetBooleanValue());

		for (long j = 0; j < nColCount; ++j)
		{
			if (count++ == 1000)
			{
				QPointer<AtomicTable> spThis = this;
				QCoreApplication::processEvents();
				if (!spThis)
					return E_FAIL;
				count = 0;
				if (stopProcess && !stopProcess->isContinue())
					return E_FAIL;
			}

			long iCol = iLeft + j;
			AtomicCells* pCells = m_mergeCellsList.FindCellInMergeCells(iRow, iCol);
			if (!pCells)
			{
				KComVariant varCol(iCol - userRange.iLeft + 1, VT_I4);
				KComVariant varCell;
				hr = pRange->get_Item(varRow, varCol, &varCell);
				if (SUCCEEDED(hr))
				{
					ks_stdptr<etoldapi::Range> spCell = KSmartParam(varCell).GetInterfaceValue();
					if (spCell)
					{
						// todo 空单元格能不能不创建？
						pCells = NewCells(spCell);
						if (pCells && !pCells->IsOneCell())
						{
							// 如果单元格是被合并的单元格，需要取合并单元格左上角的单元格才能取到内容
							if (pCells->iTop != iRow || pCells->iLeft != iCol)
							{
								KComVariant varRowM(pCells->iTop - userRange.iTop + 1, VT_I4);
								KComVariant varColM(pCells->iLeft - userRange.iLeft + 1, VT_I4);
								KComVariant varCellM;
								hr = pRange->get_Item(varRowM, varColM, &varCellM);
								pCells = NULL;
								if (SUCCEEDED(hr))
								{
									ks_stdptr<etoldapi::Range> spCellM = KSmartParam(varCellM).GetInterfaceValue();
									if (spCellM)
									{
										pCells = NewCells(spCellM);
										if (pCells)
											m_mergeCellsList.push_back(pCells);
									}
								}
							}
							else
							{
								m_mergeCellsList.push_back(pCells);
							}


						}
						//收集单元格
						pRow->vecCells.push_back(pCells);

						//按列收集单元格
						if (m_colList.find(iCol) != m_colList.end())
						{
							AtomicCol* pCol = m_colList.at(iCol);
							if (pCol)
								pCol->vecCells.push_back(pCells);
						}
						else
						{
							et_sptr<AtomicCol> pCol = new AtomicCol(iCol);
							if (pCol)
							{
								//判断是否为隐藏列
								{
									KComVariant var;
									hr = spCols->get_Item(varCol, varEmpty, &var);
									if (FAILED(hr))
										continue;
									ks_stdptr<etoldapi::Range> spCol = KSmartParam(var).GetInterfaceValue();
									if (!spCol)
										continue;
									KComVariant varHidden;
									spCol->get_Hidden(&varHidden);
									pCol->setHidden(KSmartParam(varHidden).GetBooleanValue());
								}

								pCol->vecCells.push_back(pCells);
								m_colList.insert(std::make_pair(iCol, pCol.detach()));
							}
						}

					}
				}
			}
			if (pCells != NULL)
			{
				//收集含有图片的单元格
				if (pCells->isImgFmlaCell() && m_imgFmlaCellsList.FindCellInImgFmlaCells(pCells) == NULL)
					m_imgFmlaCellsList.push_back(pCells);

				if (pCells->iRight > pCells->iLeft)
					j += pCells->iRight - pCells->iLeft;
			}
		}
		//Todo:空行要不要过滤掉 目前suyihong那边返回的行范围还会包含空行,后续如果能保证，这部分代码就可不用
		m_rowList.insert(std::make_pair(iRow, pRow.detach()));

	}
	return S_OK;
}

AtomicCells* AtomicTable::NewCells(etoldapi::Range* pCellRange)
{
	AtomicCells* pCells = new AtomicCells(pCellRange);
	if (!pCells || !m_pTableApplyParam)
		return NULL;

	kaietrecognize::ZoneType zoneType = m_tblInfo.getCellZoneType(pCells->iTop, pCells->iLeft);
	pCells->BuildCells(pCellRange, zoneType);
	float fontSize = 10.0f;
	WholeEffectBase* pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
	if (pWholeEffect)
		fontSize = pWholeEffect->getFontSizeByZoneType(zoneType);
	bool bAllowProcessBlank = m_pTableApplyParam->isNeedProcessBlank();
	pCells->InitCellsProp(fontSize, bAllowProcessBlank);
	m_vecTableCells.push_back(pCells);
	return pCells;
}

AtomicCells* AtomicTable::getMergeCell(long iRow, long iCol)
{
	AtomicCells* pCells = m_mergeCellsList.FindCellInMergeCells(iRow, iCol);
	return pCells;
}

bool isInRange(const AtomicCells* pMergeCells, const ES_CUBE& area)
{
	if (!pMergeCells)
		return false;
	if (area.rowFrom <= pMergeCells->iTop && pMergeCells->iTop <= area.rowTo
		&& area.rowFrom <= pMergeCells->iBottom && pMergeCells->iBottom <= area.rowTo
		&& area.colFrom <= pMergeCells->iLeft && pMergeCells->iLeft <= area.colTo
		&& area.colFrom <= pMergeCells->iRight && pMergeCells->iRight <= area.colTo)
		return true;
	return false;
}

int AtomicTable::getFirstMergeColIdx(kaietrecognize::TableRangeInfo& targetTable)
{
	kaietrecognize::TableRangeInfo tblInfo;
	if (!targetTable.isEmptyTableInfo())
		tblInfo = targetTable;
	else
		tblInfo = m_tblInfo;

	ES_CUBE fillAlterArea = tblInfo.getFillRowAlterArea();

	int idx = -1;
	for (size_t i = 0; i < m_mergeCellsList.size(); ++i)
	{
		AtomicCells* pMergeCells = m_mergeCellsList[i];
		if (pMergeCells && pMergeCells->iTop < pMergeCells->iBottom && isInRange(pMergeCells, fillAlterArea) && (pMergeCells->iLeft < idx || idx < 0))
			idx = pMergeCells->iLeft;
	}
	return MAX(idx, tblInfo.getFirstColIdx());
}

int AtomicTable::getFirstHeaderRowIdx(kaietrecognize::TableRangeInfo& targetTable)
{
	kaietrecognize::TableRangeInfo tblInfo;
	if (!targetTable.isEmptyTableInfo())
		tblInfo = targetTable;
	else
		tblInfo = m_tblInfo;
	if (!tblInfo.headRangeInfoVec.isEmpty())
		return tblInfo.headRangeInfoVec[0].rowFrom;
	return -1;
}

void AtomicTable::checkAndExpandMergeCellHeight(AtomicCells* pEachAtomicCells, bool bEnableAlignLeft)
{
	QVector<int> eachParaTextWidthVec = pEachAtomicCells->getEachParaTextWidthVec();
	double eachLineHeight = pEachAtomicCells->getCellCharHeight();

	//再调行高,判断行高是否需要调整
	double sumWidth = getCellWidth(pEachAtomicCells);
	double calcSumHeight = pEachAtomicCells->estimatedCellHeight(sumWidth);

	//判断是否发生了折行，对单元格进行水平居左设置
	if (bEnableAlignLeft && calcSumHeight > eachLineHeight)
		pEachAtomicCells->setHAlign(etHAlignLeft);

	//计算合并单元格所占的高度
	double realSumHeight = getMergeCellHeight(pEachAtomicCells);
	if (realSumHeight < calcSumHeight)//判断会存在遮挡情况
	{
		double dMoreRowHeight = calcSumHeight - realSumHeight;
		expandMergeCellHeight(pEachAtomicCells, dMoreRowHeight);
	}
}

double AtomicTable::getCellWidth(AtomicRange* pEachAtomicRange)
{
	HRESULT hr = S_CONTINUE;
	double sumWidth = 0;
	for (int colIdx = pEachAtomicRange->iLeft; colIdx <= pEachAtomicRange->iRight; colIdx++)
	{
		KComVariant colWidth;
		double dWidth = getPresetColWidth(colIdx);
		sumWidth += dWidth;
	}
	return sumWidth;
}

double AtomicTable::getPresetColWidth(int iCol)
{
	if (m_colList.find(iCol) != m_colList.end())
	{
		AtomicCol* pEachAtomicCol = m_colList.at(iCol);
		return pEachAtomicCol->getColResultWidth();
	}

	return 0;
}

double AtomicTable::getPresetRowHeight(int iRow)
{
	if (m_rowList.find(iRow) != m_rowList.end())
	{
		AtomicRow* pEachAtomicRow = m_rowList.at(iRow);
		return pEachAtomicRow->getRowResultHeight();
	}

	return 0;
}

void AtomicTable::presetRowHeight(int iRow, double dHeight)
{
	if (m_rowList.find(iRow) != m_rowList.end())
	{
		AtomicRow* pEachAtomicRow = m_rowList.at(iRow);
		return pEachAtomicRow->setRowResultHeight(dHeight);
	}
}

void AtomicTable::getTableSizeAndAve(double& w, double& h, double& wAve, double& hAve)
{
	//更新列宽
	w = 0;
	for (auto iter = m_colList.begin(); iter != m_colList.end(); iter++)
	{
		AtomicCol* pEachAtomicCol = iter->second;
		if (!pEachAtomicCol)
			continue;
		double dWidth = pEachAtomicCol->getColResultWidth();
		w += dWidth;
	}

	//更新列宽平均值
	wAve = 0;
	int colCnt = m_colList.size();
	if (colCnt > 0)
		wAve = w / colCnt;

	//更新行高
	h = 0;
	for (auto iter = m_rowList.begin(); iter != m_rowList.end(); iter++)
	{
		AtomicRow* pEachAtomicRow = iter->second;
		if (!pEachAtomicRow)
			continue;
		double dHeight = pEachAtomicRow->getRowResultHeight();
		h += dHeight;
	}

	//更新行高平均值
	hAve = 0;
	int rowCnt = m_rowList.size();
	if (rowCnt > 0)
		hAve = h / rowCnt;

}

double AtomicTable::getMergeCellHeight(AtomicRange* pEachAtomicRange)
{
	HRESULT hr = S_CONTINUE;
	double realSumHeight = 0;
	for (int rowIdx = pEachAtomicRange->iTop; rowIdx <= pEachAtomicRange->iBottom; rowIdx++)
	{
		double dHeight = getPresetRowHeight(rowIdx);
		realSumHeight += dHeight;
	}
	return realSumHeight;
}

void AtomicTable::expandMergeCellHeight(AtomicRange* pEachAtomicRange, double dMoreRowHeight)
{
	HRESULT hr = S_CONTINUE;
	int rowCnt = pEachAtomicRange->getRowCnt();
	if (rowCnt <= 0)
		return;
	double dEachMoreRowHeight = dMoreRowHeight / rowCnt;
	//每一行平均分配
	for (int rowIdx = pEachAtomicRange->iTop; rowIdx <= pEachAtomicRange->iBottom; rowIdx++)
	{
		double dHeight = getPresetRowHeight(rowIdx);
		dHeight += dEachMoreRowHeight;
		presetRowHeight(rowIdx, dHeight);
	}
}

WholeEffectBase::WholeEffectBase()
	:m_TitleRangeFontSize(-1)
	, m_SubTitleRangeFontSize(-1)
	, m_HeadRangeFontSize(-1)
	, m_ContentRangeFontSize(-1)
	, m_OtherRangeFontSize(-1)
	, m_InfoRangeFontSize(-1)
	, m_RowHeightSpacing(0)
	, m_ColMaxWidth(MAX_COL_DEFAULT_CHAR_CNT)
	, m_ImgCellColMinWidth(20)
	, m_ImgCellRowMinHeight(120)
{

}

WholeEffectBase::~WholeEffectBase()
{

}

double WholeEffectBase::getColMaxWidthWithChar()
{
	return m_ColMaxWidth;
}

double WholeEffectBase::getImgCellColMinWidth()
{
	return m_ImgCellColMinWidth;
}

double WholeEffectBase::getImgCellRowMinHeight()
{
	return m_ImgCellRowMinHeight;
}

double WholeEffectBase::getRowSpaingWithChar()
{
	return m_RowHeightSpacing;
}

single WholeEffectBase::getFontSizeByZoneType(kaietrecognize::ZoneType zoneType)
{
	single fontSize = 10.0f;
	switch (zoneType)
	{
	case kaietrecognize::RowTitle:
		fontSize = getHeadRangeFontSize();
		break;
	case kaietrecognize::BigTitle:
		fontSize = getTitleRangeFontSize();
		break;
	case kaietrecognize::Content:
		fontSize = getContentRangeFontSize();
		break;
	case kaietrecognize::SubTitle:
		fontSize = getSubTitleRangeFontSize();
		break;
	case kaietrecognize::Other:
		fontSize = getOtherRangeFontSize();
		break;
	case kaietrecognize::Info:
		fontSize = getInfoRangeFontSize();
		break;
	case kaietrecognize::Empty:
		break;
	default:
		break;
	}
	return fontSize;
}

single WholeEffectBase::getTitleRangeFontSize()
{
	return m_TitleRangeFontSize;
}

single WholeEffectBase::getHeadRangeFontSize()
{
	return m_HeadRangeFontSize;
}

single WholeEffectBase::getContentRangeFontSize()
{
	return m_ContentRangeFontSize;
}

single WholeEffectBase::getSubTitleRangeFontSize()
{
	return m_SubTitleRangeFontSize;
}

single WholeEffectBase::getOtherRangeFontSize()
{
	return m_OtherRangeFontSize;
}

single WholeEffectBase::getInfoRangeFontSize()
{
	return m_InfoRangeFontSize;
}

AdaptWholeEffect::AdaptWholeEffect()
{
	m_TitleRangeFontSize = 16;
	m_SubTitleRangeFontSize = 14;
	m_HeadRangeFontSize = 11;
	m_ContentRangeFontSize = 10;
	m_OtherRangeFontSize = 10;
	m_InfoRangeFontSize = 10;

	m_RowHeightSpacing = 6;
	m_ColMaxWidth = MAX_COL_DEFAULT_CHAR_CNT;

	m_ImgCellColMinWidth = 20;
	m_ImgCellRowMinHeight = 120;
}

AdaptWholeEffect::~AdaptWholeEffect()
{

}

double AdaptWholeEffect::getColSpacingWithChar(double colWidthWidthChar)
{
	double minSpacing = 0;
	double maxSpacing = 0;

	if (colWidthWidthChar < COL_WIDTH_FEW)
	{
		minSpacing = 1;
		maxSpacing = 3;
	}
	else if (colWidthWidthChar < COL_WIDTH_MODERATE)
	{
		minSpacing = 1;
		maxSpacing = 5;
	}
	else
	{
		minSpacing = 2;
		maxSpacing = 6;
	}
	//Todo:最终的列宽间距计算得再考虑超出一屏的规则
	double spacingWithChar = maxSpacing;
	return spacingWithChar;
}

CompactWholeEffect::CompactWholeEffect()
{
	m_TitleRangeFontSize = 16;
	m_SubTitleRangeFontSize = 14;
	m_HeadRangeFontSize = 11;
	m_ContentRangeFontSize = 10;
	m_OtherRangeFontSize = 10;
	m_InfoRangeFontSize = 10;

	m_RowHeightSpacing = 2;
	m_ColMaxWidth = 40;

	m_ImgCellColMinWidth = 15;
	m_ImgCellRowMinHeight = 110;
}

CompactWholeEffect::~CompactWholeEffect()
{

}

double CompactWholeEffect::getColSpacingWithChar(double colWidthWidthChar)
{
	double minSpacing = 0;
	double maxSpacing = 0;

	if (colWidthWidthChar < COL_WIDTH_FEW)
	{
		minSpacing = 0;
		maxSpacing = 1;
	}
	else if (colWidthWidthChar < COL_WIDTH_MODERATE)
	{
		minSpacing = 0;
		maxSpacing = 2;
	}
	else
	{
		minSpacing = 0;
		maxSpacing = 2;
	}
	//Todo:最终的列宽间距计算得再考虑超出一屏的规则
	double spacingWithChar = maxSpacing;
	return spacingWithChar;
}

LooseWholeEffect::LooseWholeEffect()
{
	m_TitleRangeFontSize = 18;
	m_SubTitleRangeFontSize = 14;
	m_HeadRangeFontSize = 12;
	m_ContentRangeFontSize = 11;
	m_OtherRangeFontSize = 11;
	m_InfoRangeFontSize = 11;

	m_RowHeightSpacing = 14;
	m_ColMaxWidth = MAX_COL_DEFAULT_CHAR_CNT;

	m_ImgCellColMinWidth = 25;
	m_ImgCellRowMinHeight = 150;
}

LooseWholeEffect::~LooseWholeEffect()
{

}

double LooseWholeEffect::getColSpacingWithChar(double colWidthWidthChar)
{
	double minSpacing = 0;
	double maxSpacing = 0;

	if (colWidthWidthChar < COL_WIDTH_FEW)
	{
		minSpacing = 4;
		maxSpacing = 5;
	}
	else if (colWidthWidthChar < COL_WIDTH_MODERATE)
	{
		minSpacing = 4;
		maxSpacing = 7;
	}
	else
	{
		minSpacing = 4;
		maxSpacing = 9;
	}
	//Todo:最终的列宽间距计算得再考虑超出一屏的规则
	double spacingWithChar = maxSpacing;
	return spacingWithChar;
}

TblRowProcessResult::TblRowProcessResult(int iRow)
	:m_dHeight(0)
	, m_iRow(iRow)
{

}

TblRowProcessResult::~TblRowProcessResult()
{

}

TblCellProcessResult::TblCellProcessResult(int iRow, int iCol)
	:m_iRow(iRow)
	, m_iCol(iCol)
	, m_bAllowTextOverflow(false)
{

}

TblProcessResult::TblProcessResult()
{

}

void TblProcessResult::insertCellProResult(TblCellProcessResult* pCellProResult)
{
	if (pCellProResult)
		m_CellProResult.push_back(pCellProResult);
}

TblCellProcessResult* TblProcessResult::findCellProResult(int iRow, int iCol)
{
	for (int i = 0; i < m_CellProResult.size(); i++)
	{
		TblCellProcessResult* pCellProResult = m_CellProResult[i];
		if (pCellProResult && pCellProResult->getRowIdx() == iRow && pCellProResult->getColIdx() == iCol)
		{
			return pCellProResult;
		}
	}
	return nullptr;
}

void AtomicCells::BuildCells(etoldapi::Range* pCell, kaietrecognize::ZoneType zoneType)
{
	// 合并单元格要更新range
	KCOMPTR(etoldapi::Range) spMerge;
	HRESULT hr = pCell->get_MergeArea(&spMerge);
	if (SUCCEEDED(hr) && spMerge)
	{
		long nCellCnt = 0;
		spMerge->get_Count(&nCellCnt);
		if (nCellCnt > 1)
		{
			AtomicRange mergeRange(spMerge);
			*(AtomicRange*)this = mergeRange;
		}
	}

	//初始化相关成员为默认值
	initDefaultProp();
	m_zoneType = zoneType;
	if (kaietrecognize::Info == m_zoneType)
	{
		//TableInfo类型保持原来的对齐方式
		pCell->get_HorizontalAlignment(&m_hAlign);
		pCell->get_VerticalAlignment(&m_VAlign);
	}


	KComVariant varCell;
	hr = pCell->get_Value(etRangeValueDefault, &varCell);
	if (FAILED(hr))
		return;

	if (VarIsEmpty(varCell))
		return;

	VARIANT_BOOL hasFormula;
	pCell->get_HasFormula(&hasFormula);
	if (hasFormula)
	{
		m_bHasFormula = true;
		ks_bstr bsFormula;
		pCell->get_Formula(&bsFormula);
		ks_wstring strFormula = bsFormula.c_str();
		size_t size = strFormula.size();
		size_t pos = strFormula.find(CELLIMG_FMLA_PATTERN);
		if (pos < size)
		{
			m_strContent.clear();
			m_bImgFmlaCell = true;
			return;
		}

	}

	ks_bstr bstrText;
	hr = pCell->get_Text(&bstrText);//这种方式能取到数值类型日期类型等的显示值
	if (SUCCEEDED(hr) && !bstrText.empty())
		m_strContent = bstrText;

}

void AtomicCells::InitCellsProp(float fontSize, bool bAllowProcessBlank)
{
	m_fFontSize = fontSize;
	m_indentLevel = 0;
	m_fontName = QString("Microsoft YaHei");

	if (kaietrecognize::BigTitle == m_zoneType ||
		kaietrecognize::RowTitle == m_zoneType ||
		kaietrecognize::SubTitle == m_zoneType)
		m_bBold = true;
	else
		m_bBold = false;

	initCharHeight();
	if (bAllowProcessBlank && !m_bHasFormula)
		processCellBlank();//处理空格
	doEstWidth();
}

double AtomicCells::estimatedCellHeight(double dCurWidth, bool bTextOverflow/* = false*/)
{
	QVector<int> eachParaTextWidthVec = getEachParaTextWidthVec();
	double eachLineHeight = getCellCharHeight();

	int sumLineCnt = 0;
	if (dCurWidth < 0)
		return eachLineHeight;
	for (int i = 0; i < eachParaTextWidthVec.size(); i++)
	{
		//Todo:后续得考虑对这个viewWidth2CharsWidth方法的依赖去掉
		//Todo:后续得想办法去除对下面方法的依赖
		ks_stdptr<IKEtApplication> pEtApp = kxApp->coreApplication();
		ks_stdptr<IKWorkbook> pWorkbook = pEtApp ? pEtApp->GetActiveDocument() : nullptr;
		if(!pWorkbook)
			return eachLineHeight;
		float eachParaTextWidthCharCnt = KArrangeProcessHelper::viewWidth2CharsWidth(eachParaTextWidthVec[i], pWorkbook);
		//Todo:目前这种算法估算高度会有误差(尤其是在英文放不下的情况)
		int lineCnt = 1;
		if (!bTextOverflow)
		{
			lineCnt = ceil(eachParaTextWidthCharCnt / dCurWidth);
			if (lineCnt < 1)
				lineCnt = 1;
		}
		sumLineCnt += lineCnt;
	}
	//最少也要为一行
	if (sumLineCnt < 1)
		sumLineCnt = 1;
	return eachLineHeight * sumLineCnt;

}

bool AtomicCells::isNeedSpecialApplyProp()
{
	if (!getWrapText() || isModifiedText() ||
		etHAlignCenter != getHAlign() || etVAlignCenter != getVAlign())
	{
		return true;
	}
	return false;
}

float AtomicCells::getEstWidthWithChar()
{
	return m_contentWidthWithChar;
}

QVector<int>& AtomicCells::getEachParaTextWidthVec()
{
	return m_eachParaTextWidthVec;
}

void AtomicCells::doEstWidth()
{
	int cellWidth = 0;//返回值单位:磅
	int cellCharCnt = 0;
	m_bLineBreak = false;
	m_eachParaTextWidthVec.clear();
	m_emptyLineCnt = 0;

	ks_wstring text = m_strContent;
	//获取该单元格最长的字符数
	if (!text.empty())
	{
		size_t size = text.size();
		int startPos = 0;
		size_t multiLinePos = 0;
		bool bFindManualLineBreak = false;
		int maxCharEachPara = 0;//若存在换行符用户换行的情形下，统计每一行的字符最大数
		do
		{
			multiLinePos = text.find('\n', startPos);//换行符所在的位置
			bFindManualLineBreak = (multiLinePos < size);
			if (bFindManualLineBreak)
				m_bLineBreak = true;//存在手动换行
			int endPos = bFindManualLineBreak ? multiLinePos : size - 1;

			//去获取子串
			int eachLineCharCnt = endPos - startPos;
			eachLineCharCnt = eachLineCharCnt + (bFindManualLineBreak ? 0 : 1);

			if (eachLineCharCnt > maxCharEachPara)//更新该段的最大值，注意这个值是否准确
				maxCharEachPara = eachLineCharCnt;

			ks_wstring eachParaText = text.substr(startPos, eachLineCharCnt);//注意这里endPos是否要加一
			if (eachParaText.empty())
				m_emptyLineCnt++;
			else
			{
				int eachParaTextWidth = estTextWidth(eachParaText);
				if (eachParaTextWidth > 0)
					m_eachParaTextWidthVec.push_back(eachParaTextWidth);

				if (cellWidth < eachParaTextWidth)
				{
					cellWidth = eachParaTextWidth;
				}
			}

			startPos = endPos + 1;//更新起始值

		} while (bFindManualLineBreak);

		cellCharCnt = maxCharEachPara;

	}
	//Todo:后续得想办法去除对下面方法的依赖
	ks_stdptr<IKEtApplication> pEtApp = kxApp->coreApplication();
	ks_stdptr<IKWorkbook> pWorkbook = pEtApp ? pEtApp->GetActiveDocument() : nullptr;
	if(!pWorkbook)
		return;
	m_contentWidthWithChar = KArrangeProcessHelper::viewWidth2CharsWidth(cellWidth, pWorkbook);
}

void AtomicCells::processCellBlank()
{
	//仅对表头和内容区进行处理
	if (kaietrecognize::RowTitle == m_zoneType || kaietrecognize::Content == m_zoneType)
	{
		int consecutiveSpacesCnt = 0;//连续空格的数目
		QString strResult;//最终结果
		QString strBlankBuff;//空格缓存
		size_t textIdx = 0;
		bool bAppearRealText = false;//真正内容前面的连续空格不处理

		ks_wstring text = m_strContent;
		while (textIdx < text.size())
		{
			wchar_t eachChar = text.at(textIdx);
			if (eachChar == ' ')
			{
				consecutiveSpacesCnt++;
				strBlankBuff.append(' ');
			}
			else//非空格
			{
				if (consecutiveSpacesCnt >= 5 && eachChar != '\n' && bAppearRealText)//达到了五个且最后不为手动回车且前面已出现过非空格的内容字符
				{
					strResult.append('\n');
					strBlankBuff.clear();
					consecutiveSpacesCnt = 0;
					m_bModifyText = true;
				}

				else if (!strBlankBuff.isEmpty())//把原本的空格给添加进去
				{
					strResult.append(strBlankBuff);
					strBlankBuff.clear();
					consecutiveSpacesCnt = 0;
				}
				if (!bAppearRealText)//标记出现过非空格
					bAppearRealText = true;
				strResult.append(eachChar);
			}

			textIdx++;
		}

		if (m_bModifyText)
		{
			//别漏了最后的空格
			if (!strBlankBuff.isEmpty())
			{
				strResult.append(strBlankBuff);
				strBlankBuff.clear();
			}
			m_strContent = krt::utf16(strResult);
		}
	}
}

double AtomicCells::getCellCharHeight()
{
	return m_dCharHeight;
}

double AtomicCells::getCellEstimatedHeight(double dCurWidth)
{
	int sumLineCnt = 0;
	if (dCurWidth < 0)
		return m_dCharHeight;
	for (int i = 0; i < m_eachParaTextWidthVec.size(); i++)
	{
		ks_stdptr<IKEtApplication> pEtApp = kxApp->coreApplication();
		ks_stdptr<IKWorkbook> pWorkbook = pEtApp ? pEtApp->GetActiveDocument() : nullptr;
		if(!pWorkbook)
			return m_dCharHeight;
		float eachParaTextWidthCharCnt = KArrangeProcessHelper::viewWidth2CharsWidth(m_eachParaTextWidthVec[i], pWorkbook);
		//Todo:目前这种算法估算高度会有误差(尤其是在英文放不下的情况)
		int lineCnt = 1;
		if (!m_bTextOverflow)
		{
			lineCnt = ceil(eachParaTextWidthCharCnt / dCurWidth);
			if (lineCnt < 1)
				lineCnt = 1;
		}
		sumLineCnt += lineCnt;
	}
	sumLineCnt += m_emptyLineCnt;//加上回车造成的空行数目
	//最少也要为一行
	if (sumLineCnt < 1)
		sumLineCnt = 1;
	return m_dCharHeight * sumLineCnt;
}

void AtomicCells::setTextOverflow(bool bTextOverflow)
{
	m_bTextOverflow = bTextOverflow;
}

bool AtomicCells::isContainLineBreak()
{
	return m_bLineBreak;
}

bool AtomicCells::isModifiedText()
{
	return m_bModifyText;
}

ks_wstring AtomicCells::getModifiedText()
{
	return m_strContent;
}

bool AtomicCells::isImgFmlaCell()
{
	return m_bImgFmlaCell;
}

kaietrecognize::ZoneType AtomicCells::getZoneType()
{
	return m_zoneType;
}

void AtomicCells::setWrapText(bool bWrapText)
{
	m_bWrapText = bWrapText;
}

bool AtomicCells::getWrapText()
{
	return m_bWrapText;
}

void AtomicCells::setHAlign(ETHAlign hAlign)
{
	m_hAlign = hAlign;
}

oldapi::ETHAlign AtomicCells::getHAlign()
{
	return m_hAlign;
}

void AtomicCells::setVAlign(ETVAlign vAlign)
{
	m_VAlign = vAlign;
}

oldapi::ETVAlign AtomicCells::getVAlign()
{
	return m_VAlign;
}

float AtomicCells::getFontSize()
{
	return m_fFontSize;
}

int AtomicCells::getIndentLevel()
{
	return m_indentLevel;
}

bool AtomicCells::isFontBold()
{
	return m_bBold;
}

QString AtomicCells::getFontName()
{
	return m_fontName;
}

void AtomicCells::initCharHeight()
{
	if (m_fFontSize <= 11)
		m_dCharHeight = 16.5;
	else if (m_fFontSize <= 12)
		m_dCharHeight = 17.25;
	else if (m_fFontSize <= 14)
		m_dCharHeight = 20.25;
	else if (m_fFontSize <= 16)
		m_dCharHeight = 22.5;
	else if (m_fFontSize <= 18)
		m_dCharHeight = 24.75;
}

int AtomicCells::estTextWidth(ks_wstring text)
{
	QFont font;
	font.setFamily(m_fontName);

	font.setPixelSize(m_fFontSize);
	QFontMetrics fm(font);

	QString txt(krt::fromUtf16(text.c_str()));
	int width = fm.width(txt);
	return width;
}

void AtomicCells::initDefaultProp()
{
	//初始相关成员变量
	m_zoneType = kaietrecognize::Content;
	m_contentWidthWithChar = -1;
	m_emptyLineCnt = 0;

	m_dCharHeight = -1;
	m_bTextOverflow = false;
	m_bLineBreak = false;
	m_bHasFormula = false;
	m_bImgFmlaCell = false;

	m_fFontSize = 10.0f;
	m_indentLevel = 0;
	m_bBold = false;
	m_fontName = QString("Microsoft YaHei");

	m_bWrapText = true;
	m_hAlign = etHAlignCenter;
	m_VAlign = etVAlignCenter;
	m_bModifyText = false;
}

double AtomicCol::getColResultWidth()
{
	return m_dResultWidth;
}

int AtomicCol::getColIdx()
{
	return m_iCol;
}

void AtomicCol::calcColResultWidth(WholeEffectBase* pWholeEffect)
{
	calcColResultTextWidth();
	if (!pWholeEffect)
		return;
	double dSpacing = pWholeEffect->getColSpacingWithChar(m_dResultWidth);
	m_dResultWidth = m_dResultWidth + dSpacing;
	m_dMaxWidth = pWholeEffect->getColMaxWidthWithChar();
	if (m_dResultWidth > m_dMaxWidth)//对列进行最大值的限制
		m_dResultWidth = m_dMaxWidth;

}

void AtomicCol::setColResultWidth(double dWidth)
{
	if (dWidth > m_dMaxWidth)//对列进行最大值的限制
		dWidth = m_dMaxWidth;
	m_dResultWidth = dWidth;
}

void AtomicCol::setColOriginWidth(double dWidth)
{
	m_dOriginWidth = dWidth;
}

void AtomicCol::setColContentAlignLeft()
{
	for (size_t i = 0; i < vecCells.size(); i++)
	{
		AtomicCells* pEachAtomicCells = vecCells.at(i);
		//Todo:合并单元格 是否要跳过???
		if (pEachAtomicCells && kaietrecognize::Content == pEachAtomicCells->getZoneType())
			pEachAtomicCells->setHAlign(etHAlignLeft);
	}
}

void AtomicCol::calcColResultTextWidth()
{
	//内容区域
	m_bNeedAlignLeft = false;
	float contentMaxCharCnt = 0;
	float tableHeadMaxWidthMaxCharCnt = 0;
	float infoMaxCharCnt = 0;

	for (size_t i = 0; i < vecCells.size(); i++)
	{
		AtomicCells* pEachAtomicCells = vecCells.at(i);
		if (!pEachAtomicCells->IsOneCell())//合并单元格 跳过
			continue;
		kaietrecognize::ZoneType zoneType = pEachAtomicCells->getZoneType();
		float estCellWidth = pEachAtomicCells->getEstWidthWithChar();
		if (zoneType == kaietrecognize::Content)
		{
			if (contentMaxCharCnt < estCellWidth)
				contentMaxCharCnt = estCellWidth;
			if (pEachAtomicCells->isContainLineBreak())
				m_bNeedAlignLeft = true;

		}
		else if (zoneType == kaietrecognize::ZoneType::RowTitle)
		{
			if (tableHeadMaxWidthMaxCharCnt < estCellWidth)
				tableHeadMaxWidthMaxCharCnt = estCellWidth;
		}
		else if (zoneType == kaietrecognize::ZoneType::Info)
		{
			if (estCellWidth >= m_dOriginWidth * 2)
			{
				pEachAtomicCells->setTextOverflow(true);
				pEachAtomicCells->setWrapText(false);
			}
			else if (infoMaxCharCnt < estCellWidth)
				infoMaxCharCnt = estCellWidth;

		}
		else
		{
			continue;
		}
	}

	//当表头的列宽字符数在20以上，且超过表体内容最大列宽字符数的2倍，表头换行
	//此时整列的列宽字符数为表头列宽字符数的一半（向上取整）
	if (tableHeadMaxWidthMaxCharCnt >= MAX_TABLEHEAD_COL_MAX_CHAR_CNT && tableHeadMaxWidthMaxCharCnt > contentMaxCharCnt * 2)
		tableHeadMaxWidthMaxCharCnt = ceil((float)tableHeadMaxWidthMaxCharCnt / 2);

	//计算最终的字符数
	float newColCharCnt = std::max(contentMaxCharCnt, tableHeadMaxWidthMaxCharCnt);
	newColCharCnt = std::max(newColCharCnt, infoMaxCharCnt);

	if (newColCharCnt > 50)
	{
		newColCharCnt = 50;
		m_bNeedAlignLeft = true;
	}

	if (m_bNeedAlignLeft && newColCharCnt > COL_ALIGN_LEFT_MIN_WIDTH)
		setColContentAlignLeft();

	m_dResultWidth = newColCharCnt;
}

void AtomicRow::setRowResultHeight(double dHeight)
{
	m_dResultHeight = dHeight;
}

double AtomicRow::getRowResultHeight()
{
	return m_dResultHeight;
}

int AtomicRow::getRowIdx()
{
	return m_iRow;
}

AdaptScreenWholeEffect::AdaptScreenWholeEffect()
{
	m_TitleRangeFontSize = 16;
	m_SubTitleRangeFontSize = 14;
	m_HeadRangeFontSize = 11;
	m_ContentRangeFontSize = 10;
	m_OtherRangeFontSize = 10;
	m_InfoRangeFontSize = 10;

	m_RowHeightSpacing = 0;
	m_ColMaxWidth = 40;

	m_ImgCellColMinWidth = 15;
	m_ImgCellRowMinHeight = 110;
}

AdaptScreenWholeEffect::~AdaptScreenWholeEffect()
{

}

double AdaptScreenWholeEffect::getColSpacingWithChar(double colWidthWidthChar)
{
	return 0;//适应屏幕不需要间距
}


AdaptScreenInfo::AdaptScreenInfo()
{
	m_Type = AdaptScreenNormal;
	m_wViewWithChar = -1;
	m_hViewWithPound = -1;
	m_wTableTotal = -1;
	m_hTableTotal = -1;
	m_wTableAve = -1;
	m_hTableAve = -1;
}

AdaptScreenInfo::~AdaptScreenInfo()
{

}

void AdaptScreenInfo::setViewSize(double w, double h)
{
	m_wViewWithChar = w;
	m_hViewWithPound = h;
}

void AdaptScreenInfo::setTableW(double w)
{
	m_wTableTotal = w;
}

void AdaptScreenInfo::setTableH(double h)
{
	m_hTableTotal = h;
}

void AdaptScreenInfo::setTableWidthAve(double ave)
{
	m_wTableAve = ave;
}

void AdaptScreenInfo::setTableHeightAve(double ave)
{
	m_hTableAve = ave;
}

AdaptScreenType AdaptScreenInfo::getAdaptScreenType()
{
	if (m_wTableTotal < m_wViewWithChar * 0.5)
	{
		if (m_hTableTotal < m_hViewWithPound * 0.5)
			m_Type = AdaptScreenNormal;
		else
		{
			if (m_hTableTotal < m_hViewWithPound)
				m_Type = AdaptScreenRowFirst;
			else
				m_Type = AdaptScreenColFirst;
		}
	}
	else
		m_Type = AdaptScreenColFirst;
	QVector<QString> typeNames = { "Normal","RowFirst","ColFirst" };
	QString name = typeNames[m_Type];
	qDebug() << "(adaptScreen-before)AdaptScreenType = " << name;

	return m_Type;
}

adaptScreenStrategy::adaptScreenStrategy(AtomicTable* pTable, AdaptScreenInfo* pAdaptInfo)
	:m_pAtomicTable(pTable)
	,m_pAdaptInfo(pAdaptInfo)
{

}

adaptScreenStrategy::~adaptScreenStrategy()
{

}

void adaptScreenStrategy::updateTableTotalWidth()
{
	//计算数据
	double wTableAdapt = 0;
	for (auto iter = m_pAtomicTable->m_colList.begin(); iter != m_pAtomicTable->m_colList.end(); iter++)
	{
		AtomicCol* pEachAtomicCol = iter->second;
		if (!pEachAtomicCol)
			continue;
		double dWidth = pEachAtomicCol->getColResultWidth();
		wTableAdapt += dWidth;
	}
	m_pAdaptInfo->setTableW(wTableAdapt);

	//更新列宽平均值
	double ave = 0;
	int colCnt = m_pAtomicTable->m_colList.size();
	if (colCnt > 0)
	{
		ave = m_pAdaptInfo->getTableW() / colCnt;
		m_pAdaptInfo->setTableWidthAve(ave);
	}
}

void adaptScreenStrategy::updateTableTotalHeight()
{
	//计算数据
	double hTableAdapt = 0;
	for (auto iter = m_pAtomicTable->m_rowList.begin(); iter != m_pAtomicTable->m_rowList.end(); iter++)
	{
		AtomicRow* pEachAtomicRow = iter->second;
		if (!pEachAtomicRow)
			continue;
		double dHeight = pEachAtomicRow->getRowResultHeight();
		hTableAdapt += dHeight;
	}
	m_pAdaptInfo->setTableH(hTableAdapt);

	//更新行高平均值
	double ave = 0;
	int rowCnt = m_pAtomicTable->m_rowList.size();
	if (rowCnt > 0)
	{
		ave = m_pAdaptInfo->getTableH() / rowCnt;
		m_pAdaptInfo->setTableHeightAve(ave);
	}
}

void adaptScreenStrategy::printfDebugInfo()
{
	//更新
	updateTableTotalWidth();
	updateTableTotalHeight();
	qDebug() << "(adaptScreen-after)adaptTbl:" << "W=" << m_pAdaptInfo->getTableW() << ",Wave" << m_pAdaptInfo->getWidthAve() << "[char]";
	qDebug() << "(adaptScreen-after)adaptTbl:" << "H=" << m_pAdaptInfo->getTableH() << ",Have" << m_pAdaptInfo->getHeightAve() << "[pound]";
	qDebug() << "(adaptScreen-after)view:" << "W=" << m_pAdaptInfo->getViewW() << "[char]"
		<< ",H=" << m_pAdaptInfo->getViewH() << "[pound]";
}

double adaptScreenStrategy::adaptColWidth(double wTable, double W)
{
	double wNewTable = 0;
	//优化列宽
	for (auto iter = m_pAtomicTable->m_colList.begin(); iter != m_pAtomicTable->m_colList.end(); iter++)
	{
		AtomicCol* pEachAtomicCol = iter->second;
		if (!pEachAtomicCol)
			continue;
		double dWidth = pEachAtomicCol->getColResultWidth();
		double dNewWidth = (dWidth / wTable) * W;
		pEachAtomicCol->setColResultWidth(dNewWidth);
		wNewTable += dNewWidth;
	}
	//Todo:考虑一下要不要在这里更新表格最新
	return wNewTable;
}

double adaptScreenStrategy::adaptRowHeight(double hTable, double H)
{
	double hNewTable = 0;
	//优化行高
	for (auto iter = m_pAtomicTable->m_rowList.begin(); iter != m_pAtomicTable->m_rowList.end(); iter++)
	{
		AtomicRow* pEachAtomicRow = iter->second;
		if (!pEachAtomicRow)
			continue;
		double dHeight = pEachAtomicRow->getRowResultHeight();
		double dNewHeight = (dHeight / hTable) * H;
		pEachAtomicRow->setRowResultHeight(dNewHeight);
		hNewTable += dNewHeight;
	}
	//Todo:考虑一下要不要在这里更新表格最新
	return hNewTable;
}

adaptScreenNormalStrategy::adaptScreenNormalStrategy(AtomicTable* pTable, AdaptScreenInfo* pAdaptInfo)
	:adaptScreenStrategy(pTable, pAdaptInfo)
{

}

adaptScreenNormalStrategy::~adaptScreenNormalStrategy()
{

}

void adaptScreenNormalStrategy::doAdaptScreen()
{
	adaptColWidth(m_pAdaptInfo->getTableW(), m_pAdaptInfo->getViewW() / 2);
	adaptRowHeight(m_pAdaptInfo->getTableH(), m_pAdaptInfo->getViewH() / 2);
}

adaptScreenColFirstStrategy::adaptScreenColFirstStrategy(AtomicTable* pTable, AdaptScreenInfo* pAdaptInfo)
	:adaptScreenStrategy(pTable, pAdaptInfo)
{

}

adaptScreenColFirstStrategy::~adaptScreenColFirstStrategy()
{

}

void adaptScreenColFirstStrategy::doAdaptScreen()
{
	double wView = m_pAdaptInfo->getViewW();//单位：字符数
	double hView = m_pAdaptInfo->getViewH();//单位：磅

	if (wView < m_pAdaptInfo->getTableW())
	{
		//对超过平均列宽的列进行折行处理
		double wAve = m_pAdaptInfo->getWidthAve();
		for (auto iter = m_pAtomicTable->m_colList.begin(); iter != m_pAtomicTable->m_colList.end(); iter++)
		{
			AtomicCol* pEachAtomicCol = iter->second;
			if (!pEachAtomicCol)
				continue;
			double dWidth = pEachAtomicCol->getColResultWidth();
			if (dWidth > wAve * 2)
			{
				//对该列进行折行处理
				adaptCellsInCol(pEachAtomicCol, wAve * 2);

			}
		}
		//Todo:之后得更新表格的新高度宽度以及其平均值
		updateTableTotalWidth();
		updateTableTotalHeight();
		if (wView < m_pAdaptInfo->getTableW())
		{
			//行高优化
			adaptRowHeightToViewH();
		}
	}

	//Todo:之后得更新表格的新高度宽度以及其平均值
	updateTableTotalWidth();
	updateTableTotalHeight();

	if (wView >= m_pAdaptInfo->getTableW())
	{
		adaptColWidth(m_pAdaptInfo->getTableW(), wView);
		//行高优化
		adaptRowHeightToViewH();
	}
}

void adaptScreenColFirstStrategy::adaptCellsInCol(AtomicCol* pAtomicCol, double newWidth)
{
	//设置新的列宽
	pAtomicCol->setColResultWidth(newWidth);
	//设置了列宽之后，这一列的行高需要重新调整
	std::vector<AtomicCells*> vecCells = pAtomicCol->vecCells;
	for (size_t i = 0; i < vecCells.size(); i++)
	{
		AtomicCells* pEachAtomicCells = vecCells.at(i);
		if (!pEachAtomicCells)
			continue;
		m_pAtomicTable->checkAndExpandMergeCellHeight(pEachAtomicCells, false);
	}
	//对齐方式设置为 水平居左
	pAtomicCol->setColContentAlignLeft();
}

void adaptScreenColFirstStrategy::adaptRowHeightToViewH()
{
	double wView = m_pAdaptInfo->getViewW();//单位：字符数
	double hView = m_pAdaptInfo->getViewH();//单位：磅

	double scaleH = hView / m_pAdaptInfo->getTableH();
	if (scaleH > 2)//情况1
		adaptRowHeight(m_pAdaptInfo->getTableH(), hView / 2);
	else if (scaleH >= 1 && scaleH <= 2)//情况2
		adaptRowHeight(m_pAdaptInfo->getTableH(), hView);
	else//情况3
	{
		for (auto iter = m_pAtomicTable->m_rowList.begin(); iter != m_pAtomicTable->m_rowList.end(); iter++)
		{
			AtomicRow* pEachAtomicRow = iter->second;
			if (!pEachAtomicRow)
				continue;
			double dHeight = pEachAtomicRow->getRowResultHeight();
			pEachAtomicRow->setRowResultHeight(dHeight + 2);
		}
	}
}

adaptScreenRowFirstStrategy::adaptScreenRowFirstStrategy(AtomicTable* pTable, AdaptScreenInfo* pAdaptInfo)
	:adaptScreenStrategy(pTable, pAdaptInfo)
{

}

adaptScreenRowFirstStrategy::~adaptScreenRowFirstStrategy()
{

}

void adaptScreenRowFirstStrategy::doAdaptScreen()
{
	double wView = m_pAdaptInfo->getViewW();//单位：字符数
	double hView = m_pAdaptInfo->getViewH();//单位：磅

	adaptRowHeight(m_pAdaptInfo->getTableH(), hView);
	//列宽优化
	if (wView / m_pAdaptInfo->getTableW() <= 2)
		adaptColWidth(m_pAdaptInfo->getTableW(), wView);
	else
		adaptColWidth(m_pAdaptInfo->getTableW(), wView / 2);
}

adaptScreenPrcessProxy::adaptScreenPrcessProxy(AtomicTable* pTable, const ViewSize& viewSz)
	: m_pAdaptInfo(nullptr)
{
	init(pTable, viewSz);
}

adaptScreenPrcessProxy::~adaptScreenPrcessProxy()
{
	if (m_pAdaptInfo)
	{
		delete m_pAdaptInfo;
		m_pAdaptInfo = nullptr;
	}
}

void adaptScreenPrcessProxy::doAdaptScreen()
{
	if (m_spStrategy)
	{
		m_spStrategy->doAdaptScreen();
		m_spStrategy->printfDebugInfo();
	}
}

void adaptScreenPrcessProxy::init(AtomicTable* pTable, const ViewSize& viewSz)
{
	double w = 0, h = 0, wAve = 0, hAve = 0;
	pTable->getTableSizeAndAve(w, h, wAve, hAve);
	if (!m_pAdaptInfo)
		m_pAdaptInfo = new AdaptScreenInfo();

	m_pAdaptInfo->setViewSize(viewSz.m_ViewWidth, viewSz.m_ViewHeight);
	m_pAdaptInfo->setTableW(w);
	m_pAdaptInfo->setTableH(h);
	m_pAdaptInfo->setTableWidthAve(wAve);
	m_pAdaptInfo->setTableHeightAve(hAve);

	qDebug() << "(adaptScreen-before)adaptTbl:" << "W=" << w << ",Wave" << wAve << "[char]";
	qDebug() << "(adaptScreen-before)adaptTbl:" << "H=" << h << ",Have" << hAve << "[pound]";
	qDebug() << "(adaptScreen-before)view:" << "W=" << viewSz.m_ViewWidth << "[char]"
		<< ",H=" << viewSz.m_ViewHeight << "[pound]";

	AdaptScreenType adaptType = m_pAdaptInfo->getAdaptScreenType();
	if (adaptType == AdaptScreenNormal)
		m_spStrategy = new adaptScreenNormalStrategy(pTable, m_pAdaptInfo);
	else if (adaptType == AdaptScreenColFirst)
		m_spStrategy = new adaptScreenColFirstStrategy(pTable, m_pAdaptInfo);
	else if (adaptType == AdaptScreenRowFirst)
		m_spStrategy = new adaptScreenRowFirstStrategy(pTable, m_pAdaptInfo);
}
