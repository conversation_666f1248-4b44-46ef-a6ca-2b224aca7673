﻿#pragma once

#include <src/tablestyleparser/kxtablestyleparserbase.h>
#include <src/kxetcommonheander.h>
#include <etcore/et_core_utility.h>
#include <kfc/datetime.h>
#include <include/io_utils.h>
#include <io/kso_table/xlsxrw/xlsxtransmap.h>

class KxtableDxfsParser : public KxlslParser
{
public:
	KxtableDxfsParser();

	std::vector<io_utils::DXF> getTableXf() const;
	virtual bool parserElement(const QDomElement& element) override;

	void setDxfCustomColor(const int themeColorKey, const QString& customColor);
private:
	bool collectDxf(const QDomElement& element);
	bool CollectDXfFont(const QDomElement& element, io_utils::DXF& xf);
	bool CollectDXfNumFmt(const QDomElement& element, io_utils::DXF& xf);
	bool CollectDXfFill(const QDomElement& element, io_utils::DXF& xf);
	bool collectPatternFill(const QDomElement& element, XFMASK* pMask, EtFill& f);
	bool collectGradientFill(const QDomElement& element, XFMASK* pMask, EtFill& f);
	bool CollectDXfBorder(const QDomElement& element, io_utils::DXF& xf);
	bool CollectDXfBorderLine(const QDomElement& element, BYTE& dg, EtColor& clr);
	bool CollectDXfAlignment(const QDomElement& element, io_utils::DXF& xf);
	bool CollectDXfProtection(const QDomElement& element, io_utils::DXF& xf);

	void initDXF(io_utils::DXF& xf);
	void InitFONT(FONT& font);
	bool fetchVal(const QDomElement& element, QDomAttr& outAttr);
	bool fetchColor(const QDomElement& element, EtColor& clr);
	bool fetchGradStop(const QDomElement& element, EtGradStop& gs, std::pair<bool, bool>& pr);

private:
	size_t m_iDXf;
	std::vector<io_utils::DXF> m_vecTableXf;
	ks_stdptr<IBook> m_ptrBook;
	KXlsxTransMaps m_transMap;
	int m_themeColorKey = 0;
	QString m_CustomColor;
};

struct TableStyleElementIO
{
	TableStyleElementIO(INT d, INT s, TABLE_STYLE_FORMAT t) : dxfId(d), size(s), tsf(t)
	{
		switch (t)
		{
		case TSF_FirstRowStripeXF:
		case TSF_SecondRowStripeXF:
		case TSF_FirstColStripeXF:
		case TSF_SecondColStripeXF:
			if (size < 0)
				size = 1;
			break;
		}
	}

	INT dxfId;
	INT size;
	TABLE_STYLE_FORMAT tsf;
};

class KxtableStyleParser : public KxlslParser
{
public:
	KxtableStyleParser();

	std::vector<TableStyleElementIO> getTableStyleElement() const;
	std::vector<io_utils::DXF> getTableXf() const;
	enum KAIETStyleType
	{
		TSF_KAIET_None = TSF_None,
		TSF_KAIET_BigTitle = TSF_TOTAL_COUNT + 100,
		TSF_KAIET_Info,
		TSF_KAIET_BigTitleEnd,
		TSF_KAIET_InfoEnd
	};
	const std::map<KAIETStyleType, INT>& getKaietStyleElement() const;

	DWORD getStyleOption() const;
	void setCustomColor(const int themeColorKey, const QString& customColor);
private:
	bool parserElement(const QDomElement& element) override;

	bool collectTableStyle(const QDomElement& element);
	bool CollectTableStyleElement(const QDomElement& element);
	bool collectTableStyleOption(const QDomElement& element);
private:
	std::vector<TableStyleElementIO> m_tblStyElems;
	KxtableDxfsParser m_dxfParser;
	std::map<KAIETStyleType, INT> m_kaietStyleElems;
	DWORD m_styleOption = 0;
	KXlsxTransMaps m_transMap;
};

bool newEtOnlineTableStyle(const QString& name, KxtableStyleParser& parser, UINT* outId);