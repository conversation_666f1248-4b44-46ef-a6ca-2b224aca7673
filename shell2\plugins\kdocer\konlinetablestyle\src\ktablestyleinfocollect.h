﻿#pragma once
// ------------------------------------------------------------
// Desc : 处理信息收集
// ------------------------------------------------------------

namespace KTableStyleInfoCollect
{
	struct KTableReportInfo
	{
		QString func;
		QString firstEntry;
		QString secondEntry;
		QString pageName;
		QString moduleName;
		QString elementName = "0";
		QString elementType = "0";
		int idx = -1;

		QHash<QString, QString> args;
	};

	void reportData(const QString& event, QHash<QString, QString>& args, bool bPostLoginState, bool bPostEventTime, bool bInsertTable);
	void postDispalyInfo(QHash<QString, QString>& args);
	void postStayInfo(QHash<QString, QString>& args, DWORD duration);
	void postLoadInfo(QHash<QString, QString>& args);
	void postMoreBtnClicked(QHash<QString, QString>& args);
	void postResourcePageDisplayInfo(QHash<QString, QString>& args, int resourceCnt);
	void postResourceDisplayInfo(QHash<QString, QString>& args);
	void postResourceHoveredInfo(QHash<QString, QString>& args);
	void psotResourceClicked(QHash<QString, QString>& args);

	void postKLMEvent(const QString& eventName, const KTableReportInfo& info, bool bPostLogin, bool bPostEventTime, bool bInsert);
	void postInsertDisplayEvent(const KTableReportInfo& info);
	void postInsertButtonClick(const KTableReportInfo& info);
	void postInsertButtonHover(const KTableReportInfo& info);
	void postInsertCustomColor(const KTableReportInfo& info, const QString& type, const QColor& color);
};