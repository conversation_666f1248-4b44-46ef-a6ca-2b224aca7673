﻿#include "stdafx.h"
#include "ktablebeautifypreviewhelper.h"
#include <kcomctl/kappidlesvr.h>
#include <kcomctl/kquickhelpbarwidget.h>
#include <kxshare/kxapplication.h>
#include <kxshare/kxquickhelpbarcontainer.h>
#include <ksolite/kcoreapplication.h>
#include "kdocertoolkit/kdocerutils.h"

KTableBeautifyPreviewHelper::KTableBeautifyPreviewHelper(QObject* parent)
	: QObject(parent)
	, m_oldState(ksoFalse)
{
	kxApp->installEventFilter(this);
}

KTableBeautifyPreviewHelper::~KTableBeautifyPreviewHelper()
{
}

KTableBeautifyPreviewHelper* KTableBeautifyPreviewHelper::instance()
{
	static KTableBeautifyPreviewHelper m_instance;
	return &m_instance;
}

bool KTableBeautifyPreviewHelper::isInPreview()
{
	return m_spTransTool != nullptr;
}

void KTableBeautifyPreviewHelper::beginPreview(QWidget* webView, const QString& desc)
{
	endPreview();

	m_pWebView = webView;
	m_commitDesc = desc;

	if (m_spTransTool)
	{
		m_spTransTool->Rollback();
		m_spTransTool.clear();
	}

	m_spTransTool = KDocerUtils::getActiveTransTool();

	if (m_spTransTool)
	{
		m_spTransTool->StartTrans();
	}

	blockQhBarUpdate();
}

void KTableBeautifyPreviewHelper::endPreview(bool isCommit /* = false*/)
{
	if (!KDocerUtils::getCoreApplication(this))
		return;

	if (!m_spTransTool)
		return;

	if (isCommit && !m_commitDesc.isEmpty())
	{
		m_spTransTool->CommitTrans(krt::utf16(m_commitDesc), ksoCommit, FALSE);
		m_oldState = ksoFalse;
	}
	else
	{
		m_spTransTool->Rollback();
	}

	m_pWebView = nullptr;
	m_spTransTool.clear();
	m_commitDesc.clear();

	unblockQhBarUpdate();
}

bool KTableBeautifyPreviewHelper::eventFilter(QObject* o, QEvent* e)
{
	if (!m_spTransTool)
		return false;

	if ((!m_pWebView || !m_pWebView->isVisible()) ||
		(e->type() == QEvent::KeyPress) ||
		(e->type() == QEvent::Leave && o == m_pWebView))
	{
		endPreview();
	}
	else if (e->type() == QEvent::MouseMove)
	{
		QMouseEvent* mouse = static_cast<QMouseEvent*>(e);
		QPoint pos = mouse->globalPos();
		QRect rc = m_pWebView->rect();
		QPoint topLeft = m_pWebView->mapToGlobal(rc.topLeft());
		rc.moveTopLeft(topLeft);
		if (!rc.contains(pos))
		{
			endPreview();
		}
	}
	return false;
}

void KTableBeautifyPreviewHelper::blockQhBarUpdate()
{
	KxMainWindow* pMainWin = kxApp->findRelativeMainWindowX(m_pWebView);
	if (pMainWin)
	{
		auto qhBars = pMainWin->findChildren<KxQuickHelpBarContainer*>();
		foreach(KxQuickHelpBarContainer* qhBar, qhBars)
		{
			if (!qhBar->isVisible())
				continue;

			kApp->removeEventFilter(qhBar);
			m_qhBars.push_back(qhBar);
		}
	}

	kxApp->idleSvr()->stop();
}

void KTableBeautifyPreviewHelper::unblockQhBarUpdate()
{
	foreach(KxQuickHelpBarContainer* qhBar, m_qhBars)
	{
		if (!qhBar)
			continue;

		kApp->installEventFilter(qhBar);
	}
	m_qhBars.clear();

	kxApp->idleSvr()->start();
}
