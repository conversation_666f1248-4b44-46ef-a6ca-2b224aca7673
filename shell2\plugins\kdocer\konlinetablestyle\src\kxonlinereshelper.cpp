﻿#include "stdafx.h"
#include <krt/dirs.h>

#include <json/json.h>
#include <kso/dircfginfo.h>
#include <kso/l10n/kso/drawing.h>
#include <kcomctl/krbtabwidget.h>
#include <kcomctl/krbsubtabbar.h>
#include "common/adapter/kxonlinereshelper.h"
#include <kcomctl/kcloudserviceproxy.h>
#include <kcomctl/kmemberuser.h>
#include "kxetcommonheander.h"
#include "resmgr/konlinetableresmgr.h"
#include <applogic/etapi_old.h>
using namespace etoldapi;
#include <uilogic/global.h>
#include "kxonlinereshelper.h"

#include "utilities/path/module/kcurrentmod.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include <ksolite/kconfigcentersettings.h>
#include "kcomctl/src/formatting/kcolorcombobox.h"
#include <QtSvg/QSvgRenderer>
#include <ksolite/kxjsonhelper.h>
#include <kdocertoolkit/kdocerutils.h>
#include <kdocertoolkitlite/account/kdoceraccount.h>
#include <ksolite/ksolog/kxloggerlite.h>
#include "kdocertoolkitlite/tiance/kdocertiance.h"
#include <public_header/kliteui/kskin/kliteskin.h>
#include <kcomctl/kappskin.h>
#include "ksolite/kdocercoreitef.h"

#define szFileProtocol				"file:///"
#define testLocalExtend				"testLocalExtend"
#define testLocalPocket				"testLocalPocket"
#define testLocalInline				"testLocalInline"
#define testLocalFeature			"testLocalFeature"
#define testLocalBeautify			"testLocalBeautify"
#define CACHE_DAYS 1

namespace KxOnlineTableResHelper
{
	const char* const TABLESTYLE_REGISTER = "HKEY_CURRENT_USER\\Software\\Kingsoft\\Office\\6.0\\Common";
}

namespace KxOnlineTableResHelper
{
	const char* s_resourceReletivePath[] =
	{
		"/index.html#/dropdown?upperEntrySource=menubar_tablestyle_page",		//KOT_OnlineTableStylePocket
		"/index.html?upperEntrySource=menubar_tablestyle_page",		//KOT_OnlineTableStyleExtend
		"/index.html#/floatwin?upperEntrySource=menubar_tablestyle_page",		//KOT_OnlineTableStyleInline
		"/index.html#/taskpanel/home",		//KOT_OnlineTableStyleFeature
		"/index.html#/taskpanel/home",		//KOT_WPSTableStyleFeature
		"/index.html#/v3/extend/beautify",		//KOT_OnlineTableStyleBeautify
		"/index.html#/dropdown?upperEntrySource=menubar_tablestyle_page",	//KOT_WPP_OnlineTableStylePocket
		"/index.html#/floatwin?upperEntrySource=menubar_tablestyle_page",	//KOT_WPP_OnlineTableStyleInline
	};

	const QString getlocalPath(const QString& suffix)
	{
		return KxOnlineResHelper::downloadPath() % suffix;
	}

	const QString nativeUrlPath(const QString& strUrl)
	{
		QString filePath = strUrl;
		int pos = filePath.indexOf("?");
		if (pos > 0)
			filePath.truncate(pos);

		return QFileInfo(filePath).baseName();
	}

	void installTranslator()
	{
		QString qmDirPath = QDir(docer::base::KCurrentModule::getModuleResourceDirPath()).filePath("mui/");
		QStringList lstQm;
		lstQm.append("konlinetablestyle.qm");
		if (kcoreApp && kcoreApp->translator())
			kcoreApp->translator()->installTranslation(qmDirPath, lstQm);
		QString moudlePath = docer::base::KCurrentModule::getModuleResourceDirPath();
		QResource::registerResource(moudlePath + "/mui/default/icons.data");
		KAppSkin* skin = kxApp->skin();
		if (skin)
		{
			IKResourceLoader* resLoader = skin->getResourceLoader();
			if (resLoader)
				resLoader->addIconSearchDir(TYPE_SVG, ":/icons");
	}
	}

	void uninstallTranslator()
	{
		QString qmDirPath = QDir(docer::base::KCurrentModule::getModuleResourceDirPath()).filePath("mui/");
		if (kcoreApp && kcoreApp->translator())
			kcoreApp->translator()->uninstallTranslation(qmDirPath);
		QString moudlePath = docer::base::KCurrentModule::getModuleResourceDirPath();
		QResource::unregisterResource(moudlePath + "/mui/default/icons.data");
		KAppSkin* skin = kxApp->skin();
		if (skin)
		{
			IKResourceLoader* resLoader = skin->getResourceLoader();
			if (resLoader && resLoader->hasIconSearchDir(TYPE_SVG, ":/icons"))
				resLoader->removeIconSearchDir(TYPE_SVG, ":/icons");
	}
	}

	QString getSavedPath(const QString& itemId)
	{
		return getlocalPath(itemId);
	}

	QString getFileSavePath(const QString& resId, const QString& md5)
	{
		return getSavedPath(resId) % QDir::separator() % md5 % QLatin1String(".tablestyle");
	}

	QString getOfficeDataPath()
	{
		return krt::dirs::officeData();
	}

	QString getSkinFilePath()
	{
		QString webResDir = docer::base::KCurrentModule::getModuleResourceDirPath() + "/res";
		return webResDir + "/skin.kuip";
	}

	QString getResourceURL(const QString& strRelativeURL)
	{
		QString resourceURL = strRelativeURL;
		if (!strRelativeURL.isEmpty() &&
			!strRelativeURL.startsWith(szFileProtocol) &&
			!strRelativeURL.startsWith("http://") &&
			!strRelativeURL.startsWith("https"))
		{
			QString webResDir;

			QString moduleName = KPluginName;
			QString resPrefixes = "/res";
			QString versionKey = "version";
			if (strRelativeURL.startsWith("/tablestylefeatureres"))
			{
				moduleName = "konlinetablestylefeature";
				versionKey = "featureVersion";
				resPrefixes = "";
			}
			else if (strRelativeURL.startsWith("/wpsfeatureres"))
			{
				moduleName = "kwpstablestylefeature";
				versionKey = "featureVersion";
				resPrefixes = "";
			}
			resourceURL = KDocerUtils::getDomainUrl("personallocal") % "/addons/" % docer::base::KCurrentModule::getCurrentModuleName() % resPrefixes % strRelativeURL;
			KDocerUtils::setPersonalLocalPluginResourcePath(docer::base::KCurrentModule::getCurrentModuleName());
		}
		return resourceURL;
	}

	bool isUseLocalResource()
	{
		static bool s_bTest = false;
		static bool s_init = false;
		if (!s_init)
		{
			s_init = true;
			KCCCommonSettings reg;
			QString strUrl = reg.value(testLocalPocket).toString();
			s_bTest = !strUrl.isEmpty();
		}
		return s_bTest;
	}

	QString getPaySource(const QString& entrance, const QString& detailEntrance)
	{
		if (detailEntrance == "intab_tabtool_btn" || detailEntrance == "intab_tabstyle_btn")
		{	
			return "toptoolshow";
		}
		else if (detailEntrance == "start_tabstyle_btn")
		{
			return "topstarttype";
		}
		else if (detailEntrance == "menubar_tablestyle_page_more_button")
		{
			if (entrance == "intab_tabtool_btn" || entrance == "intab_tabstyle_btn")
			{
				return "toptoolwork";
			}
			else if (entrance == "start_tabstyle_btn")
			{
				return "topstartwork";
			}
		}else if (detailEntrance == "taskbar_tablestyle_button")
		{
			return "taskbar";
		}
		else if (detailEntrance == "start_pivot_tbstyle_btn" || detailEntrance == "design_pivot_tbstyle")
		{
			return "pivot";
		}

		return "rkunll";
	}

	void etUndo(int setup)
	{
		ks_stdptr<_Application> pApp = kxApp->coreApplication();
		if (pApp == NULL)
			return;
		
		ks_stdptr<_Workbook> spWorkBook;
		pApp->get_ActiveWorkbook(&spWorkBook);
		if (spWorkBook == NULL)
			return;
		
		ks_stdptr<IKWorkbook> spCoreWorkBook;
		spCoreWorkBook = spWorkBook;
		if (!spCoreWorkBook)
			return;
		
		ks_stdptr<IKTransactionTool> spTransTool;
		spTransTool = spCoreWorkBook->GetTransactionTool();
		if (!spTransTool)
			return;
		
		spTransTool->Undo(nullptr, 1);
	}

	void undo(const QObject* mwObj, int setup)
	{
		if (KDocerUtils::isWpsApp(mwObj))
			wpsUndo(setup);
		else if (KDocerUtils::isEtApp(mwObj))
			etUndo(setup);
		else if (KDocerUtils::isWppApp(mwObj))
		{
			wppUndo(setup);
			qApp->postEvent(kxApp->findRelativeMainWindowX((QObject*)(mwObj)), new KIdleEvent(0, 0), Qt::LowEventPriority);
		}
	}

	QString getResourcePath(KxOnlineResType type)
	{
		if (isUseLocalResource())
		{
			QString strUrl;
			if (KOT_OnlineTableStylePocket == type
				|| KOT_WPP_OnlineTableStylePocket == type)
			{
				KCCCommonSettings reg;
				strUrl = reg.value(testLocalPocket).toString();
			}
			else if (KOT_OnlineTableStyleExtend == type)
			{
				KCCCommonSettings reg;
				strUrl = reg.value(testLocalExtend).toString();
			}
			else if (KOT_OnlineTableStyleInline == type
				|| KOT_WPP_OnlineTableStyleInline == type)
			{
				KCCCommonSettings reg;
				strUrl = reg.value(testLocalInline).toString();
			}
			else if (KOT_OnlineTableStyleFeature == type
				|| KOT_WPSFeature == type)
			{
				KCCCommonSettings reg;
				strUrl = reg.value(testLocalFeature).toString();
			}
			else if (KOT_OnlineTableStyleBeautify == type)
			{
				KCCCommonSettings reg;
				strUrl = reg.value(testLocalBeautify).toString();
			}
			if (!strUrl.isEmpty())
				return strUrl;
		}
		QString strRelativeURL = QLatin1String(s_resourceReletivePath[type]);
		return getResourceURL(strRelativeURL);
	}

	QString getThumbCachePath(const QString& url, const QString& resId)
	{
		return getSavedPath(resId) % QDir::separator() % QUrl(url).fileName();
	}

	CELL GetActiveCell(IKEtView* pEtView, BOOL bShadow)
	{
		KCOMPTR(IKWorksheet)		ptrWorksheetInfo;
		ks_stdptr<IKEtWindow>		ptrWndInfo;
		KCOMPTR(ISheetWndInfos)		ptrSheetWndInfos;
		KCOMPTR(IUnknown)			ptrUnk;
		KCOMPTR(IRangeInfo)			ptrRangeInfo;
		KCOMPTR(IKRanges)			ptrRanges;
		CELL						cell = {0};

		ptrWorksheetInfo = pEtView->GetActiveWorksheet();
		ptrWndInfo = pEtView->GetWindow();
		ptrSheetWndInfos = ptrWorksheetInfo->GetWndInfos();
		if (bShadow)
			ptrSheetWndInfos->GetActiveCellShadow(ptrWndInfo->GetIndex(), cell);
		else
			ptrSheetWndInfos->GetActiveCell(ptrWndInfo->GetIndex(), cell);

		return cell;
	}

	bool etHasActiveTable()
	{
		return isSelectTable() || isSelectPivotTable();
	}

	bool hasActiveTable(const QObject* mwObj)
	{
		if (KDocerUtils::isWpsApp(mwObj))
			return wpsHasActiveTable();
		else if (KDocerUtils::isEtApp(mwObj))
			return etHasActiveTable();
		else if (KDocerUtils::isWppApp(mwObj))
			return wppHasActiveTable();
		else
			return false;
	}

	bool getActiveTableInfo(TableInfo& tableInfo)
	{
		ks_stdptr<IKApplication> spCoreApp = kxApp->coreApplication();
		if (!spCoreApp)
			return false;

		ks_stdptr<_Application> spApp = spCoreApp;
		if (!spApp)
			return false;

		ks_stdptr<_Workbook> spWorkbook;
		spApp->get_ActiveWorkbook(&spWorkbook);
		if (!spWorkbook)
			return false;

		ks_stdptr<_Worksheet> spWorksheet;
		spWorkbook->get_ActiveSheet(&spWorksheet);
		if (!spWorksheet)
			return false;

		ks_stdptr<ListObjects> spLstObjs;
		spWorksheet->get_ListObjects(&spLstObjs);
		ks_stdptr<IKEtView> spEtView = spCoreApp->GetActiveView();
		if (NULL == spEtView)
			return false;

		ks_stdptr<IKWorksheet> spKWorksheet = spEtView->GetActiveWorksheet();
		ks_stdptr<ISheet> spSheet = spKWorksheet->GetSheet();

		CELL cellActive = spEtView->GetActiveCell();
		RANGE rgActive(spSheet->GetBMP());
		IDX idx = -1;
		spSheet->GetIndex(&idx);
		rgActive.SetCell(idx, cellActive.row, cellActive.col);
		ks_stdptr<ICoreListObject>  spCoreLstObj;
		ks_stdptr<ICoreListObjects> spCoreLstObjs;
		spSheet->GetExtDataItem(edSheetListObjects, (IUnknown**)&spCoreLstObjs);
		if(spCoreLstObjs == NULL)
			return false;

		HRESULT hr = spCoreLstObjs->FindFirstItem(rgActive, &spCoreLstObj);
		if (S_OK == hr)
		{
			tableInfo.id = spCoreLstObj->GetTableStyle();
			tableInfo.styleOption = spCoreLstObj->GetStyleOptions();
			return true;
		}
		return false;
	}

	void setWidgetFontSize(QWidget* widget, int fontSize, bool needBold /* = false*/)
	{
		if (!widget)
			return;

		QFont ft = widget->font();
		ft.setBold(needBold);
		ft.setPixelSize(fontSize);
		widget->setFont(ft);
	}

	void setWidgetPaletteColor(QWidget* widget, QPalette::ColorRole role, const QColor& color)
	{
		if (!widget)
			return;

		QPalette palette = widget->palette();
		palette.setColor(role, color);
		widget->setPalette(palette);
	}

	bool isSelectTable()
	{
		ks_stdptr<IKApplication> spCoreApp = kxApp->coreApplication();
		if (!spCoreApp)
			return false;

		ks_stdptr<_Application> spApp = spCoreApp;
		if (!spApp)
			return false;

		ks_stdptr<_Workbook> spWorkbook;
		spApp->get_ActiveWorkbook(&spWorkbook);
		if (!spWorkbook)
			return false;

		ks_stdptr<_Worksheet> spWorksheet;
		spWorkbook->get_ActiveSheet(&spWorksheet);
		if (!spWorksheet)
			return false;

		ks_stdptr<ListObjects> spLstObjs;
		spWorksheet->get_ListObjects(&spLstObjs);
		ks_stdptr<IKEtView> spEtView = spCoreApp->GetActiveView();
		if (NULL == spEtView)
			return false;
		CELL cellActive = GetActiveCell(spEtView, FALSE);

		RANGE rg(spWorksheet->GetSheet()->GetBMP());
		IDX idxSheet = -1;
		spWorksheet->GetSheet()->GetIndex(&idxSheet);
		rg.SetCell(idxSheet, cellActive.row, cellActive.col);

		ks_stdptr<ListObject> spActLst;
		return spLstObjs->FindFirstItem(rg, &spActLst);
	}

	bool isSelectPivotTable()
	{
		ks_stdptr<IKApplication> spCoreApp = kxApp->coreApplication();
		if (!spCoreApp)
			return false;

		ks_stdptr<IKEtView> spEtView = spCoreApp->GetActiveView();
		if (!spEtView)
			return false;

		ks_stdptr<IKWorksheet> spWorksheet = spEtView->GetActiveWorksheet();
		if (!spWorksheet)
			return false;

		ISheet* pSheet = spWorksheet->GetSheet();
		if (!pSheet)
			return false;

		CELL cxActive = spEtView->GetActiveCell();
		IDX idx = -1;
		pSheet->GetIndex(&idx);

		ks_stdptr<IBook> spBook;
		pSheet->GetBook(&spBook);
		if (!spBook)
			return false;

		IPropertyAreaMgr* pAreaMgr = spBook->GetPropertAreaMgr();
		if (!pAreaMgr)
			return false;

		HANDLE hd = pAreaMgr->GetItemByPos(idx, cxActive.row, cxActive.col);
		if (NULL == hd)
			return false;
		PropertyAreaArgs args = PropertyAreaArgs::createInv();
		if (FAILED(pAreaMgr->GetItemInfo(hd, NULL, &args)))
			return false;

		return args.ipa && args.ipa->CastPivotTable() != NULL;
	}

	bool getActiveDocDirty()
	{
		IKApplication* pApp = kxApp->coreApplication();
		IKDocument* pDoc = pApp ? pApp->GetActiveDocument() : nullptr;
		if (pDoc)
			return pDoc->IsDirtyDoc();
		return true;
	}

	void setActiveDocDirty(bool bDirty)
	{
		IKApplication* pApp = kxApp->coreApplication();
		IKDocument* pDoc = pApp ? pApp->GetActiveDocument() : nullptr;
		if (pDoc)
			pDoc->SetDirty(FALSE);
	}

	QString qColorToHex(const QColor& qColor)
	{
		QString hexRGB = QString("#%1%2%3")
			.arg(qColor.red(), 2, 16, QChar('0'))
			.arg(qColor.green(), 2, 16, QChar('0'))
			.arg(qColor.blue(), 2, 16, QChar('0'));

		return hexRGB.toUpper();
	}

	bool getReplaceColorList(const QVariantMap& themeColorMap, QVariantList& replaceList, QString& errMsg)
	{
		if (themeColorMap.isEmpty())
			return true;

		QString hexRGB = themeColorMap.value("replace_rgb").toString();
		if (hexRGB.isEmpty())
		{
			errMsg = "parameter of 'replace_rgb' is empty";
			return false;
		}

		QVariantList themeColorList = themeColorMap.value("rgb_list", QVariantList()).toList();
		if (themeColorList.isEmpty())
		{
			errMsg = "parameter of 'rgb_list' is empty";
			return false;
		}

		for (auto it = themeColorList.cbegin(); it != themeColorList.cend(); it++)
		{
			QVariantMap rgbMap = (*it).toMap();
			if (!rgbMap.contains("brightness"))
			{
				errMsg = "parameter of 'rgb_list-brightness' is empty";
				return false;
			}

			double brightness = rgbMap.value("brightness", 0.0).toDouble();
			double colorTransparency2Tint = (100 - rgbMap.value("transparency", 0).toInt()) / (double)100;
			QString subRGB = rgbMap.value("rgb").toString().toUpper();
			if (subRGB.isEmpty())
			{
				errMsg = "parameter of 'rgb_list-rgb' is empty";
				return false;
			}

			QColor qColor(hexRGB);
			KThemeColorItem themeColorItem(qColor);
			themeColorItem.setBrightness(brightness);
			themeColorItem.addTransform(drawing::Color::Tint, colorTransparency2Tint);
			qColor = themeColorItem.toQColor();

			replaceList.append(QVariantMap({ {"rgb", subRGB }, {"replace_rgb", qColorToHex(qColor)} }));
		}

		return true;
	}

	QVariantList getThemeColorList()
	{
		auto getThemeColorRgb = [](int index, const drawing::Color::SchemeColor& themeKey) {
			KThemeColorItem item(themeKey);
			QVariantMap result;
			result["index"] = index;
			result["rgb"] = KxOnlineTableResHelper::qColorToHex(item.toQColor());
			result["text"] = item.GetColorTooltip();
			return result;
		};

		QVariantList result;
		result.append(getThemeColorRgb(7, drawing::Color::Dark1));
		result.append(getThemeColorRgb(1, drawing::Color::Accent1));
		result.append(getThemeColorRgb(2, drawing::Color::Accent2));
		result.append(getThemeColorRgb(3, drawing::Color::Accent3));
		result.append(getThemeColorRgb(4, drawing::Color::Accent4));
		result.append(getThemeColorRgb(5, drawing::Color::Accent5));
		result.append(getThemeColorRgb(6, drawing::Color::Accent6));
		return result;
	}

	bool changeSVGColor(const QString& path, const QVariantMap& themeColorMap, QByteArray& resultSVG)
	{
		if (!QFile::exists(path))
			return false;

		QString errMsg;
		QVariantList replaceList;
		if (!getReplaceColorList(themeColorMap, replaceList, errMsg))
			return false;

		QByteArray bytes = KDocerUtils::readFile(path);
		if (bytes.isEmpty())
			return false;

		for (auto it = replaceList.cbegin(); it != replaceList.cend(); it++)
		{
			QVariantMap rgbMap = (*it).toMap();
			QString before = rgbMap.value("rgb").toString();
			QString after = rgbMap.value("replace_rgb").toString();
			bytes.replace(before.toUtf8().data(), after.toUtf8().data());
		}

		resultSVG = bytes;
		return true;
	}

	bool changeSVGColor(const QString& path, drawing::Color targetColor, const QVariantMap& themeColorMap, QByteArray& resultSVG)
	{
		if (!QFile::exists(path))
			return false;
		QVariantMap replaceMap = themeColorMap;
		if (targetColor.type() == drawing::Color::Preset)
			replaceMap["replace_rgb"] = "#000000";
		else
			replaceMap["replace_rgb"] = KxOnlineTableResHelper::qColorToHex(KThemeColorItem(targetColor.getScheme()).toQColor());
		return changeSVGColor(path, replaceMap, resultSVG);
	}

	bool getCacheResourceInfo(const QString& cacheKey, QList<KTableStyleResourceInfo>& outList)
	{
		KCCPluginsSettings reg;
		reg.beginGroup(docer::base::KCurrentModule::getCurrentModuleName());
		bool bSuccess = false;
		do 
		{
			QString cacheVersionChannel = reg.value(cacheKey % QLatin1String("_version_channel")).toString();
			if (cacheVersionChannel.isEmpty() || cacheVersionChannel != QString("%1_%2").arg(KDcInfoCDetail::getVersion()).arg(KDcInfoCDetail::getMC()))
				break;
			QString cacheData = reg.value(cacheKey % QLatin1String("_data")).toString();
			if (parseResourceInfo(JsonHelper::convertStringToQJson(cacheData).toVariantMap(), outList))
				bSuccess = true;
		} while (false);
		reg.endGroup();
		return bSuccess;
	}

	void writeCacheResourceInfo(const QString& cacheKey, const QJsonObject& data)
	{
		KCCPluginsSettings reg;
		reg.beginGroup(docer::base::KCurrentModule::getCurrentModuleName());
		reg.setValue(cacheKey % QLatin1String("_timstamp"), QDateTime::currentDateTime().addDays(1));
		reg.setValue(cacheKey % QLatin1String("_data"), JsonHelper::convertQJsonToString(data));
		reg.setValue(cacheKey % QLatin1String("_version_channel"), QString("%1_%2").arg(KDcInfoCDetail::getVersion()).arg(KDcInfoCDetail::getMC()));
	}

	bool parseResourceInfo(const QVariantMap& info, QList<KTableStyleResourceInfo>& outList)
	{
		QString mkKey = info["mk_key"].toString();
		QString policy = info["alg"].toString();
		for (const auto& item :info["items"].toList())
		{
			QVariantMap itemMap = item.toMap();
			KTableStyleResourceInfo resInfo;
			resInfo.mkKey = mkKey;
			resInfo.policy = policy;
			resInfo.id = itemMap["id"].toString();
			if(resInfo.id.isEmpty())
				continue;
			resInfo.uuid = itemMap["uuid"].toString();
			resInfo.name = itemMap["name"].toString();
			resInfo.md5 = itemMap["md5"].toString();
			resInfo.rid = itemMap["rid"].toString();
			resInfo.bFree = (itemMap["sale_mode"].toInt() & 1) ? true : false;
			resInfo.resourceKey = itemMap["resource_key"].toString();
			QVariantMap tableSchemeData = itemMap["tablestyle_scheme_data"].toMap();
			if (tableSchemeData.contains("theme_color_list"))
				resInfo.colorInfo = tableSchemeData["theme_color_list"].toMap();
			QVariantList privileges = itemMap["privilege_name"].toList();
			for (const auto& pri : privileges)
			{
				if(pri.toString().isEmpty())
					continue;
				resInfo.privileges.append(pri.toString());
			}
			resInfo.previewUrlMap[ThumbType::Small] = itemMap["thumb_small_url"].toString();
			resInfo.previewUrlMap[ThumbType::Medium] = itemMap["thumb_medium_url"].toString();
			resInfo.previewUrlMap[ThumbType::Big] = itemMap["thumb_big_url"].toString();
			outList.append(resInfo);
		}
		return !outList.isEmpty();
	}

	void requestPublishResource(const QString& position, std::function<void(const QList<KTableStyleResourceInfo>&)> onSuccess, std::function<void(const DownloadFailInfo&)> onError)
	{
		TableItemRquestInfo requestInfo;
		requestInfo.position = position;
		requestPublishResource(requestInfo, onSuccess, onError);
	}

	void requestPublishResource(const TableItemRquestInfo& requestInfo, std::function<void(const QList<KTableStyleResourceInfo>&)> onSuccess, std::function<void(const DownloadFailInfo&)> onError)
	{
		auto checkInTimeStamp = [](const QString& cacheKey) {
			KCCPluginsSettings reg;
			reg.beginGroup(docer::base::KCurrentModule::getCurrentModuleName());
			bool bSuccess = false;
			do
			{
				QString cacheVersionChannel = reg.value(cacheKey % QLatin1String("_version_channel")).toString();
				if (cacheVersionChannel.isEmpty() || cacheVersionChannel != QString("%1_%2").arg(KDcInfoCDetail::getVersion()).arg(KDcInfoCDetail::getMC()))
					break;
				QDateTime cacheTimeStamp = reg.value(cacheKey % QLatin1String("_timstamp"), QDateTime()).toDateTime();
				if (cacheTimeStamp.isNull() || cacheTimeStamp <= QDateTime::currentDateTime())
					break;
				bSuccess = true;
			} while (false);
			reg.endGroup();
			return bSuccess;
		};
		QString pluginName = docer::base::KCurrentModule::getCurrentModuleName();
		QString cacheKey = pluginName % QLatin1String("_") % requestInfo.position % QLatin1String("_") % QString::number(requestInfo.offset) % QLatin1String("_") % QString::number(requestInfo.limit);
		QList<KTableStyleResourceInfo> resList = KOnlineTableResManager::getInstance().getResourceInfo(cacheKey);
		bool bOnlyRecord = false;
		if (!resList.isEmpty() || getCacheResourceInfo(cacheKey, resList))
		{
			KOnlineTableResManager::getInstance().setResourceInfo(cacheKey, resList);
			QTimer::singleShot(0, [=]() { onSuccess(resList); });
			bOnlyRecord = true;
		}

		if (!resList.isEmpty() && checkInTimeStamp(cacheKey))
			return;

		QVariantMap reqData;
		reqData["hdid"] = KDcInfoCDetail::getHdid();
		reqData["position"] = requestInfo.position;
		reqData["offset"] = requestInfo.offset;
		reqData["limit"] = requestInfo.limit;
		reqData["platform"] = TianCe::getFilterPlatform();
		reqData["version"] = KDcInfoCDetail::getVersion();
		reqData["mb_types"] = QVariantList() << 0;
		reqData["filter_info"] = QVariantMap{
			{"channel", KDcInfoCDetail::getMC()}
		};
		QVariantMap header;
		header["Content-Type"] = "application/json";
		header["Cookie"] = KDocerAccount::getCookie();
		QVariantMap args;
		args["data"] = JsonHelper::variantMapSerialize(reqData);
		args["header"] = header;

		QUrl url = QUrl(KDocerUtils::getDomainUrl("docerapiwps") % QLatin1String("/deliver/meta/v1/rec"));
		QUrlQuery urlQuery(url);
		urlQuery.addQueryItem("rmsp", KDocerUtils::getRequestRmsp(docer::base::KCurrentModule::getCurrentModuleName()));
		url.setQuery(urlQuery);

		docer::request(url, args, DRM_POST, [=](const QByteArray& data) {
			QJsonObject responseObj = JsonHelper::convertByteArrayToQJson(data);
			if (responseObj["result"].toString() != "ok")
			{
				KxLoggerLite::writeWarning(docer::base::KCurrentModule::getCurrentModuleName().toStdWString(),
					QString("KxOnlineTableResHelper::requestPublishResource result error cacheKey:%1 data:%2")
					.arg(cacheKey)
					.arg(QString::fromUtf8(data)).toStdWString());
				if(!bOnlyRecord)
					onError(DownloadFailInfo());
				return;
			}
			QJsonObject responseDataObj = responseObj["data"].toObject();
			if (responseDataObj.isEmpty())
			{
				KxLoggerLite::writeWarning(docer::base::KCurrentModule::getCurrentModuleName().toStdWString(),
					QString("KxOnlineTableResHelper::requestPublishResource emptydata cacheKey:%1 data:%2")
					.arg(cacheKey)
					.arg(QString::fromUtf8(data)).toStdWString());
				if (!bOnlyRecord)
					onError(DownloadFailInfo());
				return;
			}
			QList<KTableStyleResourceInfo> resList;
			if (parseResourceInfo(responseDataObj.toVariantMap(), resList))
				writeCacheResourceInfo(cacheKey, responseDataObj);
			if (KOnlineTableResManager::getInstance().getResourceInfo(cacheKey).isEmpty())
				KOnlineTableResManager::getInstance().setResourceInfo(cacheKey, resList);
			if (!bOnlyRecord)
				onSuccess(resList);
		}, [=](DownloadFailInfo info) {
			KxLoggerLite::writeWarning(docer::base::KCurrentModule::getCurrentModuleName().toStdWString(),
				QString("KxOnlineTableResHelper::requestPublishResource request error cacheKey:%1 %2")
				.arg(cacheKey)
				.arg(info.toString()).toStdWString());
			if(!bOnlyRecord)
				onError(info);
		});
	}

	void openDocerUnifyPayDialog(const TableDownloadReportInfo& payInfo, const QString& payKey)
	{
		QUrl url(KDocerUtils::getDomainUrl("vip") % QLatin1String("/vcl_svr/static/docerpay"));
		QUrlQuery urlQuery(url);
		urlQuery.addQueryItem("position", payInfo.channel);
		urlQuery.addQueryItem("csource", payInfo.subChannel);
		urlQuery.addQueryItem("component", payInfo.component);
		urlQuery.addQueryItem("collect_key", payKey);
		QVariantMap bizContent;
		bizContent["mk_key"] = payInfo.mkKey;
		urlQuery.addQueryItem("biz_content", JsonHelper::variantMapSerialize(bizContent).toUtf8().toBase64());
		url.setQuery(urlQuery);

		QString moduleName = "docer_tablestyle";
		if (auto docerCoreLite = docer::KLiteDocerCoreMgr::GetDocerCoreLite())
		{
			docerCoreLite->showDocerPayDlg(url.toString(),
				moduleName, kxApp->currentMainWindow(), nullptr, QVariantMap());
		}
		else
		{
			KxLoggerLite::writeWarning(QString(__FUNCTION__).toStdWString(), L"docercorelite is nullptr");
		}
	}

	bool checkLogin(const QString& loginSrc)
{
		if (!KDocerAccount::isLogined())
		{
			QVariantMap loginInfo;
			loginInfo["from"] = "docertable";
			loginInfo["qrcode"] = "docer";
			loginInfo["loginSrc"] = loginSrc;
			KDocerAccount::login(loginInfo);
			return false;
		}
		return true;
	}

	QString getSecondEntry(const QString& cmdName)
	{
		QString secondEntry = "restab_start_table";
		if (cmdName == "TableStyleGallery")
			secondEntry = "restab_tabletool_table";
		else if (cmdName == "PivotTableStyleGallery")
			secondEntry = "restab_design_table";
		return secondEntry;
	}
};
