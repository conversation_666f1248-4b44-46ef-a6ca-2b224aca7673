﻿#include <stdafx.h>
#include "kinsertonlinetablecmd.h"
#include "kinsertonlinetablewidget.h"
#include "kinsertonlinetablemodel.h"
#include <kdocertoolkit/kdocerutils.h>
#include <src/custom/kxdocertablestylegalleryitem.h>
#include <kcomctl/kcommandfactory.h>
#include <src/ktablestyleinfocollect.h>
#include <kdocerfunctional.h>
#include <kcomctl/kpopupcefwidget.h>

#include "ksolite/kdocer/kdocercoreitef.h"
#include <src/kxonlinereshelper.h>
#include "kdocercorehelper.h"

KInsertOnlineTableStyleCommand::KInsertOnlineTableStyleCommand(KxMainWindow* host, QObject* parent)
	:KxGalleryCommand(host, parent)
{
	KxOnlineTableResHelper::installTranslator();
	auto docerCoreImpl = getIKDocerCore();
	if (docerCoreImpl)
		docerCoreImpl->applyFunc("registerExtendResource", QVariantMap());
	m_initThemeColorPaneColor = drawing::Color();
}

KInsertOnlineTableStyleCommand::~KInsertOnlineTableStyleCommand()
{
	KxOnlineTableResHelper::uninstallTranslator();
}

QWidget* KInsertOnlineTableStyleCommand::createExtendedWidget(QWidget* parent)
{
	auto mw = kxApp->findRelativeMainWindowX(this);
	if (!mw || !mw->commands())
		return nullptr;

	if(!m_menuButton)
	{
		KCefPopupMenuButton* menuButton = KTik::findParentByType<KCefPopupMenuButton>(parent);
		if (menuButton)
			m_menuButton = menuButton;
	}

	if (!m_listCommand)
	{
		m_listCommand = new KxListCommand(mw, mw->commands());
		if (auto prototypeCmd = KDocerUtils::findCommand(mw, "InsertContentTable"))
			m_listCommand->addSubCommand(prototypeCmd);
	}

	if(!m_applyStyleCmd)
	{
#ifdef WPP_PROJECT
		m_applyStyleCmd = new KxWppTableStyleFeatureCommand(mw, mw->commands());
#else
		m_applyStyleCmd = new KxTableStyleFeatureCommand(mw, mw->commands());
#endif // WPP_PROJECT
		connect(m_applyStyleCmd, &KxTableStyleFeatureCommand::sendInsertInfo, this, &KInsertOnlineTableStyleCommand::onInsertFinished);
	}

	if (!m_dataSource)
	{
		m_dataSource = new KxInsertTableModel(KDocerUtils::getCoreApplication(this), this, this, m_applyStyleCmd);
		connect(m_dataSource, SIGNAL(sigDownloadError(const KOTSResourceInfo&)), this, SLOT(onDownloadError(const KOTSResourceInfo&)));
	}

	KxInsertTableStyleContainer* widget = new KxInsertTableStyleContainer(parent, this, m_listCommand);
	connect(widget, &KxInsertTableStyleContainer::sigMoreColorClicked, this, &KInsertOnlineTableStyleCommand::onMoreColorClicked);
	connect(widget, &KxInsertTableStyleContainer::sigSnapColorClicked, this, &KInsertOnlineTableStyleCommand::onSnapColorClicked);
	return widget;
}

KCommand* KInsertOnlineTableStyleCommand::clone(QObject* host, QObject* parent)
{
	KxMainWindow* pMainWin = qobject_cast<KxMainWindow*>(host);
	if (!pMainWin)
		return nullptr;

	KInsertOnlineTableStyleCommand* pCmd =
		new KInsertOnlineTableStyleCommand(pMainWin, parent);
	copyProperties(pCmd);
	return pCmd;
}

HRESULT KInsertOnlineTableStyleCommand::Get(KSO_Group, KSO_DataID id, void*, void* val)
{
	int32* va = (int32*)val;
	*va = MAKEINT32(5, 5);
	return S_OK;
}

OperatorErrorCode KInsertOnlineTableStyleCommand::InsertTable()
{
	if (IActionTarget* actionTarget = getActionTarget())
	{
		HRESULT hr = actionTarget->Exec(m_ksocmd->group, m_ksocmd->cmdID, OLECMDEXECOPT_DODEFAULT, this);

		if (E_ABORT == hr)
		{
			return InsertOpCansel;
		}
		else
		{
			return SUCCEEDED(hr) ? Success : InsertOpError;
		}
	}
	return InsertOpError;
}

void KInsertOnlineTableStyleCommand::onPreviewBegin(const drawing::Color& color)
{
	emit colorChange(color);
}

void KInsertOnlineTableStyleCommand::onPreviewEnd()
{
	emit colorChange(m_initThemeColorPaneColor);
}

void KInsertOnlineTableStyleCommand::onMoreColorClicked()
{
	if (KPopupCefWidget::hasPopupWidget())
	{
		KPopupCefWidget::closeActivePopupWidget();
	}

#ifdef _FIX_OHOS_TODO	
	//鸿蒙下popupWidget为KPopupWidgetEx类型。无法使用KPopupCefWidget::hasPopupWidget()
	//当前存在两个activePopupWidget：颜色面板、下拉窗，故调用两遍
	if (qApp->activePopupWidget())
		qApp->activePopupWidget()->close();
	if (qApp->activePopupWidget())
		qApp->activePopupWidget()->close();
#endif

	QTimer::singleShot(0, bindContext(this, [=]() {
		QColor newColor;
		newColor = kxApp->getColor(newColor, "");
		if (newColor.isValid())
		{
			drawing::Color clr = drawing::Color::fromRgb(newColor.rgb());
			onColorChange(clr);
			addRecentColor(newColor);
			KTableStyleInfoCollect::postInsertCustomColor(getReportInfo(true), QLatin1String("colorselector"), newColor);
		}
		if (m_menuButton)
			emit m_menuButton->popup();
	}));
}

void KInsertOnlineTableStyleCommand::onSnapColorClicked()
{
	if (KPopupCefWidget::hasPopupWidget())
	{
		KPopupCefWidget::closeActivePopupWidget();
	}

#ifdef _FIX_OHOS_TODO	
	//鸿蒙下popupWidget为KPopupWidgetEx类型。无法使用KPopupCefWidget::hasPopupWidget()
	//当前存在两个activePopupWidget：颜色面板、下拉窗，故调用两遍
	if (qApp->activePopupWidget())
		qApp->activePopupWidget()->close();
	if (qApp->activePopupWidget())
		qApp->activePopupWidget()->close();
#endif

	QTimer::singleShot(0, bindContext(this, [=]() {
		QColor snapColor = kxApp->getSnapperColor();
		if (snapColor.isValid())
		{
			drawing::Color clr = drawing::Color::fromRgb(snapColor.rgb());
			onColorChange(clr);
			addRecentColor(snapColor);
			KTableStyleInfoCollect::postInsertCustomColor(getReportInfo(true), QLatin1String("absorbcolor"), snapColor);
		}
		if (m_menuButton)
			emit m_menuButton->popup();
	}));
}

void KInsertOnlineTableStyleCommand::onDownloadError(const KOTSResourceInfo& resourceInfo)
{
	onInsertFinished(resourceInfo, ReportInfo(), OperatorErrorCode::DownloadError, QString());
}

void KInsertOnlineTableStyleCommand::onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc)
{
	emit sigInsertFinished(resourceInfo, reportInfo, errorCode, errorDesc);
}

void KInsertOnlineTableStyleCommand::addRecentColor(const QColor& color)
{
	KxMainWindow* mw = kxApp->findRelativeMainWindowX(this);
	if (!mw)
		return;
	IAPIFormatGetter* pAPIFmtGetter = mw->getFormatGetter();
	if (!pAPIFmtGetter)
		return;
	ks_stdptr<IExtraColors> pExtraColor;
	HRESULT hr = pAPIFmtGetter->getExtraColors(&pExtraColor);
	if (FAILED(hr) || !pExtraColor)
		return;
	pExtraColor->add(color);
}

KTableStyleInfoCollect::KTableReportInfo KInsertOnlineTableStyleCommand::getReportInfo(bool bCustomColor)
{
	KTableStyleInfoCollect::KTableReportInfo reportInfo;
	reportInfo.func = QLatin1String("docer_inserttable");
	reportInfo.firstEntry = QLatin1String("insert_table");
	reportInfo.pageName = QLatin1String("inserttable_dropdown_page");
	reportInfo.moduleName = QLatin1String("choose_color");
	reportInfo.elementName = QLatin1String("costumize_color");
	reportInfo.elementType = QLatin1String("button");
	return reportInfo;
}

void KInsertOnlineTableStyleCommand::onColorChange(const drawing::Color& color)
{
	m_initThemeColorPaneColor = color;
	emit colorChange(color);
}

///////////////////////////////////////////////////////////////////
KInsertWppOnlineTableStyleCommand::KInsertWppOnlineTableStyleCommand(KxMainWindow* host, QObject* parent)
	:KInsertOnlineTableStyleCommand(host, parent)
{
}

#ifdef WPP_PROJECT
DECLARE_COMMAND_FACTORY(KInsertWppOnlineTableStyleCommand, KxMainWindow)
#else
DECLARE_COMMAND_FACTORY(KInsertOnlineTableStyleCommand, KxMainWindow)
#endif // WPP_PROJECT