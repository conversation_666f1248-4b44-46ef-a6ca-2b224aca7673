﻿#pragma once

#include <src/custom/kxdocertablestylegalleryitem.h>

namespace KTableStyleInfoCollect {
	struct KTableReportInfo;
}

class KxInsertScrollView : public KScrollGalleryView
{
	Q_OBJECT
public:
	KxInsertScrollView(KGalleryAbstractModel* model, QWidget* parent);
	~KxInsertScrollView();

protected:
	void customEvent(QEvent* event);

protected slots:
	void onInsertItem(const int count);
	void onRetryButtonHoverd(bool bHoverd);

private:
	KGalleryAbstractModel* m_model = nullptr;
};

class KxInsertTableItem : public KxDocerTableStyleGalleryItem
{
	Q_OBJECT
public:
	KxInsertTableItem(KGalleryAbstractModel* model,
		KTableStyleResourceInfo resourceInfo,
		const drawing::Color& color,
		KCommand* tableStyleCmd,
		int index);

	virtual QSize sizeHint() const override;

	virtual void reportDisplayInfo() override;
	virtual void prepareDrawItem(QPainter& dc, const QRect& rc, KGalleryView::ViewType type) override;
	virtual void setHovered(bool bHoverrd) override;

signals:
	void sigClosePopupWidget();

public slots:
	virtual void changeIconColor(const drawing::Color& color) override;
	void onColorChange(const drawing::Color& color);
	virtual void onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc) override;
protected:
	virtual TableDownloadReportInfo getTableDownloadInfo();
	KTableStyleInfoCollect::KTableReportInfo getTableReportInfo();

	virtual void click() override;
protected:
	int m_index = 0;
	bool m_bColorChanged = false;;
	bool m_bColorful = false;
};

class KxInsertFootItem : public KGalleryModelAbstractItem
{
	Q_OBJECT
public:
	explicit KxInsertFootItem(KGalleryAbstractModel* model);
	~KxInsertFootItem();

	virtual QSize sizeHint() const;
	virtual bool hitTestSubIcon(const QPoint& itemPos, const QPoint& mousePos) override;
	virtual void setHoveredOnSubIcon(const bool bStatus) override;
	virtual bool hoveredOnSubIcon() const override;

	virtual bool isClickable() const;
	virtual void click();

	void setLoading(bool bLoading);
signals:
	void sigLoadNextPage();
	void sigRetryButtonHoverd(bool bHoverd);

protected:
	virtual void drawItem(QPainter& dc, const QRect& rc, KGalleryView::ViewType type) const override;
	virtual void prepareDrawItem(QPainter& dc, const QRect& rc, KGalleryView::ViewType type) override;

	void drawLoading(QPainter& dc, const QRect& rc) const;
	void drawLoadingRolling(QPainter& dc, const QRect& rc) const;
	void drawError(QPainter& dc, const QRect& rc) const;
	void getErrorAreaRect(const QRect& rc, const QFont& font, QRect& hintRect, QRect& btnRect) const;

	void startLoading();
	void stopLoading();
	void timerEvent(QTimerEvent* event) override;

protected:
	bool m_bLoading = true;
	bool m_bSenddedLoad = false;
	bool m_bRetryButtonHoverd = false;
	int m_loadingTimerId = -1;
	int m_loadingRotation = 0;
};

class KxInsertTableModel : public KGalleryAbstractModel
{
	Q_OBJECT
public:
	enum State { Loading = 0, GalleryItem, NoResource, NoInternet };
	KxInsertTableModel(IKApplication* coreApp, QObject* parent, KGalleryCommand* galleryCmd, KCommand* applyCmd);
	~KxInsertTableModel();

	virtual bool prepareItems() override;
	KxInsertTableModel::State getStatus();
protected:
	void setState(State state);
	void appendItems(const QList<KTableStyleResourceInfo>& resourceList);
public slots:
	void loadNextPage();
signals:
	void stateChanged(int state);
	void sigDownloadError(const KOTSResourceInfo& resourceInfo);
	void sigRetryButtonHoverd(bool bHoverd);
	void sigClosePopupWidget();
private:
	int m_state = State::Loading;
	bool m_bRequesting = false;
	bool m_bResComplete = false;
	QPointer<KGalleryCommand> m_galleryCmd;
	QPointer<KCommand> m_applyStyleCmd;
	QPointer<KxInsertFootItem> m_footItem;
};