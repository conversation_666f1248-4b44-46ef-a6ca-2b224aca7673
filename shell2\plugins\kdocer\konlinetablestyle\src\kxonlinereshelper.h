﻿#pragma once

#include "stdafx.h"
#include "kxonlinetablestyledefine.h"
#include <kxshare/kxcommonwebapi.h>
#include <common/request/network/knetworkrequest.h>

enum KOnlineTableStylePreviewType
{
	KOnlineTableStyleTpPreview = 0,	//任务栏
	KOnlineTableStyleMenuPreview,	//下拉菜单栏
	KOnlineTableStyleQuickHelpPreview	//快速帮助栏
};

namespace KxOnlineTableResHelper
{
	extern const char* const TABLESTYLE_REGISTER;
	
	enum KxOnlineResType
	{
		KOT_OnlineTableStylePocket,										// 下拉菜单
		KOT_OnlineTableStyleExtend,										// 右侧侧边栏
		KOT_OnlineTableStyleInline,										// 快速帮助栏
		KOT_OnlineTableStyleFeature,									// 智能特性
		KOT_WPSFeature,													// 稻壳智能特性-文字组件
		KOT_OnlineTableStyleBeautify,									// 智能特性
		KOT_WPP_OnlineTableStylePocket,									// wpp下拉菜单
		KOT_WPP_OnlineTableStyleInline									// wpp快速帮助栏
	};
	void installTranslator();
	void uninstallTranslator();

	QString getSavedPath(const QString& itemId);
	QString getFileSavePath(const QString& resId, const QString& md5);
	QString getSkinFilePath();
	QString getOfficeDataPath();
	QString getResourceURL(const QString& strRelativeURL);
	QString getResourcePath(KxOnlineResType type);
	QString getThumbCachePath(const QString& url, const QString& resId);

	bool etHasActiveTable();
	bool wpsHasActiveTable();
	bool wppHasActiveTable();
	bool hasActiveTable(const QObject* mwObj);
	bool getActiveTableInfo(TableInfo& tableInfo);
	bool isUseLocalResource();
	QString getPaySource(const QString& entrance, const QString& detailEntrance);
	void etUndo(int setup);
	void wpsUndo(int setup);
	void wppUndo(int setup);
	void undo(const QObject* mwObj, int setup);

	void wppActiveNormalView(QObject* obj);
	KxView* wppGetView(int paneID, QObject* obj);

	void setWidgetFontSize(QWidget* widget, int fontSize, bool needBold = false);
	void setWidgetPaletteColor(QWidget* widget, QPalette::ColorRole role, const QColor& color);

	bool isSelectTable();
	bool isSelectPivotTable();

	bool getActiveDocDirty();
	void setActiveDocDirty(bool bDirty);
	bool slideHasTable();
	long getCurSelectSlideId();

	//svg
	QString qColorToHex(const QColor& qColor);
	bool getReplaceColorList(const QVariantMap& themeColorMap, QVariantList& replaceList, QString& errMsg);
	QVariantList getThemeColorList();
	bool changeSVGColor(const QString& path, const QVariantMap& themeColorMap, QByteArray& resultSVG);
	bool changeSVGColor(const QString& path, drawing::Color targetColor, const QVariantMap& themeColorMap, QByteArray& resultSVG);

	bool getCacheResourceInfo(const QString& cacheKey, QList<KTableStyleResourceInfo>& outList);
	void writeCacheResourceInfo(const QString& cacheKey, const QJsonObject& data);
	bool parseResourceInfo(const QVariantMap& info, QList<KTableStyleResourceInfo>& outList);
	void requestPublishResource(const QString& position,
		std::function<void(const QList<KTableStyleResourceInfo>&)> onSuccess, std::function<void(const DownloadFailInfo&)> onError);
	void requestPublishResource(const TableItemRquestInfo& requestInfo,
		std::function<void(const QList<KTableStyleResourceInfo>&)> onSuccess, std::function<void(const DownloadFailInfo&)> onError);

	void openDocerUnifyPayDialog(const TableDownloadReportInfo& payInfo, const QString& payKey);
	bool checkLogin(const QString& loginSrc);

	QString getSecondEntry(const QString& cmdName);
};