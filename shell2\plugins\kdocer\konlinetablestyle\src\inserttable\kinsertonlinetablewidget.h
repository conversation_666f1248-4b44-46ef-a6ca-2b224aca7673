#pragma once

#include <kcomctl/kgallerycommand.h>
#include <kxshare/kxcommands.h>
#include <kcomctl/kgallerymodel.h>
#include <src/kxonlinetablestyledefine.h>
#include <kxshare/formatting.h>

namespace KTableStyleInfoCollect {
	struct KTableReportInfo;
}

class KLiteToolTip;

class KOnlineTableThemeColorButton : public QAbstractButton
{
	Q_OBJECT
public:
	KOnlineTableThemeColorButton(const drawing::Color& color, const QString& text, QWidget* parent = nullptr);
	void setSelectedState(bool isSelected);
	void setHoverState(bool state);
	void setIsCustom(bool bCustom);
	bool isCustom();
	const drawing::Color& getColor();
	void setColor(const drawing::Color& clr);

protected:
	void paintEvent(QPaintEvent* event) override;
	void enterEvent(QEvent* event) override;
	void leaveEvent(QEvent* event) override;

	QSize sizeHint() const override;

public Q_SLOTS:
	void onClick();

signals:
	void colorChange(const drawing::Color& color);
	void previewBegin(const drawing::Color& color);
	void previewEnd();
	void colorBtnClicked(const drawing::Color& color);

private:
	QColor getColorFromThemeColor();

private:
	drawing::Color m_themeColor;
	QIcon m_selectedIcon;
	bool m_isSelected = false;
	bool m_isHovered = false;
	bool m_bCustom = false;
};

class KOnlineTableThemeColorPane : public QWidget
{
	Q_OBJECT
public:
	explicit KOnlineTableThemeColorPane(QWidget* parent);
	~KOnlineTableThemeColorPane();
	void init(const drawing::Color& color);

protected:
	void leaveEvent(QEvent* event) override;
	void showEvent(QShowEvent* event) override;
	bool eventFilter(QObject* watched, QEvent* event) override;

signals:
	void colorChange(const drawing::Color& color);
	void previewBegin(const drawing::Color& color);
	void previewEnd();
	void themeColorPaneStatus(bool);
	void colorBtnClicked(const drawing::Color& color);
	void sigCustomColorPopup();
	void sigCustomColorHide();

	void sigMoreColorClicked();
	void sigSnapColorClicked();

public slots:
	void onColorChange(const drawing::Color& color);
	void onPreViewBegin(const drawing::Color& color);
	void onPreviewEnd();
	void onPreviewEndTimeOut();
	void onPreviewBeginTimeOut();
	void onColorButtonClicked(const drawing::Color& color);

	void onCustomBtnClick(const drawing::Color& color);
	void onCustomPreviewBegin(const drawing::Color& color);
	void onCustomPreviewEnd();
	void onCustomPreviewBeginTimeOut();

	void onDelayClosePopupWidget();

	void onCustomItemClick(int idx);
protected:
	void updateSelectIndex(const drawing::Color& color);
	void updateHoverState(bool bCustom);

	void clearShortCut();
	void resetShortCut();

	void popupCustomWidget();

	KTableStyleInfoCollect::KTableReportInfo getReportInfo(bool bCustomColor);

private:
	QVector<KOnlineTableThemeColorButton*> m_buttons;
	QPointer<KOnlineTableThemeColorButton> m_customButton;
	QPointer<KPopupWidget> m_popupWidget;
	QPointer<KScrollGalleryView> m_customView;
	QPointer<KGalleryAbstractModel> m_customModel;
	QList<QPointer<QShortcut>> m_shortcuts;
	int m_selectButtonIndex = 1;
	QTimer* m_previewEndTimer = nullptr;
	QTimer* m_previewBeginTimer = nullptr;
	QTimer* m_customPreviewBeginTimer = nullptr;
	QTimer* m_delayClosePopupTimer = nullptr;
	drawing::Color m_hoverColor = drawing::Color::PhColor;
	drawing::Color m_clickColor = drawing::Color::PhColor;
};

class KxInsertTableStyleContainer : public QWidget {
	Q_OBJECT
public:
	KxInsertTableStyleContainer(QWidget* parent, KGalleryCommand* styleCmd, KxListCommand* listCmd);
	~KxInsertTableStyleContainer();

	virtual QSize sizeHint() const override;

signals:
	void sigMoreColorClicked();
	void sigSnapColorClicked();

protected:
	void showEvent(QShowEvent* event);
	void hideEvent(QHideEvent* event);
	void keyPressEvent(QKeyEvent* event);
	void closePopupWidget();

	QWidget* createGalleryWidget();
	QWidget* createLoadingWidget(QWidget* parent);
	QWidget* createErrorWidget(QWidget* parent, const QString& text);

	QWidget* createThemeColorPane(QWidget* parentWidget);
	KTableStyleInfoCollect::KTableReportInfo getReportInfo();
protected slots:
	void onRetryClicked();
	void onStateChanged(int status);
	void onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);
	void onClosePopupWidget();
private:
	void init();

private:
	QPointer<KGalleryCommand> m_styleCmd;
	QPointer<KxListCommand> m_menuCmd;
	QDateTime m_showTime;
	QDateTime m_loadTime;
	bool m_bCustomColor = false;
	bool m_bReportShowed = false;
	int m_scrollMaxValue = 0;
	int m_scrollCount = 0;
	QStackedLayout* m_resourceLayout = nullptr;
};

class KInsertTableMenuWidget : public KMenuWidget
{
	Q_OBJECT
public:
	explicit KInsertTableMenuWidget(KCommand* cc, QWidget* parent, bool isVLayout = true);
	~KInsertTableMenuWidget();

public:
	virtual void setVisible(bool visible) override;

protected:
	bool eventFilter(QObject* watched, QEvent* event) override;
	void hideEvent(QHideEvent* e) override;

	KTableStyleInfoCollect::KTableReportInfo getReportInfo();
protected slots:
	void onPopup();
	void onHoverTimerOut();
	void onItemTrigger(KMenuWidgetItem* item);
private:
	bool isPopuping();
	void unsetPopup();
	void delayUnsetPopup();

private:
	bool m_bInstallFilter = false;
	QTimer m_delayUnsetPopupTimer;
};