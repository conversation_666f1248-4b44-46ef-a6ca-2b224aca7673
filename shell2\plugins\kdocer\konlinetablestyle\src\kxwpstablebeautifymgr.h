﻿#pragma once
#include "kdownload/kdownload.h"
#include "kxwpstablebeautifyhelper.h"
#include "kxonlinetablestyledefine.h"

class KTableEmphasizer;
class KTableSelectionNotify;
namespace onekeybeautify
{
	HRESULT getTableColWidthAndRowHeight(QVector<single>& colWidth, QVector<single>& rowHeight);
	bool isTableHorCenter();
	bool isAdjustedTable(QVector<single>& preColWidths, QVector<single>& preRowHeights);
};

class KTableEmphasizeMgr: public QObject
{
	Q_OBJECT

public:
	static KTableEmphasizeMgr* getInstance();

	bool emphasizeHorBorders(const int rowIndex = 0);
	bool emphasizeHorBtmBorder(const int rowIndex = 0);
	bool emphasizeHorFill(const int rowIndex = 0);
	bool emphasizeHorNone(const int rowIndex = 0);

	bool emphasizeVerBorders(const int colIndex = 0);
	bool emphasizeVerFill(const int colIndex = 0);
	bool emphasizeVerNone(const int colIndex = 0);

	bool emphasizeTable(const int index, const int style, const QString& type);
	bool emphasizeNone(const QString& type);
	bool emphasizeTable(KTableEmphasizer* emphasizer, int index, KTableEmphasizeType type);
	void clearEmphasizeInfo();

	bool isTableValid();
	QString getTableSeletedInfo();
	QMap<QString, KEmphasizeInfo> getEmphasizeInfo();
	bool getTableInfo(KTableRowColInfo& tbInfo);

private slots:
	void onSelectionChanged(bool bForce);

signals:
	void sigSelectionChanged();

private:
	KTableEmphasizeMgr();
	~KTableEmphasizeMgr();

private:
	KTableEmphasizer* m_rowEmphasizer;
	KTableEmphasizer* m_colEmphasizer;
	KEmphasizeIntersectCellInfo m_intersectCellInfo;
	KTableSelectionNotify* m_notify;
};

namespace ksolite
{
	class KxCommonJsApi;
};
class KWpsOneKeyTableArrangeMgr: public QObject
{
	Q_OBJECT

public:
	static KWpsOneKeyTableArrangeMgr* getInstance();

	void oneKeyTableArrange(ksolite::KxCommonJsApi* jsApi);
	void makeTableToCenter(wpsoldapi::Table* table);
	int readCurStyleIdIndex();

signals:
	void applyTableStyle(const KOTSResourceInfo&, const ReportInfo&, const QString&, bool, ksolite::KxCommonJsApi*);

private:
	void makeTableHorCenter(wpsoldapi::Table* table);
	void setRowHeightFitContent(wpsoldapi::Table* table);
	void setTableAutoFit(wpsoldapi::Table* table);

	void applyTableStyleRes(const QString& filePath);

	QList<int>& getStyleIdLst();
	void setCurStyleIdIndex(const int index);

private:
	KWpsOneKeyTableArrangeMgr();
	~KWpsOneKeyTableArrangeMgr();

private:
	ksolite::KxCommonJsApi* m_jsApi;
};
