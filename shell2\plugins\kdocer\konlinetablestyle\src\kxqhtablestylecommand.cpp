#include "stdafx.h"
#include "kxqhtablestylecommand.h"
#include "kxtponlinetablestylewidget.h"
#include "ktablebeautify.h"
#include "kxonlinereshelper.h"
#include "kxwpptablestylehelper.h"
#include <kxshare/kxcommands.h>
#include <kcomctl/kproxycommand.h>
#include <kcomctl/kcommandfactory.h>
#include <kcomctl/kquickhelpbarwidget.h>
#include <ksolite/kcloudsvrproxy.h>
#include "kxtablestylefeaturecommand.h"

#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include <kdocertoolkit/kdocerutils.h>
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "kdocercorehelper.h"
namespace
{
	constexpr const char* PasteAction = "paste";
	constexpr const char* OnekeyBeautifyAction = "onekeybeautify";
	constexpr const char* HighlightContentsAction = "highlightcontents";

	void reportBtnClick(const QString btnName)
	{
		if (btnName.isEmpty())
			return;

		QHash<QString, QString> args;
		args.insert("from", "quickbar_tablestyle_btn");
		args.insert("belong_page", "quickbar_tablestyle");
		args.insert("button_name", btnName);
		args.insert("operation", "click");
		KDocerUtils::postGeneralEvent("docer_tablestyle_btnclick", args);
	}

}

KxQhTableSmartStyleCommand::KxQhTableSmartStyleCommand(KxMainWindow* host, QObject* parent)
	: KxGalleryCommand(host, parent)
	, m_proxyCommand(nullptr)
{
	setProperty("autoMark", "tablesmartstyle");
}

KxQhTableSmartStyleCommand::~KxQhTableSmartStyleCommand()
{
}

void KxQhTableSmartStyleCommand::update()
{
	if (!KDocerUtils::isCoreAppMatch(this))
		return;

	bool bVisible = false;
	if (auto ikDocerCore = getIKDocerCore())
	{
		if (auto helper = ikDocerCore->getWppOnlineTaskPaneHelper())
		{
			bVisible = helper->isValidStyleTable(true);
		}
	}
	setVisible(bVisible);
}

void KxQhTableSmartStyleCommand::syncCommandInfo(QWidget* widget)
{
	KxGalleryCommand::syncCommandInfo(widget);
}

QWidget* KxQhTableSmartStyleCommand::createExtendedWidget(QWidget* parent)
{
	reportBtnClick("tablestyle_button");

	if (!m_featureCmd)
		m_featureCmd = new KxTableStyleFeatureCommand(kxApp->findRelativeMainWindowX(this), this);

	KxOnlineTableStyleWidget* widget = new KxOnlineTableStyleWidget(parent, m_featureCmd, false, true);
	widget->setFixedSize(KWPSStyle::dpiScaledSize(QSize(444, 387)));
	widget->setProperty("quickBar", true);

	QString onlineExtendURL = KxOnlineTableResHelper::getResourcePath(
		0 == kxApp->applicationName().compare("wpp", Qt::CaseInsensitive) ?
		KxOnlineTableResHelper::KOT_WPP_OnlineTableStyleInline
		: KxOnlineTableResHelper::KOT_OnlineTableStyleInline);
	QString entrance = property("entrance").toString();
	QString detailEntrance = property("p4").toString();
	onlineExtendURL += ("&detailEntrance=" + detailEntrance);
	onlineExtendURL += ("&paysource=" + KxOnlineTableResHelper::getPaySource(entrance, detailEntrance));
	widget->init(onlineExtendURL, "konlinetablestyle_tp");
	return widget;
}

KCommand* KxQhTableSmartStyleCommand::clone(QObject* host, QObject* parent)
{
	KxQhTableSmartStyleCommand* pCmd = nullptr;
	if (KxMainWindow* pMainWin = qobject_cast<KxMainWindow*>(host))
	{
		pCmd = new KxQhTableSmartStyleCommand(pMainWin, parent);
		copyProperties(pCmd);
	}
	return pCmd;
}

DECLARE_COMMAND_FACTORY(KxQhTableSmartStyleCommand, KxMainWindow);