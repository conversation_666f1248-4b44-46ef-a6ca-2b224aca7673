﻿#pragma once

#include "kxonlinetablestyledefine.h"
#include <kxshare/kxtaskpanecommand.h>
#include "common/jsapi/widget/kdocercommonwebwidget.h"

class KxDocerTpTableStyleCommand;
struct TableInfo;

class KxOnlineTableStyleWidget;
class KxTableStyleFeatureCommand
	: public KTriggerCommand
{
	Q_OBJECT
public:
	explicit KxTableStyleFeatureCommand(KxMainWindow* host, QObject* parent);
	~KxTableStyleFeatureCommand();

public:
	KCommand* clone(QObject* host, QObject* parent) override;
	void update() override;
	void getHasMultiChildTable(bool bApplyAll, bool& bAnalyzed, bool& bHasMultiChildTable);

public slots:
	void onOpenTpbyAction(const QVariantMap& params);
	void applyTableStyle(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, const QString& filePath, bool hover);
	void applySystemTableStyle(int resourceIndex, bool hover);

signals:
	void hoverOnlineResourceSuccess();
	void hoverSystemResourceSuccess();
	void sendInsertInfo(const KOTSResourceInfo& info, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);
	void sendSystemInsertInfo(int resourceIndex, OperatorErrorCode errorCode, const QString& errorDesc);
	void pannelEnable(PanelEnableStatus status);
	void updateTableInfo(const TableInfo& tableInfo);
	void sigGridLineStatuesChange(bool bGridlineChecked);
	void sigIndentifyStatus(TableRecoStaus status, TableAnalyzeFrom from);
	void sigArrangeApplyResult(bool bSuccess, TableArrangeResult result, qint64 totalTimes, TableApplyInfo tableApplyInfo);
	void sigApplyArrange(const QString& applyType, bool bApplyAll);
	void sigLeaverHover();
	void sigAnalyzeTable(TableAnalyzeFrom);
	void sigCancelAnalyze();
	void sigUpdateTableView();
	void sigGetHasMultiChildTable(bool bApplyAll);
	void sigGetHasMultiChildTableResult(bool, bool);

protected:
	QWidget* createExtendedWidget(QWidget* parent);

private:
	QPointer<KxOnlineTableStyleWidget> m_featureWidget;
	KxDocerTpTableStyleCommand* m_tableStyleCmd;
};

///////////////////////////////////////////////////////////////////
class KxWppTableStyleFeatureCommand : public KxTableStyleFeatureCommand
{
	Q_OBJECT
public:
	explicit KxWppTableStyleFeatureCommand(KxMainWindow* host, QObject* parent);
};
