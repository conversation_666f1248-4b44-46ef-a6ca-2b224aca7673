﻿#include "stdafx.h"
#include "kxtablestylegallerycommand.h"
#include "kxonlinereshelper.h"
#include "kxetcommonheander.h"
#include "kxtponlinetablestylewidget.h"
#include <kcomctl/kclassiccommands.h>
#include <kxshare/kxcommands.h>
#include <kcomctl/kcommandfactory.h>
#include <kcomctl/kproxycommand.h>
#include <QtNetwork/qsslconfiguration.h>
#include "kcomctl/kpopupcefwidget.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include "kxtablestylefeaturecommand.h"
#include "common/widget/kdocerwidgetutil.h"
#include "krt/global.h"

#include "custom/kxdocertablestylewidget.h"
#include <kdocertoolkit/kdocerutils.h>

namespace
{
	const char* const s_strTableStyleCmd = "TableStyleGallery";
	const char* const s_strTableStyleCmdInHome = "TableStyleGalleryInHomeTab";
	const char* const s_strPivotTableStyleCmd = "PivotTableStyleGallery";
	const char* const s_strPivotTableStyleCmdInHome = "PivotTableStyleGalleryInHomeTab";
	const char* const s_strPivotOnlineTableStyle = "PivotOnlineTableStyle";
	const char* const s_strPivotOnlineTableStyleInHomeTab = "PivotOnlineTableStyleInHomeTab";
	const char* const s_strOnlineTableStyle = "OnlineTableStyle";
	const char* const s_strOnlineTableStyleInHomeTab = "OnlineTableStyleInHomeTab";
	const char* const s_strTableStyleBtnInHome = "start_tabstyle_btn";
	const char* const s_strPivotTableStyleBtnInHome = "start_pivot_tbstyle_btn";
	constexpr DWORD s_nTableHeaderRowBit = 0x0001;
	constexpr DWORD s_nTableFirstColBit = 0x0010;
	constexpr DWORD s_nPivotTableHeaderRowBit = 0x0100;
	constexpr DWORD s_nPivotTableHeaderColBit = 0x0200;
};

KxTableStyleGalleryCommand::KxTableStyleGalleryCommand(KxMainWindow * host, QObject * parent)
	: KDocerCommonCommand<KxGalleryCommand>(host, parent)
{
}

KxTableStyleGalleryCommand::~KxTableStyleGalleryCommand()
{
}

KCommand* KxTableStyleGalleryCommand::clone(QObject* host, QObject* parent)
{
	KxMainWindow* pMainWin= qobject_cast<KxMainWindow*>(host);
	if (!pMainWin)
		return nullptr;

	KxTableStyleGalleryCommand* pCmd = 
		new KxTableStyleGalleryCommand(pMainWin, parent);
	copyProperties(pCmd);
	return pCmd;
}

HRESULT KxTableStyleGalleryCommand::Get(KSO_Group, KSO_DataID id, void*, void* val)
{
	ASSERT(id == KSO_Data_TableStyleValue);

	if (m_curTableInfo.id == -1)
		return E_FAIL;

	if (!KxOnlineTableResHelper::isSelectTable() && KxOnlineTableResHelper::isSelectPivotTable())
		m_curTableInfo.styleOption = tableStyleOptToPivotTableStyleOpt(m_curTableInfo.styleOption);

	DWORD* styInfo = static_cast<DWORD*>(val);
	styInfo[0] = m_curTableInfo.id;
	styInfo[1] = m_curTableInfo.styleOption;

	return S_OK;
}

DWORD KxTableStyleGalleryCommand::tableStyleOptToPivotTableStyleOpt(const DWORD opt)
{
	//透视表应用稻壳表格样式需做转换
	//具体转换为: 表格标题行->透视表列标题; 表格第一列->透视表行标题;
	//具体参见 et_appcore_table_style.h
	DWORD ret = opt;
	if (!(opt & s_nPivotTableHeaderColBit) && (opt & s_nTableHeaderRowBit))
		ret = (opt & (~s_nTableHeaderRowBit)) | s_nPivotTableHeaderColBit;

	if (!(ret & s_nPivotTableHeaderRowBit) && (ret & s_nTableFirstColBit))
		ret = (ret & (~s_nTableFirstColBit)) | s_nPivotTableHeaderRowBit;

	return ret;
}

OperatorErrorCode KxTableStyleGalleryCommand::activeEtTableStyle(const TableInfo& tableInfo)
{
	m_curTableInfo = tableInfo;
	
	//改为同步调用，可以获取插入返回值
	if (IActionTarget* actionTarget = getActionTarget())
	{	
		HRESULT hr = actionTarget->Exec(m_ksocmd->group, m_ksocmd->cmdID, OLECMDEXECOPT_DODEFAULT, this);

		if (E_ABORT == hr)
		{
			return InsertOpCansel;
		}
		else
		{
			return SUCCEEDED(hr) ? Success : InsertOpError; 
		}
	}

	return InsertOpError;
}

QString KxTableStyleGalleryCommand::getDetailEntrance(const QString& cmdId /*= QString()*/)
{
	QString activeMenuBar = KDocerCommonHelper::getActiveMainWindowToolBarCommandId();
	QString detailEntrance;
	if (activeMenuBar == "CT_TableTool" || activeMenuBar == "CT_Design"
		|| activeMenuBar == "CT_DesignTable" || activeMenuBar == "CT_PivotTableDesign")
	{
		if (KDocerUtils::isEtApp(this))
		{
			detailEntrance = "intab_tabtool_btn";
			if (cmdId == s_strPivotOnlineTableStyle || cmdId == s_strPivotOnlineTableStyleInHomeTab)
				detailEntrance = "design_pivot_tbstyle";
		}
		else
		{
			detailEntrance = "intab_tabstyle_btn";
		}
	}
	else if (activeMenuBar == "CT_Home")
	{
		detailEntrance = s_strTableStyleBtnInHome;
		if (cmdId == s_strPivotOnlineTableStyle || cmdId == s_strPivotOnlineTableStyleInHomeTab)
			detailEntrance = "start_pivot_tbstyle_btn";
	}

	return detailEntrance;
}

bool KxTableStyleGalleryCommand::isCompatibilityMode()
{
	if (KDocerUtils::getCoreApplication(this))
	{
		IKDocument* pActDoc = KDocerUtils::getCoreApplication(this)->GetActiveDocument();
		if (pActDoc)
			return pActDoc->IsCompatibilityMode();
	}

	return false;
}

QWidget* KxTableStyleGalleryCommand::createExtendedWidget(QWidget* parent)
{
	if (!KDocerUtils::isCoreAppMatch(this))
		return nullptr;

	if (KDocerUtils::isEtApp(this) || KDocerUtils::isWpsApp(this))
	{
		if (!m_featureCmd)
		{
#ifdef WPP_PROJECT
			m_featureCmd = new KxWppTableStyleFeatureCommand(kxApp->findRelativeMainWindowX(this), this);
#else
			m_featureCmd = new KxTableStyleFeatureCommand(kxApp->findRelativeMainWindowX(this), this);
#endif // WPP_PROJECT
		}

		QString cmdName = s_strTableStyleCmd;
		if(KDocerUtils::isEtApp(this))
		{
			QString id = property("id").toString();
			QString detailEntrance = getDetailEntrance(property("id").toString());
			if (id == s_strOnlineTableStyle || id == s_strOnlineTableStyleInHomeTab)
			{
				if (detailEntrance == s_strTableStyleBtnInHome)
					cmdName = s_strTableStyleCmdInHome;
			}
			else if (id == s_strPivotOnlineTableStyle || id == s_strPivotOnlineTableStyleInHomeTab)
			{
				cmdName = s_strPivotTableStyleCmd;
				if (detailEntrance == s_strPivotTableStyleBtnInHome)
					cmdName = s_strPivotTableStyleCmdInHome;
			}
		}
		QWidget* w = new KxDocerTableStyleWidget(parent, m_featureCmd, cmdName);
		return w;
	}

	QWidget* w = KDocerCommonCommand<KxGalleryCommand>::createExtendedWidget(parent);
	KxOnlineTableStyleWidget* widget = w ? w->findChild<KxOnlineTableStyleWidget*>() : nullptr;
	if (widget)
	{
		parent->setFixedHeight(widget->sizeHint().height());
		widget->setFixedHeight(widget->sizeHint().height());

		KxWebViewContainer* webView = widget->getWebview();
		if (webView && webView->getWebView())
			webView->getWebView()->setFixedHeight(widget->sizeHint().height());
	}
	return w;
}

QWidget* KxTableStyleGalleryCommand::createWidget(QWidget* parent, KCommand::WidgetPurpose wp)
{
	if (isCompatibilityMode())
	{
		if (KxOnlineWidget* w = KTik::findParentByType<KxOnlineWidget>(parent))
			w->setVisible(false);

		return nullptr;
	}

	if (!m_featureCmd)
	{
#ifdef WPP_PROJECT
		m_featureCmd = new KxWppTableStyleFeatureCommand(kxApp->findRelativeMainWindowX(this), this);
#else
		m_featureCmd = new KxTableStyleFeatureCommand(kxApp->findRelativeMainWindowX(this), this);
#endif // WPP_PROJECT
	}

	KxOnlineTableStyleWidget* onlineWidget = new KxOnlineTableStyleWidget(parent, m_featureCmd, false, false, true);
	onlineWidget->setFixedWidth(onlineWidget->sizeHint().width());
	onlineWidget->setFixedHeight(onlineWidget->sizeHint().height());
	QString onlinePocketURL = KxOnlineTableResHelper::getResourcePath(
		KDocerUtils::isWppApp(this) ?
		KxOnlineTableResHelper::KOT_WPP_OnlineTableStylePocket :
		KxOnlineTableResHelper::KOT_OnlineTableStylePocket);
	QString detailEntrance = getDetailEntrance(property("id").toString());
	onlinePocketURL += ( "&detailEntrance=" + detailEntrance);
	onlinePocketURL += ( "&paysource=" + KxOnlineTableResHelper::getPaySource("menubar_tablestyle_page", detailEntrance));
	onlineWidget->init(onlinePocketURL, "konlinetablestyle_menu");
	onlineWidget->onParentReady();
	docer::widget::updatePopupWidgetSize(onlineWidget);

	//上报来源
	onlineWidget->setProperty("p1", "menubar_tablestyle_page");
	onlineWidget->setProperty("p4", detailEntrance);

	QString cmdName = s_strTableStyleCmd;
	QString id = property("id").toString();
	if (id == s_strOnlineTableStyle || id == s_strOnlineTableStyleInHomeTab)
	{
		if (detailEntrance == s_strTableStyleBtnInHome)
			cmdName = s_strTableStyleCmdInHome;
	}
	else if (id == s_strPivotOnlineTableStyle || id == s_strPivotOnlineTableStyleInHomeTab)
	{
		cmdName = s_strPivotTableStyleCmd;
		if (detailEntrance == s_strPivotTableStyleBtnInHome)
			cmdName = s_strPivotTableStyleCmdInHome;
	}

	KCommand* wppTableStyleCommand = kxApp->findRelativeMainWindowX(this)->commands()->command(cmdName);
	if (wppTableStyleCommand)
	{
		connect(wppTableStyleCommand, SIGNAL(tableWidgetChanged(int)), onlineWidget, SLOT(tabIndexChanged(int)));
	}

	return onlineWidget;
}

///////////////////////////////////////////////////////////////////
KxWppTableStyleGalleryCommand::KxWppTableStyleGalleryCommand(KxMainWindow* host, QObject* parent)
	: KxTableStyleGalleryCommand(host, parent)
{
}

#ifdef WPP_PROJECT
DECLARE_COMMAND_FACTORY(KxWppTableStyleGalleryCommand, KxMainWindow)
#else
DECLARE_COMMAND_FACTORY(KxTableStyleGalleryCommand, KxMainWindow)
#endif // WPP_PROJECT