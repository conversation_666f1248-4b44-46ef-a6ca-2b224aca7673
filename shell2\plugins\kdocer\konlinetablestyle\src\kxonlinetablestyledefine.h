﻿#pragma once

#define KPluginNameW L"konlinetablestyle"
#define TABLESTYLE_REQUEST_COUNT 32
#ifdef Q_OS_WIN
#define g_strNonEmptyCellLimitRecId 66358
#define g_strGroupModuleId 60
#elif defined Q_OS_OHOS
#define g_strNonEmptyCellLimitRecId 66368
#define g_strGroupModuleId 142
#else
#define g_strNonEmptyCellLimitRecId 66362
#define g_strGroupModuleId 139
#endif

enum OperatorErrorCode
{
	Success = 0,
	StyleFileDamage,
	StyleFileInValide,
	InsertOpError,
	DownloadError,
	InsertOpCansel,
	InsertOpParserFailed,
	InsertOpNotImple,
	InsertOpNoActiveTable,
	TableRecognizeFailed,
	TableRecognizeCancel,
	LastApplyItemChanged,
	BatchOneSheetProtectedOrHided,
	BatchOneSheetFinished,
	BatchApplyFinished,
	BatchApplyStart,
	ErrorCodeEnd
};

enum TableStyleWidgetType
{
	TSWT_InListobject,
	TSWT_InPivotTable,
};

enum class TableAnalyzeFrom {
	FromBegin,
	FromPreview = 1,
	FromApply,
	FromArrange,
	FromBatch
};

enum class TableArrangeResult {
	ArrangeSuccess = 0,
	ArrangenoValidArea,
	ArrangeFailed,
	ArrangeSheetInvalid
};

enum class TableRecoStaus {
	TableRecogizeBegin = -1,
	TableRecogizing = 0,
	TableRecogizeSuucess,
	TableRecogizeFailed,
	TableRecogizeNoUpdate,
	TableRecogizeTooLarge,
};

enum class DownloadResult {
	Success,
	ParamsError,
	RequestError,
	NoLogin,
	PrivilegeError,
	ResourceStatusError,
	ServerError,
	OtherError
};

enum class PanelEnableStatus : int {
	Enable = 0,
	SecureDocReadOnly,
	CompatibilityMode,
	EtCellEdit,
	EtWorkSheetProtect,
	OtherErrorStatus
};

enum class ThumbType :int {
	Small,
	Medium,
	Big
};

enum class RecoType {
	Service = 0,	//仅服务端识别
	Effect,			//超阈值使用排版兜底
	TableStyle,		//超阈值使用表格样式扩表方式兜底
	Retry,			//单个表格样式应用失败重试，直接走兜底
};

struct KOTSResourceInfo{
	int themeColorKey = 0;			//主题色key，1-7; 7对应深色 1
	QString id;
	QString name;
	QString color;
	QString customColor;		//表示自定义颜色
	QVariant area;
	bool bHover = false;	//表示当前是否来自资源hover
	bool bApplyAll = false;	//仅表格组件有效，表示是否将表格样式应用到全部的表格中
	bool bBatchApply = false; //仅表格组件有效，在bApplyAll为true的基础上，表示是否将表格样式应用到整个工作簿，还是仅当前工作表
	bool bInsertTable = false;	//仅文字与演示组件下使用，表示是否在应用表格样式前插入一个表格
	bool bHoverReturnResult = false;	//hover状态下，是否返回当前应用结果；用于表格右键、工具选项卡-表格美化、会员专享-表格美化调起；前端需要根据返回值切换提示条状态
};

struct KTableStyleResourceInfo {
	QString id;
	QString name;
	QString md5;
	QString mkKey;
	QString downloadKey;
	QString policy;
	QString uuid;
	QString rid;
	bool bFree = false;
	QString resourceKey;
	QVariantMap colorInfo;
	QStringList privileges;
	QMap<ThumbType, QString> previewUrlMap;
};

struct TableDownloadReportInfo 
{
	QString id;
	QString md5;
	QString mkKey;
	QString downloadKey;
	QString channel;
	QString subChannel;
	QString clientType;
	QString component;
};

struct TableItemRquestInfo 
{
	QString position;
	int offset = 0;
	int limit = TABLESTYLE_REQUEST_COUNT;
};

struct TableApplyInfo
{
	quint64 nonemptyCellCnt = 0;	//非空单元格数（当前工作表）
	quint64 areaCellCnt = 0;		//区域单元格数（当前工作表）
	quint64 extractTime = 0;		//提取表格数据时间（当前工作表）
	quint64 identifyTime = 0;		//表格结构识别时间（当前工作表）
	quint64 beautifyTime = 0;		//样式套用时间（当前工作表）
	quint64 totalTime = 0;			//总美化时间
	QString selectCell;				//框选单元格模式
	quint64 totalStartTime = QDateTime::currentDateTime().toMSecsSinceEpoch();			//美化开始时间，便于后续计算总美化时间
	bool	isHuge = false;
	bool	isAutoFit = false;

	void clearTime() {
		extractTime = 0;
		identifyTime = 0;
		beautifyTime = 0;
		totalTime = 0;
		totalStartTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
	};
};

struct ReportInfo
{
	QString from;
	QString belongPage;
	QString tablestyleId;
	QString userStatusId;
	QString sourceStatusId;
	QString downloadKey;
	bool	click = false;
	bool	hasMultiChildTable = false;
	bool	hasNormalTable = false;
	bool	hasTableApply = false;
	bool	isAreaApply = false;
	int tableNumber = 0;
	int currentTableNumber = -1;			//当前正在处理的工作表或页排序数
	TableApplyInfo tableApplyInfo;
};
struct TableInfo
{
	TableInfo():id(-1)
		,styleOption(0xFFFFFFFF)
	{}

	int id;
	DWORD styleOption;
};

struct KDocerButtonStyle
{
	QIcon icon;
	QString text;
	QString skuKey;
	QColor textColor;
	QColor textColorHover;
	QColor textColorClick;
	QColor backgroundColor;
	QColor backgroundColorHover;
	QColor backgroundColorClick;
};

const QString getErrorDesc(OperatorErrorCode code);