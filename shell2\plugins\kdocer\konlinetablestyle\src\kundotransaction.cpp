﻿#include "stdafx.h"
#include "kundotransaction.h"
using namespace etoldapi;

KUndoTransaction::KUndoTransaction(_Workbook* pWorkbook,
	PCWSTR desc,
	BOOL bClearCopy)
{
	m_Desc = desc;
	m_bClearCopy = bClearCopy;

	m_ptrWorkbook = pWorkbook;

	KCOMPTR(IKWorkbook) pEtWorkbook = pWorkbook;
	m_bEndtrans	   = FALSE;
	Init(pEtWorkbook);
	m_ptrUndoTrans->StartTrans();
}

void KUndoTransaction::Init(IKWorkbook* pEtWorkbook)
{
	m_bNeedCommit	= TRUE;
	m_ptrUndoTrans = pEtWorkbook->GetTransactionTool();
	m_ptrEtApp = pEtWorkbook->GetApplication();

	ASSERT(m_ptrUndoTrans != NULL);
}

void KUndoTransaction::AddTransCommand(interface IKTransCommand* pCmd)
{
	m_ptrUndoTrans->AddCommand(pCmd);
}

void KUndoTransaction::EndTrans()
{
	try
	{
		ks_stdptr<_Application>	ptrApp = m_ptrEtApp;
		ASSERT(ptrApp);
		if (m_bClearCopy)
		{
			KCOMPTR(Range) ptrRange;
			ptrRange = m_ptrEtApp->GetAppPersist()->GetCutCopyRange();
			if (ptrRange != NULL)
				ptrApp->put_CutCopyMode(etCopyCutNone);
		}

		if (!m_ptrWorkbook)
		{
			m_bEndtrans = TRUE;
			return;
		}

		if (m_bNeedCommit)
			m_ptrUndoTrans->CommitTrans(m_Desc.c_str(), ksoCommit, FALSE);
		else
			m_ptrUndoTrans->Rollback();
	}
	catch (...)
	{
		ASSERT(!"严重错误！");
	}
	m_bEndtrans = TRUE;
}

_Workbook*	KUndoTransaction::GetEntry()
{
	return m_ptrWorkbook;
}

KUndoTransaction::~KUndoTransaction()
{
	m_ptrWorkbook.clear();
	if (!m_bEndtrans)
	{
		EndTrans();
	}
}

void KUndoTransaction::CancelTrans(HRESULT hr, BOOL bReportError, BOOL bClearCopy)
{
	m_bNeedCommit = FALSE;
	if (bReportError)
		ReportError(hr);
	if (m_bClearCopy)
		m_bClearCopy = bClearCopy;
}

void KUndoTransaction::ReportError(HRESULT hr)
{
	// ֻ����Ƕ������������ŵ������󣬷����ڶ��Ƕ���лᵯ�����������Ϣ��

	// 51818, 51819, applogic ��Ƕ�ײ���һ�㶼�����1
	// ��uilogic ��Ϊ applogic ���ǳɹ���������� applogic ��������Ͳ����д�����ʾ

	//	if (m_ptrUndoTrans->GetNestLevel() <= 1)
	{
		ks_stdptr<_Application>	ptrApp = m_ptrEtApp;
		ASSERT(ptrApp); 
		ptrApp->_ReportError(hr);	
	}
}