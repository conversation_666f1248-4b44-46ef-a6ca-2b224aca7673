﻿#pragma once
#include "kso/api/wpsapi_old_enum.h"

enum KTableEmphasizeType
{
	TET_Invalid = -1,
	TET_None,
	TET_TableBorder,
	TET_TableFill,
	TET_TableBtmBorder,
};

struct KTableRowColInfo
{
	KTableRowColInfo()
		: rowCnt(0)
		, colCnt(0)
		, curSelectRow(-1)
		, curSelectCol(-1)
	{
	}

	int rowCnt;
	int colCnt;
	int curSelectRow;
	int curSelectCol;
};

struct KBorderStyleInfo
{
	WpsLineStyle borderLineStyle;
	WpsLineWidth borderLineWidth;
	WpsBorderType borderType;
	QColor borderColor;

	KBorderStyleInfo();
	KBorderStyleInfo(const WpsLineStyle lineStyle, const WpsLineWidth lineWidth,
		const WpsBorderType borderType, const QColor& color);
	QJsonObject toJsonObject();
	static KBorderStyleInfo fromJsonObject(const QJsonObject& obj);
};

struct KEmphasizeStyleInfo
{
	bool bBoldFont;
	bool bValid;
	QMap<WpsBorderType, KBorderStyleInfo> borderStyleLst;
	QColor fontColor;
	QColor backgroundColor;

	KEmphasizeStyleInfo();
	QJsonObject toJsonObject();
	static KEmphasizeStyleInfo fromJsonObject(const QJsonObject& obj);
};

struct KEmphasizeIntersectCellInfo
{
	KEmphasizeStyleInfo rowStyle;
	KEmphasizeStyleInfo colStyle;
	KEmphasizeStyleInfo rowOriginStyle;
	int row;
	int column;
	bool bValid;

	KEmphasizeIntersectCellInfo();
	QJsonObject toJsonObject();
	static KEmphasizeIntersectCellInfo fromJsonObject(const QJsonObject& info);
};

struct KEmphasizeInfo
{
	bool bValid;
	int emphasizeNum;
	KEmphasizeStyleInfo originStyle;
	KEmphasizeStyleInfo columnHeaderCellStyle;
	KTableEmphasizeType emphasizeType;

	KEmphasizeInfo();

	QJsonObject toJsonObject();
	static KEmphasizeInfo fromJsonObject(const QJsonObject& info);
};

struct IKView;
struct IKSelection;
namespace wpsoldapi
{
	struct Selection;
	struct Table;
	struct Shading;
	struct Borders;
};

namespace drawing
{
	class Color;
};
namespace KEmphasizeTableHelper
{
	HRESULT getCurSelectTable(wpsoldapi::Table** tb);
	IKView* getActiveView();
	IKSelection* getCurSelection();
	void getShading(wpsoldapi::Shading** ppShading);
	QString readEmphasizeInfo();
	void writeEmphasizeInfo(const QString& info);
	void getSelShading(wpsoldapi::Selection* selection, wpsoldapi::Shading** ppShading);
	bool getCellsFillColor(wpsoldapi::Shading* cells, QColor& color);
	bool getSelCellsFillColor(wpsoldapi::Selection* selection, QColor& color);
	bool getFontStyle(QColor& color, bool& bBold);
	bool getSelFontStyle(wpsoldapi::Selection* selection, QColor& color, bool& bBold);
	bool getBorderStyle(QMap<oldapi::WpsBorderType, KBorderStyleInfo>& borderStyleLst);
	bool getSelBorderStyle(wpsoldapi::Selection* selection, QMap<oldapi::WpsBorderType, KBorderStyleInfo>& borderStyleLst);
	int getColorGray(const drawing::Color& o, int gray);
	bool setFontStyle(const QColor& color, const bool bBold);
	bool setSelFontStyle(wpsoldapi::Selection* selection, const QColor& color, const bool bBold);
	bool selectCellWithIndex(const int row, const int col);

	bool getCurSelectIndex(int& row, int& column);
	bool getTableColRowInfo(wpsoldapi::Table* table, int& rowCnt, int& colCnt);
};
