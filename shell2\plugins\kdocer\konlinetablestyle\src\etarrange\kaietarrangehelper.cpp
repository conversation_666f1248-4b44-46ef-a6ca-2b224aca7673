#include <stdafx.h>
#include "kaietarrangehelper.h"
#include "kxonlinetablestyledefine.h"
#include <common/request/network/knetworkrequest.h>
#include <kdocertoolkit/kdocerutils.h>
#include <ksolite/kdcinfoc.h>
#include <ksolite/log.h>
#include <kxshare/kaescodec.h>
#include <zlib.h>
#include <cryptopp/hmac.h>
#include <cryptopp/sha.h>
#include <cryptopp/filters.h>
#include "kdocertoolkitlite/kdocerutilslite.h"
#define ET_ARRANGE_TASK "zone_recog"

namespace KAIEtArrangeHelper {

	QString encrypt(const QByteArray& text)
	{
		char dde[32] = { char(0x8b ^ 0x12), char(0xe3 ^ 0x13), char(0xe4 ^ 0x14), char(0x91 ^ 0x15), char(0x69 ^ 0x12),
			char(0xb7 ^ 0x13), char(0x7e ^ 0x14), char(0x0f ^ 0x15), char(0x05 ^ 0x12), char(0x2a ^ 0x13),
			char(0x83 ^ 0x14), char(0xa5 ^ 0x15), char(0x73 ^ 0x12), char(0x1e ^ 0x13), char(0x38 ^ 0x14),
			char(0x5a ^ 0x15), char(0xc5 ^ 0x12), char(0xbd ^ 0x13), char(0xeb ^ 0x14), char(0xff ^ 0x15),
			char(0x1d ^ 0x12), char(0x49 ^ 0x13), char(0x01 ^ 0x14), char(0xe4 ^ 0x15), char(0x32 ^ 0x12),
			char(0x63 ^ 0x13), char(0xd2 ^ 0x14), char(0x1b ^ 0x15), char(0xf8 ^ 0x12), char(0xdc ^ 0x13),
			char(0x7c ^ 0x14), char(0x93 ^ 0x15) };

		for (int i = 0; i < 32; ++i)
		{
			int m = i % 4;
			if (m == 0)
				dde[i] = dde[i] ^ 0x12;
			else if (m == 1)
				dde[i] = dde[i] ^ 0x13;
			else if (m == 2)
				dde[i] = dde[i] ^ 0x14;
			else
				dde[i] = dde[i] ^ 0x15;
		}
		QByteArray iv;
		if (!KAesCodec::generateAesKey(iv, 16))
			return QString();

		QString rv;
		if (!KAesCodec::aesEncryptCBCMode(
			QByteArray(dde, 32),
			iv,
			text,
			rv))
		{
			ASSERT(FALSE);
			return QString();
		}

		iv.append(QByteArray::fromBase64(rv.toUtf8()));
		QString t(iv.toBase64());
		return t;
	}

	QByteArray decrypt(const QString& text)
	{

		QByteArray ba = QByteArray::fromBase64(text.toUtf8());
		if (ba.length() <= 16)
			return QByteArray();
		QByteArray iv = ba.mid(0, 16);
		QByteArray content = ba.mid(16).toBase64();

		char dde[32] = { char(0x8b ^ 0x12), char(0xe3 ^ 0x13), char(0xe4 ^ 0x14), char(0x91 ^ 0x15), char(0x69 ^ 0x12),
			char(0xb7 ^ 0x13), char(0x7e ^ 0x14), char(0x0f ^ 0x15), char(0x05 ^ 0x12), char(0x2a ^ 0x13),
			char(0x83 ^ 0x14), char(0xa5 ^ 0x15), char(0x73 ^ 0x12), char(0x1e ^ 0x13), char(0x38 ^ 0x14),
			char(0x5a ^ 0x15), char(0xc5 ^ 0x12), char(0xbd ^ 0x13), char(0xeb ^ 0x14), char(0xff ^ 0x15),
			char(0x1d ^ 0x12), char(0x49 ^ 0x13), char(0x01 ^ 0x14), char(0xe4 ^ 0x15), char(0x32 ^ 0x12),
			char(0x63 ^ 0x13), char(0xd2 ^ 0x14), char(0x1b ^ 0x15), char(0xf8 ^ 0x12), char(0xdc ^ 0x13),
			char(0x7c ^ 0x14), char(0x93 ^ 0x15) };

		for (int i = 0; i < 32; ++i)
		{
			int m = i % 4;
			if (m == 0)
				dde[i] = dde[i] ^ 0x12;
			else if (m == 1)
				dde[i] = dde[i] ^ 0x13;
			else if (m == 2)
				dde[i] = dde[i] ^ 0x14;
			else
				dde[i] = dde[i] ^ 0x15;
		}

		QByteArray rv;
		if (!KAesCodec::aesDecryptCBCMode(
			QByteArray(dde, 32),
			iv,
			content,
			rv))
		{
			ASSERT(FALSE);
			return QByteArray();
		}
		return rv;
	}

	bool doCompress(const QByteArray& src,
		QByteArray& dest)
	{
		const int BUFSIZE = 128 * 1024;
		char tmpBuf[BUFSIZE];
		int ret;

		dest.clear();

		z_stream strm;
		strm.zalloc = Z_NULL;
		strm.zfree = Z_NULL;
		strm.opaque = Z_NULL;
		strm.next_in = reinterpret_cast<uchar*>(const_cast<char*>(src.data()));
		strm.avail_in = src.length();
		strm.next_out = reinterpret_cast<uchar*>(tmpBuf);
		strm.avail_out = BUFSIZE;

		// windowBits = 15 + 16 to enable gzip
		// From the zlib manual: windowBits can also be greater than 15 for optional gzip encoding. Add 16 to windowBits
		// to write a simple gzip header and trailer around the compressed data instead of a zlib wrapper.
		ret = deflateInit2(&strm, Z_BEST_COMPRESSION, Z_DEFLATED, 15 + 16, 8, Z_DEFAULT_STRATEGY);

		if (ret != Z_OK)
			return false;

		while (strm.avail_in != 0) {
			ret = deflate(&strm, Z_NO_FLUSH);
			if (ret != Z_OK)
				return false;

			if (strm.avail_out == 0) {
				dest.append(tmpBuf, BUFSIZE);
				strm.next_out = reinterpret_cast<uchar*>(tmpBuf);
				strm.avail_out = BUFSIZE;
			}
		}

		int deflateRes = Z_OK;
		while (deflateRes == Z_OK) {
			if (strm.avail_out == 0) {
				dest.append(tmpBuf, BUFSIZE);
				strm.next_out = reinterpret_cast<uchar*>(tmpBuf);
				strm.avail_out = BUFSIZE;
			}

			deflateRes = deflate(&strm, Z_FINISH);
		}

		if (deflateRes != Z_STREAM_END)
			return false;

		dest.append(tmpBuf, BUFSIZE - strm.avail_out);
		deflateEnd(&strm);

		return true;
	}

	bool doUnCompress(const QByteArray& src,
		QByteArray& dest)
	{
		dest.clear();

		if (src.size() <= 4) {
			qWarning("uncompress: Input data is truncated");
			return false;
		}

		z_stream strm;
		const int CHUNK_SIZE = 1024;
		char out[CHUNK_SIZE];

		strm.zalloc = Z_NULL;
		strm.zfree = Z_NULL;
		strm.opaque = Z_NULL;
		strm.avail_in = static_cast<uint>(src.size());
		strm.next_in = reinterpret_cast<uchar*>(const_cast<char*>(src.data()));

		const int windowBits = 15;
		const int ENABLE_ZLIB_GZIP = 32;

		int ret = inflateInit2(&strm, windowBits | ENABLE_ZLIB_GZIP); // gzip decoding
		if (ret != Z_OK)
			return false;

		// run inflate()
		do {
			strm.avail_out = CHUNK_SIZE;
			strm.next_out = reinterpret_cast<uchar*>(out);

			ret = inflate(&strm, Z_NO_FLUSH);
			Q_ASSERT(ret != Z_STREAM_ERROR); // state not clobbered

			switch (ret) {
			case Z_NEED_DICT:
			case Z_DATA_ERROR:
			case Z_MEM_ERROR:
				inflateEnd(&strm);
				return false;
			}

			dest.append(out, CHUNK_SIZE - strm.avail_out);
		} while (!strm.avail_out);

		// clean up and return
		inflateEnd(&strm);
		return true;
	}

	std::string CalcHmacSHA256(
		const char* key, size_t key_size,
		const char* request, size_t size)
	{
		CryptoPP::HMAC<CryptoPP::SHA256> hmac(
			reinterpret_cast<CryptoPP::byte const*>(key), key_size);
		std::string res;
		CryptoPP::StringSource(
			reinterpret_cast<CryptoPP::byte const*>(request),
			size,
			true,
			new CryptoPP::HashFilter(hmac, new CryptoPP::StringSink(res)));
		return res;
	}

	const std::string generateSign(quint64 ts, quint64 cellCount, const QString& reqId)
	{
		char ndde[32] = { char(0x8b ^ 0x12), char(0xe3 ^ 0x13), char(0xe4 ^ 0x14), char(0x91 ^ 0x15), char(0x69 ^ 0x12),
			char(0xb7 ^ 0x13), char(0x7e ^ 0x14), char(0x0f ^ 0x15), char(0x05 ^ 0x12), char(0x2a ^ 0x13),
			char(0x83 ^ 0x14), char(0xa5 ^ 0x15), char(0x73 ^ 0x12), char(0x1e ^ 0x13), char(0x38 ^ 0x14),
			char(0x5a ^ 0x15), char(0xc5 ^ 0x12), char(0xbd ^ 0x13), char(0xeb ^ 0x14), char(0xff ^ 0x15),
			char(0x1d ^ 0x12), char(0x49 ^ 0x13), char(0x01 ^ 0x14), char(0xe4 ^ 0x15), char(0x32 ^ 0x12),
			char(0x63 ^ 0x13), char(0xd2 ^ 0x14), char(0x1b ^ 0x15), char(0xf8 ^ 0x12), char(0xdc ^ 0x13),
			char(0x7c ^ 0x14), char(0x93 ^ 0x15) };

		for (int i = 0; i < 32; ++i)
		{
			int m = i % 4;
			if (m == 0)
				ndde[i] = ndde[i] ^ 0x12;
			else if (m == 1)
				ndde[i] = ndde[i] ^ 0x13;
			else if (m == 2)
				ndde[i] = ndde[i] ^ 0x14;
			else
				ndde[i] = ndde[i] ^ 0x15;
		}

		char s[256] = { 0 };

		if (ts > 0)
		{
			ks_sprintf_s(s, "appid=%s&cells=%lld&req_id=%s&ts=%lld",
				"pcet",
				cellCount,
				reqId.toLatin1().constData(),
				ts);
		}
		else
		{
			ks_sprintf_s(s, "appid=%s&cells=%lld&req_id=%s&ts=%lld",
				"pcet",
				cellCount,
				reqId.toLatin1().constData(),
				QDateTime::currentDateTime().toSecsSinceEpoch());
		}

		std::string sign = CalcHmacSHA256(ndde, sizeof(ndde), s, ks_strlen(s));
		QByteArray t = QByteArray(sign.c_str(), sign.length()).toBase64();
		return std::string(t.constData(), t.size());
	}

	QString generateRequestId(
		const QString& key)
	{
		QString fileName;
		GUID guid;
		CoInitialize(NULL);
		if (S_OK == ::CoCreateGuid(&guid))
		{
			QUuid uuid(guid.Data1, guid.Data2, guid.Data3, 
				guid.Data4[0], guid.Data4[1], guid.Data4[2], guid.Data4[3],
				guid.Data4[4], guid.Data4[5], guid.Data4[6], guid.Data4[7]);
			fileName = uuid.toString();
			CoUninitialize();
		}
		else
		{
			ASSERT(FALSE);
			CoUninitialize();
			return QString();
		}
		
		fileName = QCryptographicHash::hash(fileName.toUtf8(), QCryptographicHash::Md5).toHex();

		QString result = "et_";
		if (!key.isEmpty())
		{
			result.append(key);
			result.append("_");
		}

		return result.append(fileName);
	}

	void addArrangeCommonHeader(QVariantMap& header)
	{
		QString plgVer = KDocerUtils::getPluginVersion("konlinetablestyle");
		QString arrVersion = KDcInfoCDetail::getVersion();
		header["Jm-Client-Version"] = arrVersion;
		header["Jm-Client-Type"] = "personal";
		header["Jm-Client-Plugin-Version"] = plgVer.isEmpty() ? arrVersion : plgVer;
	}

	void getArrangePostData(const QJsonObject& tableObj, std::function<void(bool, bool, const EtArrangeData&)> callback)
	{
		if (tableObj.isEmpty())
		{
			callback(false, false, EtArrangeData());
			return;
		}
		QJsonArray tableList = tableObj.value("tableList").toArray();
		if (tableList.isEmpty())
		{
			callback(false, false, EtArrangeData());
			return;
		}
		quint64 cellCnt = 0;
		quint64 totalCnt = 0;
		for (const auto& item : qAsConst(tableList))
		{
			if (!item.isObject())
				continue;
			QJsonObject obj = item.toObject();
			if (!obj.contains("cellCount") || !obj.contains("tableStart") || !obj.contains("tableEnd"))
				continue;
			cellCnt += obj["cellCount"].toInt();
			QJsonArray tableStart = obj["tableStart"].toArray();
			QJsonArray tableEnd = obj["tableEnd"].toArray();
			int rowStart = tableStart[0].toInt();
			int rowEnd = tableEnd[0].toInt();
			int colStart = tableStart[1].toInt();
			int colEnd = tableEnd[1].toInt();
			totalCnt += (rowEnd - rowStart + 1) * (colEnd - colStart + 1);
		}

		QVariantMap header;
		header["Content-Type"] = "application/json;charset=utf8";
		header["Jm-Cells"] = QString::number(cellCnt);
		header["Jm-RangeCells"] = QString::number(totalCnt);
		header["Jm-Task"] = ET_ARRANGE_TASK;

		QString reqId = generateRequestId(ET_ARRANGE_TASK);
		header["Jm-Reqid"] = reqId;
		addArrangeCommonHeader(header);

		QVariantMap reqInfo;
		reqInfo["header"] = header;
		QString url = KDocerUtils::getDomainUrl("setijimo") % QLatin1String("/api/v1/recog/teval");
		docer::request(url, reqInfo, DRM_GET, [=](const QByteArray& resultData) {
			QVariantMap args = JsonHelper::convertByteArrayToQJson(resultData).toVariantMap();
			bool bPass = false;
			QString errorMsg;
			EtArrangeData arrangeData;
			QJsonObject postData;
			bool bTooLarge = false;
			do
			{
				if (args["result"].toString() != "ok")
				{
					errorMsg = "Arrange teval request failed : result not ok";
					break;
				}
				if (!args.contains("data"))
				{
					errorMsg = "Arrange teval request failed : no data";
					break;
				}
				QVariantMap data = args["data"].toMap();
				bool bRangePass = data.value("is_range_pass_v1", false).toBool();
				bool bReqPass = data.value("is_pass_v1", false).toBool();
				if (!bRangePass || !bReqPass)
				{
					bTooLarge = true;
					errorMsg = QString("Arrange teval request failed : not pass, is_range_pass_v1:%1  is_pass_v1:%2").arg(bRangePass ? "1" : "0").arg(bReqPass ? "1" : "0");
					break;
				}
				QByteArray tableData = QJsonDocument(tableObj).toJson(QJsonDocument::Compact);
				QByteArray gzipTables;
				const bool gzip = doCompress(tableData, gzipTables);
				if (!gzip)
				{
					errorMsg = "Arrange zip table failed";
					break;
				}
				QString tablesCipher = encrypt(gzipTables);
				if (tablesCipher.isEmpty())
				{
					errorMsg = "Arrange table encrypt failed";
					break;
				}
				arrangeData.postData.insert("tables_cipher", tablesCipher);
				arrangeData.postData.insert("tables_encoding", "gzip");
				arrangeData.cellCnt = cellCnt;
				arrangeData.requestId = reqId;
				arrangeData.totalCnt = totalCnt;
				arrangeData.timeout = static_cast<quint64>(data.value("timeout", 5).toInt()) * 1000;
				if (data.contains("ts"))
					arrangeData.ts = data.value("ts", 0).toULongLong();
				else
					arrangeData.ts = QDateTime::currentDateTime().toSecsSinceEpoch();
				bPass = true;
			} while (false);
			callback(bPass, bTooLarge, arrangeData);
			if (!bPass)
			{
				KxLoggerLite::writeInfo(KPluginNameW, QString("tablestyle reco: %1").arg(errorMsg).toStdWString());
				return;
			}
		}, [=](DownloadFailInfo errorInfo) {
			callback(false, false, EtArrangeData());
			KxLoggerLite::writeInfo(KPluginNameW,
				QString("tablestyle reco: request arrange teval failed url:%1 errorCode:%2 ")
					.arg(errorInfo.url)
					.arg(errorInfo.errorCode)
					.arg(errorInfo.httpStatusCode)
					.arg(errorInfo.description)
					.arg(errorInfo.errorMsg).toStdWString()
				);
		}, 15000);
	}

	void requestTableRecognize(const EtArrangeData& arrangeData, std::function<void(bool, const QJsonObject&)> callback)
	{
		if (arrangeData.postData.isEmpty())
		{
			callback(false, QJsonObject());
			return;
		}
		QVariantMap header;
		header["Content-Type"] = "application/json; charset=utf8";
		header["Jm-Appid"] = "pcet";
		header["Jm-Reqid"] = arrangeData.requestId;
		header["Jm-Cells"] = arrangeData.cellCnt;
		header["Jm-Sign"] = QString::fromStdString(generateSign(arrangeData.ts, arrangeData.cellCnt, arrangeData.requestId));
		header["Jm-Ts"] = QString::number(arrangeData.ts);
		header["Jm-Client-Entrance"] = "member_beautification";
		addArrangeCommonHeader(header);

		QVariantMap requestData;
		requestData["header"] = header;
		requestData["data"] = QString::fromUtf8(QJsonDocument(arrangeData.postData).toJson(QJsonDocument::Compact));

		QString url = KDocerUtils::getDomainUrl("setijimo") % QLatin1String("/api/v2/recog/zone");
		docer::request(url, requestData, DRM_POST, [=](const QByteArray& data) {
			QJsonObject resultParams = JsonHelper::convertByteArrayToQJson(data);
			QJsonObject resultData = resultParams["data"].toObject();
			if (!resultData.contains("zone_recog_cipher") || !resultData["zone_recog_cipher"].isString())
			{
				callback(false, QJsonObject());
				return;
			}
			QString cipher = resultData["zone_recog_cipher"].toString();
			QByteArray ba = decrypt(cipher);
			QByteArray compressData;
			const bool gzip = doUnCompress(ba, compressData);
			if (!gzip)
			{
				callback(false, QJsonObject());
				return;
			}
			QString strData = QString::fromUtf8(compressData);
			QJsonParseError jsonError;
			QJsonDocument jsonDocument = QJsonDocument::fromJson(compressData, &jsonError);
			if (jsonError.error != QJsonParseError::NoError || !jsonDocument.isObject())
			{
				callback(false, QJsonObject());
				return;
			}
			callback(true, jsonDocument.object());
		}, [=](DownloadFailInfo errorInfo) {
			callback(false, QJsonObject());
		},arrangeData.timeout);
	}
}
