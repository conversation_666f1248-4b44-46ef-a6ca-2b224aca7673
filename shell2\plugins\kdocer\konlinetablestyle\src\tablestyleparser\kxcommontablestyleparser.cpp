﻿#include "stdafx.h"
#include <src/tablestyleparser/kxcommontablestyleparser.h>
#include <ooxml/mapping/ns/ns.h>
#include <package/openXmlServer.h>

using namespace mso::ooxml;

#define POUNDS 12700

KxDxf2StyleParser::KxDxf2StyleParser()
{
}

KxDxf2StyleParser::~KxDxf2StyleParser()
{
}

QList<KxCommonTableStyleInfo::PartStyleInfo> KxDxf2StyleParser::getStyleList() const
{
	return m_styleList;
}

bool KxDxf2StyleParser::parserElement(const QDomElement& element)
{
	//child element
	bool bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::dxf:
			return collectDxf(childElement);
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

void KxDxf2StyleParser::setDxfCustomColor(const int themeColorKey, const QString& customColor)
{
	m_themeColorKey = themeColorKey;
	m_CustomColor = customColor;
}

bool KxDxf2StyleParser::collectDxf(const QDomElement& element)
{
	KxCommonTableStyleInfo::PartStyleInfo info;
	m_styleList << info;
	KxCommonTableStyleInfo::PartStyleInfo& refInfo = m_styleList.last();

	return parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::font:
			return collectDXfFont(childElement, refInfo);
		case s::fill:
			return collectDXfFill(childElement, refInfo);
		case s::border:
			return collectDXfBorder(childElement, refInfo);
		}
		return true;
	});
}

bool KxDxf2StyleParser::collectDXfFont(const QDomElement& element, KxCommonTableStyleInfo::PartStyleInfo& styleInfo)
{
	QDomAttr valAttr;
	return parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::name:
		{
			if (fetchVal(childElement, valAttr))
			{
				styleInfo.fontInfo.name = valAttr.value();
			}
		}
		break;
		case s::b:
		{
			if (fetchVal(childElement, valAttr))
			{
				styleInfo.fontInfo.bBold = onOff(valAttr.value());
			}
			else {
				styleInfo.fontInfo.bBold = true;
			}
		}
		break;
		case s::i:
		{
			if (fetchVal(childElement, valAttr))
			{
				styleInfo.fontInfo.bItalic = onOff(valAttr.value());
			}
			else {
				styleInfo.fontInfo.bItalic = true;
			}
		}
		break;
		case s::color:
			fetchColor(childElement, styleInfo.fontInfo.fontColor);
			break;
		case s::sz:
		{
			if (fetchVal(childElement, valAttr))
			{
				styleInfo.fontInfo.size = valAttr.value().toUInt();
			}
		}
		break;
		case s::u:
		{
			if (fetchVal(childElement, valAttr))
			{
				if (valAttr.value() == "none")
					styleInfo.fontInfo.underLine = 0;
				else
					styleInfo.fontInfo.underLine = 1;
			}
			else {
				styleInfo.fontInfo.underLine = 0;
			}
		}
		break;
		}
		return true;
	});
}

bool KxDxf2StyleParser::collectDXfFill(const QDomElement& element, KxCommonTableStyleInfo::PartStyleInfo& styleInfo)
{
	bool bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::patternFill:
			return collectPatternFill(childElement, styleInfo.fillColor);
		case s::gradientFill:
			return collectGradientFill(childElement, styleInfo.fillColor);
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxDxf2StyleParser::collectPatternFill(const QDomElement& element, drawing::Fill& f)
{
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::patternType:
			setPatternType(attr.value(), f);
			break;
		}
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;
	//child
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::bgColor:
		{
			drawing::Color clr;
			fetchColor(childElement, clr);
			if (f->type() == drawing::FillTypePattern)
				f->setBackgroundColor(clr);
			else
				f->setColor(clr);
		}
		break;
		case s::fgColor:
		{
			if(f->type() != drawing::FillTypePattern)
				break;
			drawing::Color clr;
			fetchColor(childElement, clr);
			f->setColor(clr);
		}
		break;
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxDxf2StyleParser::collectGradientFill(const QDomElement& element, drawing::Fill& f)
{
	std::pair<bool, bool> prFlag;
	prFlag.first = prFlag.second = false;
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::degree:
			f.setLinearAngle(drawing::PositiveFixedAngle::fromDegree(attr.value().toDouble()));
			break;
		};
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;

	//child
	drawing::GradientStopList stops;
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::stop:
		{
			drawing::GradientStop gs;
			if (!fetchGradStop(childElement, gs))
				return false;
			stops.push_back(gs);
		}
		break;
		}
		return true;
	});
	f.setType(drawing::FillTypeGradient);
	f.setStops(stops);
	ASSERT(bRet);
	return bRet;
}

bool KxDxf2StyleParser::collectDXfBorder(const QDomElement& element, KxCommonTableStyleInfo::PartStyleInfo& styleInfo)
{
	//child
	bool bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		drawing::Outline line;
		KxCommonTableStyleInfo::BorderType borderType = KxCommonTableStyleInfo::BorderEnd;
		switch (xn)
		{
		case s::left:
			collectDXfBorderLine(childElement, line);
			borderType = KxCommonTableStyleInfo::Left;
			break;
		case s::right:
			collectDXfBorderLine(childElement, line);
			borderType = KxCommonTableStyleInfo::Right;
			break;
		case s::top:
			collectDXfBorderLine(childElement, line);
			borderType = KxCommonTableStyleInfo::Top;
			break;
		case s::bottom:
			collectDXfBorderLine(childElement, line);
			borderType = KxCommonTableStyleInfo::Bottom;
			break;
		case s::horizontal:
			collectDXfBorderLine(childElement, line);
			borderType = KxCommonTableStyleInfo::InsideH;
			break;
		case s::vertical:
			collectDXfBorderLine(childElement, line);
			borderType = KxCommonTableStyleInfo::InsideV;
			break;
		}

		if (line.isNull())
			line.setFill(drawing::Fill::No);

		if (borderType != KxCommonTableStyleInfo::BorderEnd)
			styleInfo.borderInfo[borderType] = line;

		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxDxf2StyleParser::collectDXfBorderLine(const QDomElement& element, drawing::Outline& line)
{
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::style:
			drawing::CompoundLine lineType = drawing::CompoundLineTriple;
			drawing::LineDashType lineDashType = drawing::LineDashTypeSolid;
			int lineWidth = 0;
			bool bOk = lineStyle2LineType(attr.value(), lineType, lineDashType, lineWidth);
			if (bOk)
			{
				line.setCompoundLine(lineType);
				line.setWidth(lineWidth);
				line.setDashType(lineDashType);
			}
			return bOk;
		};
		return true;
	});
	if (!bRet)
		return bRet;
	//child
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::color:
			drawing::Color clr;
			bool bOk = fetchColor(childElement, clr);
			if (bOk)
				line.setFill(drawing::Fill(clr));
			return bOk;
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxDxf2StyleParser::fetchVal(const QDomElement& element, QDomAttr& outAttr)
{
	if (!element.hasAttributes())
		return false;
	return parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::val:
		{
			outAttr = attr;
		}
		break;
		}
		return true;
	});
}

bool KxDxf2StyleParser::fetchColor(const QDomElement& element, drawing::Color& clr)
{
	return parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::rgb:
		case x14::rgb:
			clr.setRgb(hexLong(attr.value()) | 0xff000000);
			break;
		case s::_auto:
		case x14::_auto:
			if (clr.isEmpty())
				clr.setPreset(drawing::Color::PresetBlack);
			clr.setAutoColor(true);
			break;
		case s::theme:
		case x14::theme:
		{
			UINT thmColorIndex = attr.value().toUInt();
			if (thmColorIndex < drawing::Color::Background1 && thmColorIndex> drawing::Color::Light2)
			{
				if (thmColorIndex == drawing::Color::Accent1)	//特殊处理着色1，允许被替换颜色
				{
					if (m_themeColorKey >= 1 && m_themeColorKey <= 6)
					{
						clr.setScheme((drawing::Color::SchemeColor)(drawing::Color::Accent1 + m_themeColorKey - 1));//主题色1值为1，故进行-1定位
						break;
					}
					else if (m_themeColorKey == 7)	//替换颜色为 深色 1
					{
						clr.setScheme(drawing::Color::Dark1);
						break;
					}
					else if (!m_CustomColor.isEmpty())
					{
						clr.setRgb(hexLong(m_CustomColor.replace("#", "FF")) | 0xff000000);
						break;
					}
				}
				clr.setScheme((drawing::Color::SchemeColor)thmColorIndex);

			}
			else if (thmColorIndex < drawing::Color::Accent1 && thmColorIndex >= drawing::Color::Dark1)
				clr.setScheme((drawing::Color::SchemeColor)(thmColorIndex + drawing::Color::Background1));
		}
		break;
		case s::tint:
		case x14::tint:
			clr.setBrightness(attr.value().toDouble());
			break;
		default:
			if (attr.name() == "transparency")
			{
				int trans = 0;
				bool bOk = false;
				trans = attr.value().toInt(&bOk);
				if (!bOk)
					trans = 0;
				clr.setTransparency(trans / (qreal)100);
			}
			break;
		}
		return true;
	});
}

bool KxDxf2StyleParser::fetchGradStop(const QDomElement& element, drawing::GradientStop& gs)
{
	drawing::Color clr;
	double position = 0;

	//attr
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::position:
		{
			double dVal = attr.value().toDouble();
			if (dVal >= 0 && dVal <= 1)
			{
				position = dVal;
			}
			else
			{
				ASSERT(FALSE);
				return false;
			}
		}
		break;
		}
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;

	//child
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::color:
			fetchColor(childElement, clr);
			break;
		}
		return true;
	});
	gs = drawing::GradientStop(clr, position);

	ASSERT(bRet);
	return bRet;
}

bool KxDxf2StyleParser::lineStyle2LineType(const QString& lineType, drawing::CompoundLine& dashType, drawing::LineDashType& lineDashType, int& lineWidth)
{
	if (lineType == "none" || lineType.isEmpty())
		return false;

	lineWidth = POUNDS * 0.75;
	dashType = drawing::CompoundLineSingle;
	if (lineType == "double")
		dashType = drawing::CompoundLineDouble;
	if (lineType == "thick")
		lineWidth = 1.5 * POUNDS;
	else if (lineType.startsWith("medium"))
		lineWidth = POUNDS;

	lineDashType = drawing::LineDashTypeSolid;
	if (lineType == "hair")
		lineDashType = drawing::LineDashTypeSystemDot;
	else if (lineType == "dotted")
		lineDashType = drawing::LineDashTypeSystemDash;
	else if (lineType == "dashDotDot" || lineType == "mediumDashDotDot")
		lineDashType = drawing::LineDashTypeSystemDashDotDot;
	else if (lineType == "dashDot" || lineType == "mediumDashDot")
		lineDashType = drawing::LineDashTypeSystemDashDot;
	else if (lineType == "dashed" || lineType == "mediumDashed")
		lineDashType = drawing::LineDashTypeDash;
	return true;
}

void KxDxf2StyleParser::setPatternType(const QString& typeStr, drawing::Fill& fill)
{
	if (typeStr == "none")
		fill->setType(drawing::FillTypeNone);
	else if (typeStr == "solid")
		fill->setType(drawing::FillTypeSolidColor);
	else
	{
		fill->setType(drawing::FillType::FillTypePattern);
		static const QMap<QString, drawing::PresetPattern> s_str2PatternType= {
			{"mediumGray",drawing::PresetPattern50Percent},
			{"darkGray",drawing::PresetPattern75Percent},
			{"lightGray",drawing::PresetPattern25Percent},
			{"darkHorizontal",drawing::PresetPatternDarkHorizontal},
			{"darkVertical",drawing::PresetPatternDarkVertical},
			{"darkDown",drawing::PresetPatternDarkDownwardDiagonal},
			{"darkUp",drawing::PresetPatternDarkUpwardDiagonal},
			{"darkGrid",drawing::PresetPatternSmallCheckerBoard},
			{"darkTrellis",drawing::PresetPatternTrellis},
			{"lightHorizontal",drawing::PresetPatternHorizontal},
			{"lightVertical",drawing::PresetPatternVertical},
			{"lightDown",drawing::PresetPatternDownwardDiagnal},
			{"lightUp",drawing::PresetPatternUpwardDiagonal},
			{"lightGrid",drawing::PresetPatternSmallGrid},
			{"lightTrellis",drawing::PresetPatternDiagonalCross},
			{"gray125",drawing::PresetPattern10Percent},
			{"gray0625",drawing::PresetPattern05Percent},
		};
		drawing::PresetPattern pattType = drawing::PresetPatternHorizontal;
		if (s_str2PatternType.contains(typeStr))
			pattType = s_str2PatternType[typeStr];
		fill->setPattern(pattType);
	}
}

KxCommonTableStyleParser::KxCommonTableStyleParser()
{
}

KxCommonTableStyleInfo::StyleInfo KxCommonTableStyleParser::getTableStyle() const
{
	return m_tblStyleInfo;
}

QList<KxCommonTableStyleInfo::PartStyleInfo> KxCommonTableStyleParser::getStyleList() const
{
	return m_dxfParser.getStyleList();
}

QList<KxCommonTableStyleInfo::PartStyleType> KxCommonTableStyleParser::getStyleOptionList() const
{
	return m_styleOptionList;
}

void KxCommonTableStyleParser::setCustomColor(const int themeColorKey, const QString& customColor)
{
	m_themeColorKey = themeColorKey;
	m_CustomColor = customColor;
	m_dxfParser.setDxfCustomColor(themeColorKey, customColor);
}

bool KxCommonTableStyleParser::parserElement(const QDomElement& element)
{
	//child element
	return parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::dxfs:
			return m_dxfParser.parserElement(childElement);//collectDxfs(element);
		case s::tableStyle:
			m_tblStyleInfo = KxCommonTableStyleInfo::StyleInfo();
			return collectTableStyle(childElement);
		default:
			return collectTableStyleOption(childElement);
		}
		return true;
	});
}

bool KxCommonTableStyleParser::collectTableStyle(const QDomElement& element)
{
	QString name;
	QString guid;
	//attr
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::name:
			name = attr.value();
			break;
		case xr9::uid:
			guid = attr.value();
			guid = QUuid::createUuidV5(guid, QString::number(m_themeColorKey) % m_CustomColor).toString();
			break;
		}
		return true;
	});

	ASSERT(bRet);
	if (!bRet || (name.isEmpty() && guid.isEmpty()))
		return false;
	if (guid.isEmpty())
		guid = QUuid::createUuid().toString();
	m_tblStyleInfo.name = name;
	m_tblStyleInfo.guid = guid;

	//child element
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::tableStyleElement:
			return CollectTableStyleElement(childElement);
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxCommonTableStyleParser::CollectTableStyleElement(const QDomElement& element)
{
	INT nDxfID = -1;
	INT nSize = -1;
	KxCommonTableStyleInfo::PartStyleType styleType = KxCommonTableStyleInfo::StyleEND;

	//attr
	parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::dxfId:
			nDxfID = attr.value().toUInt();
			break;
		case s::size:
			nSize = attr.value().toUInt();
			break;
		case s::type:
			styleType = etTableType2StyleType(attr.value());
			break;
		}
		return true;
	});

	if (styleType < KxCommonTableStyleInfo::WholeTable || styleType >= KxCommonTableStyleInfo::StyleEND)
		return true;

	m_tblStyleInfo.partStyleIndexMap[styleType] = nDxfID;
	return true;
}

bool KxCommonTableStyleParser::collectTableStyleOption(const QDomElement& element)
{
	if (!m_styleOptionList.isEmpty())
		return true;
	parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		if (attr.value() == "0")
			return true;
		switch (xn)
		{
		case s::showFirstColumn:
			m_styleOptionList << KxCommonTableStyleInfo::FirstCol;
			break;
		case s::showLastColumn:
			m_styleOptionList << KxCommonTableStyleInfo::LastCol;
			break;
		case s::showRowStripes:
			m_styleOptionList << KxCommonTableStyleInfo::Band1H;
			break;
		case s::showColumnStripes:
			m_styleOptionList << KxCommonTableStyleInfo::Band1V;
			break;
		case s::headerRowCount:
			m_styleOptionList << KxCommonTableStyleInfo::FirstRow;
			break;
		case s::totalsRowCount:
			m_styleOptionList << KxCommonTableStyleInfo::LastRow;
			break;
		}
		return true;
	});
	return true;
}

KxCommonTableStyleInfo::PartStyleType KxCommonTableStyleParser::etTableType2StyleType(const QString& styleName)
{
	KxCommonTableStyleInfo::PartStyleType styleType = KxCommonTableStyleInfo::StyleEND;
	if (styleName == "wholeTable")
		styleType = KxCommonTableStyleInfo::WholeTable;
	else if (styleName == "firstColumnStripe")
		styleType = KxCommonTableStyleInfo::Band1V;
	else if (styleName == "secondColumnStripe")
		styleType = KxCommonTableStyleInfo::Band2V;
	else if (styleName == "firstRowStripe")
		styleType = KxCommonTableStyleInfo::Band1H;
	else if (styleName == "secondRowStripe")
		styleType = KxCommonTableStyleInfo::Band2H;
	else if (styleName == "lastColumn")
		styleType = KxCommonTableStyleInfo::LastCol;
	else if (styleName == "firstColumn")
		styleType = KxCommonTableStyleInfo::FirstCol;
	else if (styleName == "headerRow")
		styleType = KxCommonTableStyleInfo::FirstRow;
	else if (styleName == "totalRow")
		styleType = KxCommonTableStyleInfo::LastRow;
	else if (styleName == "firstHeaderCell")
		styleType = KxCommonTableStyleInfo::TLCELL;
	else if (styleName == "lastHeaderCell")
		styleType = KxCommonTableStyleInfo::TRCELL;
	else if (styleName == "firstTotalCell")
		styleType = KxCommonTableStyleInfo::BLCELL;
	else if (styleName == "lastTotalCell")
		styleType = KxCommonTableStyleInfo::BRCELL;
	return styleType;
}