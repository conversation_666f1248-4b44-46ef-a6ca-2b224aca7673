﻿#pragma once
#include "kxwpstablebeautifyhelper.h"

class KEmphasizeHandler
{
public:
	KEmphasizeHandler();
	virtual ~KEmphasizeHandler();

	virtual bool emphasizeTable(const KEmphasizeInfo& emInfo, KEmphasizeIntersectCellInfo& cellInfo,
		KEmphasizeStyleInfo& targetStyle);

protected:
	virtual KEmphasizeStyleInfo getTableStyleToApply(const KEmphasizeStyleInfo& originInfo);
	virtual bool _emphasizeTable(const KEmphasizeStyleInfo& info);
	bool _emphasizeTableNew(const KEmphasizeStyleInfo& info);

	void setBorderStyle(wpsoldapi::Borders* borders, const KBorderStyleInfo& info);
	bool emphasizeFill(wpsoldapi::Selection* selection, const KEmphasizeStyleInfo& info);
	void setCellsFillColor(wpsoldapi::Shading* cellsShading, const KEmphasizeStyleInfo& info);
	bool emphasizeBorders(wpsoldapi::Selection* selection, const KEmphasizeStyleInfo& info);
	void setBordersStyle(wpsoldapi::Borders* borders, const KEmphasizeStyleInfo& info);
};

class KEmphasizeBordersHanlder :virtual public KEmphasizeHandler
{
public:
	KEmphasizeBordersHanlder();
	~KEmphasizeBordersHanlder();

protected:
	KEmphasizeStyleInfo getTableStyleToApply(const KEmphasizeStyleInfo& originInfo) override;
	bool _emphasizeTable(const KEmphasizeStyleInfo& info) override;
};

class KEmphasizeBtmBorderHanlder :public KEmphasizeHandler
{
public:
	KEmphasizeBtmBorderHanlder();
	~KEmphasizeBtmBorderHanlder();

protected:
	KEmphasizeStyleInfo getTableStyleToApply(const KEmphasizeStyleInfo& originInfo) override;
	bool _emphasizeTable(const KEmphasizeStyleInfo& info) override;

private:
	bool emphasizeBorder(wpsoldapi::Selection* selection, const KEmphasizeStyleInfo& info);
};

class KEmphasizeFillHanlder :virtual public KEmphasizeHandler
{
public:
	KEmphasizeFillHanlder();
	~KEmphasizeFillHanlder();

protected:
	KEmphasizeStyleInfo getTableStyleToApply(const KEmphasizeStyleInfo& originInfo) override;
	bool _emphasizeTable(const KEmphasizeStyleInfo& info) override;
};

class KEmphasizeNoneHandler :public KEmphasizeBordersHanlder, public KEmphasizeFillHanlder
{
public:
	KEmphasizeNoneHandler(bool bHor);
	~KEmphasizeNoneHandler();

	bool emphasizeTable(const KEmphasizeInfo& emInfo, KEmphasizeIntersectCellInfo& cellInfo,
		KEmphasizeStyleInfo& targetStyle) override;

protected:
	KEmphasizeStyleInfo getTableStyleToApply(const KEmphasizeStyleInfo& originInfo) override;
	bool _emphasizeTable(const KEmphasizeStyleInfo& info) override;

private:
	bool _emphasizeColHeaderCell(const KEmphasizeInfo& emInfo);
	bool m_bHor;
};

class KTableEmphasizer
{
public:
	KTableEmphasizer();
	virtual ~KTableEmphasizer();

	void setEmphasizeInfo(const KTableEmphasizeType type, const int num);
	const KEmphasizeInfo& getEmphasizeInfo();

	virtual bool emphasizeTable(const KEmphasizeIntersectCellInfo& intersectCellInfo);
	virtual bool isEmphasizeValid();
	KEmphasizeIntersectCellInfo getIntersectCellInfo();

protected:
	virtual bool selectRowOrCol(int index);
	virtual bool selectCell();
	virtual bool getSelectCellIndex(int& index);
	virtual void createHandler(const KTableEmphasizeType type);
	virtual bool createEmphasizeStr(const QString& origin, QString& strInfo, const bool bClear);
	virtual bool getOriginStyleInfo();
	virtual bool getIntersectCellStyleInfo();
	virtual void updateIntersectCellStyleInfo(const KEmphasizeStyleInfo& styleInfo);

	void addHandler(KEmphasizeHandler* handler);
	bool getCurrentUsedStyleInfo(KEmphasizeStyleInfo& styleInfo);
	void getSelectCellInfo();

private:
	void resetHandler();
	void collapseSelection();
	void setEmphaiszeInfo(const bool bClear);
	bool updateRowColInfo();

protected:
	int m_emphasizeNum;
	KTableEmphasizeType m_emphasizeType;
	KEmphasizeInfo m_emInfo;
	KEmphasizeIntersectCellInfo m_intersectCellInfo;
	QList<KEmphasizeHandler*> m_handlers;
	int m_rowCnt;
	int m_colCnt;
	int m_selectedRow;
	int m_selectedCol;
};

class KTableRowEmphasizer :public KTableEmphasizer
{
public:
	KTableRowEmphasizer();
	~KTableRowEmphasizer();

	bool isEmphasizeValid() override;
	bool getIntersectCellStyleInfo() override;
	void updateIntersectCellStyleInfo(const KEmphasizeStyleInfo& styleInfo) override;

protected:
	bool selectRowOrCol(int index) override;
	bool selectCell() override;
	bool getSelectCellIndex(int& index) override;
	void createHandler(const KTableEmphasizeType type) override;
	bool createEmphasizeStr(const QString& origin, QString& strInfo, const bool bClear) override;

private:
	bool selectRow(const int index);
};

class KTableColEmphasizer :public KTableEmphasizer
{
public:
	KTableColEmphasizer();
	~KTableColEmphasizer();

	bool isEmphasizeValid() override;
	bool getIntersectCellStyleInfo() override;
	void updateIntersectCellStyleInfo(const KEmphasizeStyleInfo& styleInfo) override;

protected:
	bool selectRowOrCol(int index) override;
	bool selectCell() override;
	bool getSelectCellIndex(int& index) override;
	
	void createHandler(const KTableEmphasizeType type) override;
	bool createEmphasizeStr(const QString& origin, QString& strInfo, const bool bClear) override;
	bool getOriginStyleInfo() override;

private:
	bool selectCol(const int index);
	bool getHeaderCellStyleInfo(KEmphasizeStyleInfo& styleInfo);
};

class KTableSelectionNotify
	: public QObject
	, public IKCoreNotifyFilter
{
	Q_OBJECT
public:
	KTableSelectionNotify(QObject* parent = 0);
	~KTableSelectionNotify();
	void registerNotifyFilter();
	void unRegisterNotifyFilter();
	STDPROC_(BOOL) OnFilterNotify(ksoNotify* ne) override;

public:
	void init();

signals:
	void sigSelectionChanged(bool bForce);

private slots:
	void onCoreInited();
	void onDestroyed();

private:
	void connectCoreSignals();

protected:
	bool m_bInited;
	bool m_bRegistered;
};