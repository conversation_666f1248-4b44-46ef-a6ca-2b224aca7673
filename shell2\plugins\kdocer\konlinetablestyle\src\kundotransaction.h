#pragma once

#include "kxetcommonheander.h"

//Copy From office\et\include\applogic\et_applogic_helper.inl

class KUndoTransaction
{
private:
	KCOMPTR(IKEtApplication)	m_ptrEtApp;
	KCOMPTR(IKTransactionTool)	m_ptrUndoTrans;
	ks_wstring					m_Desc;
	BOOL						m_bNeedCommit;
	BOOL						m_bClearCopy;
	BOOL						m_bEndtrans;
	KCOMPTR(etoldapi::_Workbook)	m_ptrWorkbook;

public:
	KUndoTransaction(etoldapi::_Workbook*, PCWSTR, BOOL bClearCopy);
	~KUndoTransaction();
	void	CancelTrans(HRESULT hr, BOOL bReportError, BOOL bClearCopy);
	void	EndTrans();
	etoldapi::_Workbook*	GetEntry();
	void	PauseMacroRecording() {}
	void	ResumeMacroRecording() {}
	void	AddTransCommand(interface IKTransCommand*);
	void    KeepCopyMode() {m_bClearCopy = FALSE;}
protected:
	void	Init(IKWorkbook*);
	void	ReportError(HRESULT);
};

#undef APP_BeginUndoTrans
#define APP_BeginUndoTrans(entry, bClearCopyState, desc)	KUndoTransaction xTrans((entry), (desc), (bClearCopyState)); 

#undef APP_EndUndoTRANS
#define APP_EndUndoTRANS(hr, bReportError, bClearCopyState)	if (FAILED(hr))													\
{ xTrans.CancelTrans((hr), (bReportError), (bClearCopyState)); } \
{																				\
	xTrans.EndTrans();															\
	app_helper::KViewBatchUpdate _Update(xTrans.GetEntry(), ERM_SheetChanged);	\
}																				