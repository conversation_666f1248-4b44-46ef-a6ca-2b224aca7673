#pragma once

#include <kcomctl/kcommand.h>
#include <src/kxtablestylefeaturecommand.h>

namespace KTableStyleInfoCollect {
	struct KTableReportInfo;
}

class KInsertOnlineTableStyleCommand : public KxGalleryCommand {
	Q_OBJECT
public:
	KInsertOnlineTableStyleCommand(KxMainWindow* host, QObject* parent);
	~KInsertOnlineTableStyleCommand();

	virtual QWidget* createExtendedWidget(QWidget* parent);

	virtual KCommand* clone(QObject* host, QObject* parent) override;

	STDPROC Get(KSO_Group, KSO_DataID id, void*, void* val);
	OperatorErrorCode InsertTable();
public slots:
	virtual void onColorChange(const drawing::Color& color);
	virtual void onPreviewBegin(const drawing::Color& color);
	virtual void onPreviewEnd();

	void onMoreColorClicked();
	void onSnapColorClicked();

	void onDownloadError(const KOTSResourceInfo& resourceInfo);
	void onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);

protected:
	void addRecentColor(const QColor& color);
	KTableStyleInfoCollect::KTableReportInfo getReportInfo(bool bCustomColor);

signals:
	void colorChange(drawing::Color);
	void sigInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);

private:
	QPointer<KxTableStyleFeatureCommand> m_applyStyleCmd;
	QPointer<KGalleryAbstractModel> m_model;
	QPointer<KxListCommand> m_listCommand;
	QPointer<KCefPopupMenuButton> m_menuButton;
};

///////////////////////////////////////////////////////////////////
class KInsertWppOnlineTableStyleCommand : public KInsertOnlineTableStyleCommand{
	Q_OBJECT
public:
	KInsertWppOnlineTableStyleCommand(KxMainWindow* host, QObject* parent);
};