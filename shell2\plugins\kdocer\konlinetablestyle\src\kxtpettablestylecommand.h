﻿#pragma once

#include "kxonlinetablestyledefine.h"
#include "kxtptablestylecommand.h"
#include <etx_exec.h>
#include <etx_core.h>
#include <src/tablestyleparser/kxettablestyleparser.h>
#include <event/et_event_itf.h>
#include <kso/framework/coreobj.h>
#include <etx_event.h>
#include <applogic/et_applogic_infos.h>
#include <legacy/async/kpromise.h>

class KArrangeProcessAssistant;
class KTransPreview;

class KxTpETOnlineTableStyleCommand :public KxDocerTpTableStyleCommand, public IKCoreNotifyFilter, public IWatchedRegionNotification, public IEventSink
{
	Q_OBJECT
public:
	KxTpETOnlineTableStyleCommand(KxMainWindow* host, QObject* parent);
	~KxTpETOnlineTableStyleCommand();

	void update() override;

	virtual void updateCurtableTableIndex() override;

	virtual OperatorErrorCode applyTableStyleDocument(const KOTSResourceInfo& resourceInfo, const QByteArray& content, bool hover) override;

	virtual OperatorErrorCode applyTableByTableInfo(const TableInfo& tableInfo) override;

	OperatorErrorCode newAndApplyTableStyleByCmd(const QString& tableName, KxtableStyleParser& parser, TableInfo& tableInfo);
	OperatorErrorCode newAndApplyTableStyle(const QString& tableName, KxtableStyleParser& parser, bool bApplyAll, bool& bHasDataTable, const QList<QRect> normalTableMatchResult);

	virtual PanelEnableStatus judgeEnableInsertTable() override;

	void tableArrangeReco(TableAnalyzeFrom from, RecoType recoType, const std::function<void(bool)>& callback = std::function<void(bool)>(), int worksheetIdx = -1);
	bool tableArrangeRecoByCustom(RecoType recoType, QJsonObject& obj, quint64 cellCnt);

	void clearArrangeInfo();

	void onSheetChange();
	void updateRegisterRange(IKWorksheet* pWorkSheet = nullptr);

	bool checkRangeNeedUpdate();

	virtual HRESULT OnEvent(IN ET_Event_ID ID, IN KSO_WParam wParam, IN KSO_LParam lParam) override;
protected:
	STDPROC_(BOOL) OnFilterNotify(ksoNotify* notify);
	STDPROC_(void) OnUpdate(int msg, const REGION_OPERATION_PARAM*) override;
	QRect getCurrentSelectArea();

	void updateDataTableRect();
	bool isDataTableChanged();
	QList<int> getCurrentMatchNormalTableIdx(bool bApplyAll, bool bArrange, QList<QPair<QRect, bool>>* outMatchTbl = nullptr);
	void changeHoverStatus(bool bHover);
public slots:
	void updateSheetView();
	void applyTableArrange(const QString& applyMode, bool bApplyAll);
	void tableRecognize(TableAnalyzeFrom from);
	virtual void onLeaveHoverPreview() override;
	void cancelAnalyze();
	void getHasMultiChildTable(bool bApplyAll);
	void getHasMultiChildTableResult(bool bApplyAll, bool& bAnalyzed, bool& bHasMultiChildTable);

	void doScheduleTasks();
	void doBatchTasks(int curIdx = 1);
	virtual void applyTableStyle(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, const QString& filePath, bool hover) override;
	void singleApplyTableStyle(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, const QString& filePath, bool hover);
	bool canSheetApply(int curIdx);
	bool isBatchApply();
	void getHasTableResult(bool bApplyAll, bool& bHasNormalTable, bool& bHasTableApply);
private:
	static bool __stdcall identifyTableContinueProc();
	static bool s_identifyTableContinue;

	TableArrangeResult doApplyArrange(const QString& applyMode, bool bApplyAll);
signals:
	void sigGridLineStatuesChange(bool bChecked);
	void sigIndentifyStatus(TableRecoStaus status, TableAnalyzeFrom from);
	void sigArrangeApplyResult(bool bSuccess, TableArrangeResult result, qint64 totalTimes, TableApplyInfo tableApplyInfo);
	void sigHasMultiChildTableResult(bool bSuccess, bool bHasMultiTable);
private:
	bool m_bGridLineChecked = false;
	bool m_bNeedUpdateTable = true;
	bool m_bAnalyzing = false;
	bool m_bApplying = false;
	bool m_bCancelArrange = false;
	bool m_bArranging = false;
	bool m_bApplingArrage = false;
	bool m_bApplingArrangeStyle = false;
	TableAnalyzeFrom m_curAnalyzeFrom = TableAnalyzeFrom::FromBegin;
	QList<QPair<QRect, bool>> m_normalTableList;
	QList<QRect> m_dataTableList;
	QList<QRect> m_pivotTableList;
	QList<docer::base::AnsycTask> m_scheduleList;
	QList<docer::base::AnsycTask> m_batchList;
	QList<docer::base::AnsycTask> m_arrangeScheduleList;
	KOTSResourceInfo m_lastApplyRes;
	ks_stdptr<KArrangeProcessAssistant> m_arrangeAssistant;
	ks_stdptr<IKWorksheet> m_spWorksheet;
	ks_stdptr<IBook> m_spBook;
	HANDLE m_hWatchedRegion = nullptr;
	int m_worksheetCount = 0;
	bool m_bHover = false;
};