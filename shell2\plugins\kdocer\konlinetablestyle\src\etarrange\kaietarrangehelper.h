#pragma once

namespace KAIEtArrangeHelper{

	struct EtArrangeData
	{
		quint64 cellCnt = 0;
		quint64 totalCnt = 0;
		quint64 ts = 0;
		quint64 timeout = 0;
		QString requestId;
		QJsonObject postData;
	};

	QString encrypt(const QByteArray& text);
	QByteArray decrypt(const QString& text);

	bool doCompress(const QByteArray& src, QByteArray& dest);
	bool doUnCompress(const QByteArray& src, QByteArray& dest);

	std::string CalcHmacSHA256(
		const char* key, size_t key_size,
		const char* request, size_t size);
	const std::string generateSign(quint64 ts, quint64 cellCount, const QString& reqId);

	QString generateRequestId(const QString& key);

	void addArrangeCommonHeader(QVariantMap& header);

	void getArrangePostData(const QJsonObject& tableObj, std::function<void(bool, bool, const EtArrangeData&)> callback);
	void requestTableRecognize(const EtArrangeData& arrangeData, std::function<void(bool, const QJsonObject&)> callback);
}