#pragma once

#include <src/kxonlinetablestyledefine.h>

namespace kdocerresnetwork {
	struct DownloadFailInfo;
	struct CommonArgs;
}

class KDocerOpBtnConfig;
class KOnlineTableResManager : QObject
{
public:
	static KOnlineTableResManager& getInstance();

	void downloadResFile(const TableDownloadReportInfo& downloadInfo, bool bApply, std::function<void(DownloadResult result, const QString& path)> callback);

	void initStyleConfig();
	KDocerButtonStyle getButtonStyle(const QStringList& privileges = QStringList());

	QList<KTableStyleResourceInfo> getResourceInfo(const QString& cacheKey);
	void setResourceInfo(const QString& cacheKey, const QList<KTableStyleResourceInfo>& resourceList);
	QString getPayCsourcePrefix();
private:
	KOnlineTableResManager();
	~KOnlineTableResManager();

	void onDownloadFailed(const QString& id, kdocerresnetwork::DownloadFailInfo info);
	void onDownloadSuccess(const QString& resId, const QString& filePath);
private:
	QMap<QString, QList<std::function<void(DownloadResult, const QString&)>>> m_downloadCallbacks;
	KDocerOpBtnConfig* m_btnConfig = nullptr;
	QMap<QString, QList<KTableStyleResourceInfo>> m_resourceMap;
};