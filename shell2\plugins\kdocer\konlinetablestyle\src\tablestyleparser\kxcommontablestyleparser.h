﻿#pragma once

#include <src/tablestyleparser/kxtablestyleparserbase.h>
#include <kcomctl/kcolorcombobox.h>
#include <office/drawing/model/fill.h>
#include <office/drawing/model/line.h>

namespace KxCommonTableStyleInfo {
	const static int PartStyleSize = 9;
	enum PartStyleType : int
	{
		WholeTable = 0,
		FirstRow,
		LastRow,
		FirstCol,
		LastCol,
		Band1H,
		Band2H,
		Band1V,
		Band2V,
		TLCELL,
		TRCELL,
		BLCELL,
		BRCELL,
		StyleEND
	};
	enum BorderType : int
	{
		Left,
		Right,
		Top,
		Bottom,
		InsideH,
		InsideV,
		BorderEnd
	};
	struct StyleFontInfo
	{
		bool bBold = false;
		bool bItalic = false;
		UINT size = 0;
		UINT underLine = 0;
		drawing::Color fontColor;
		QString name;
	};

	struct PartStyleInfo
	{
		drawing::Fill fillColor;
		StyleFontInfo fontInfo;
		QMap<BorderType, drawing::Outline> borderInfo;
		PartStyleInfo()
		{}
	};

	struct StyleInfo
	{
		QString guid;
		QString name;
		QMap<PartStyleType, int> partStyleIndexMap;
		StyleInfo()
		{
		}
	};
};

class KxDxf2StyleParser : public KxlslParser
{
public:
	KxDxf2StyleParser();
	~KxDxf2StyleParser();

	QList<KxCommonTableStyleInfo::PartStyleInfo> getStyleList() const;
	virtual bool parserElement(const QDomElement& element) override;
	
	void setDxfCustomColor(const int themeColorKey, const QString& customColor);
private:
	bool collectDxf(const QDomElement& element);
	bool collectDXfFont(const QDomElement& element, KxCommonTableStyleInfo::PartStyleInfo& styleInfo);
	bool collectDXfFill(const QDomElement& element, KxCommonTableStyleInfo::PartStyleInfo& styleInfo);
	bool collectPatternFill(const QDomElement& element, drawing::Fill& f);
	bool collectGradientFill(const QDomElement& element, drawing::Fill& f);
	bool collectDXfBorder(const QDomElement& element, KxCommonTableStyleInfo::PartStyleInfo& styleInfo);
	bool collectDXfBorderLine(const QDomElement& element, drawing::Outline& line);

	bool fetchVal(const QDomElement& element, QDomAttr& outAttr);
	bool fetchColor(const QDomElement& element, drawing::Color& clr);
	bool fetchGradStop(const QDomElement& element, drawing::GradientStop& gs);

	bool lineStyle2LineType(const QString& lineType, drawing::CompoundLine& dashType, drawing::LineDashType& lineDashType, int& lineWidth);
	void setPatternType(const QString& typeStr, drawing::Fill& fill);
private:
	QList<KxCommonTableStyleInfo::PartStyleInfo> m_styleList;
	int m_themeColorKey = 0;
	QString m_CustomColor;
};

class KxCommonTableStyleParser : public KxlslParser
{
public:
	KxCommonTableStyleParser();

	KxCommonTableStyleInfo::StyleInfo getTableStyle() const;
	QList<KxCommonTableStyleInfo::PartStyleInfo> getStyleList() const;
	QList<KxCommonTableStyleInfo::PartStyleType> getStyleOptionList() const;

	void setCustomColor(const int themeColorKey, const QString& customColor);
private:
	bool parserElement(const QDomElement& element) override;

	bool collectTableStyle(const QDomElement& element);
	bool CollectTableStyleElement(const QDomElement& element);
	bool collectTableStyleOption(const QDomElement& element);

	KxCommonTableStyleInfo::PartStyleType etTableType2StyleType(const QString& styleType);
	
private:
	KxCommonTableStyleInfo::StyleInfo m_tblStyleInfo;
	KxDxf2StyleParser m_dxfParser;
	QList<KxCommonTableStyleInfo::PartStyleType> m_styleOptionList;
	int m_themeColorKey = 0;
	QString m_CustomColor;
};
