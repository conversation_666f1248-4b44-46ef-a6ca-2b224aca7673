﻿#include <stdafx.h>
#include "konlinetableresmgr.h"
#include <kdocerfunctional.h>
#include <kdocerresnetwork/kdocerresnetwork.h>
#include <src/kxonlinereshelper.h>
#include <ksolite/ksolog/kxloggerlite.h>
#include <utilities/path/module/kcurrentmod.h>
#include <common/request/network/knetworkrequest.h>
#include <kdocertoolkitlite/account/kdoceraccount.h>
#include "kdocertoolkitlite/ui/btnconfig/kdocerbtnconfig.h"
#include <ksolite/kdocer/kdocerresnetwork/kdocernetworkwrapper.h>

KOnlineTableResManager& KOnlineTableResManager::getInstance()
{
	static KOnlineTableResManager inst;
	return inst;
}

void KOnlineTableResManager::downloadResFile(const TableDownloadReportInfo& downloadInfo, bool bApply, std::function<void(DownloadResult result, const QString& path)> callback)
{
	QString filePath = KxOnlineTableResHelper::getFileSavePath(downloadInfo.id, downloadInfo.md5);
	if (!bApply && QFileInfo(filePath).exists())
	{
		QTimer::singleShot(0,
			bindContext(this, [=]() {
				callback(DownloadResult::Success, filePath);
			}));
		return;
	}
	QString callbackTaskId = QString("%1_%2").arg(downloadInfo.id).arg(bApply ? "1" : "0");
	m_downloadCallbacks[callbackTaskId] << callback;
	if (m_downloadCallbacks[callbackTaskId].size() > 1)
		return;
	kdocerresnetwork::DownloadArgs args;
	args.saveArgs.saveFilePath = filePath;
	args.resourceKey = "tablestyle_scheme";
	args.resourceArgs.downloadKey = downloadInfo.downloadKey;
	args.resourceArgs.id = downloadInfo.id;
	args.resourceArgs.bServerAuth = bApply;
	args.commonArgs.client_type = downloadInfo.clientType;
	args.commonArgs.channel = downloadInfo.channel;
	args.commonArgs.sub_channel = downloadInfo.subChannel;
	args.commonArgs.component = downloadInfo.component;
	args.commonArgs.tokenDownload = bApply ? DRT_SDKOrderV2 : DRT_Token;
	QVariantMap extra;
	extra["app_type"] = 1;
	args.resourceArgs.extra = extra;
	docer::download(args,
		bindContext(this, [=](kdocerresnetwork::DownloadSuccessInfo info) {
			onDownloadSuccess(callbackTaskId, info.path);
		}),
		bindContext(this, [=](kdocerresnetwork::DownloadFailInfo errorInfo) {
			onDownloadFailed(callbackTaskId, errorInfo);
		}));
}

void KOnlineTableResManager::initStyleConfig()
{
	if (m_btnConfig)
		return;

	m_btnConfig = new KDocerOpBtnConfig(this);
	m_btnConfig->setScene("editorScene");
	m_btnConfig->init();
}

KDocerButtonStyle KOnlineTableResManager::getButtonStyle(const QStringList& privileges)
{
	auto fromOpConfig = [](const docer::OpBtnStyle& opStyle)
	{
		docer::OpBtnColorStyle opColorStyle = opStyle.getColorStyle();

		KDocerButtonStyle style;
		style.icon = opStyle.icon;
		style.text = opStyle.title;
		style.skuKey = opStyle.sku_key;
		style.textColor = opColorStyle.font_color;
		style.textColorHover = opColorStyle.font_color_hover;
		style.textColorClick = opColorStyle.font_color_click;
		style.backgroundColor = opColorStyle.btn_color;
		style.backgroundColorHover = opColorStyle.btn_color_hover;
		style.backgroundColorClick = opColorStyle.btn_color_click;
		return style;
	};

	if (privileges.isEmpty())
	{
		bool pass = KDocerAccount::isLogined();
		docer::OpBtnConfig config = m_btnConfig->getConfig();
		return fromOpConfig(pass ? config.btnFree.pass : config.btnFree.fail);
	}

	bool pass = KDocerAccount::hasPrivilege(privileges);
	docer::OpBtnConfig config = m_btnConfig->getConfig();
	return fromOpConfig(pass ? config.btnMember.pass : config.btnMember.fail);
}

QList<KTableStyleResourceInfo> KOnlineTableResManager::getResourceInfo(const QString& cacheKey)
{
	if (!m_resourceMap.contains(cacheKey))
		return QList<KTableStyleResourceInfo>();
	else
		return m_resourceMap[cacheKey];
}

void KOnlineTableResManager::setResourceInfo(const QString& cacheKey, const QList<KTableStyleResourceInfo>& resourceList)
{
	m_resourceMap[cacheKey] = resourceList;
}

QString KOnlineTableResManager::getPayCsourcePrefix()
{
#ifdef Q_OS_MACOS
	return "mac_docer_";
#elif defined(Q_OS_OHOS)
	return "harmonywin_docer_";
#else
	return "pc_docer_";
#endif
}

KOnlineTableResManager::KOnlineTableResManager()
{
}

KOnlineTableResManager::~KOnlineTableResManager()
{
	if (m_btnConfig)
	{
		m_btnConfig->deleteLater();
		m_btnConfig = nullptr;
	}
}

void KOnlineTableResManager::onDownloadFailed(const QString& id, kdocerresnetwork::DownloadFailInfo info)
{
	KxLoggerLite::writeWarning(docer::base::KCurrentModule::getCurrentModuleName().toStdWString(),
		QString("KOnlineTableResManager download failed resId:%1 %2")
		.arg(id)
		.arg(info.toString())
		.toStdWString());
	auto callbackList = m_downloadCallbacks[id];
	m_downloadCallbacks.remove(id);
	DownloadResult errorCode = DownloadResult::OtherError;
	QJsonObject response = JsonHelper::convertByteArrayToQJson(info.content);
	int secondLevelCode = -1;
	int thirdLevelCode = -1;
	if (response.contains("code"))
	{
		QString code = QString::number(response["code"].toInt());
		secondLevelCode = code.mid(3, 2).toInt();
		thirdLevelCode = code.mid(5).toInt();
	}
	switch (info.httpStatusCode)
	{
	case 400:
		if (secondLevelCode == 0)
			errorCode = DownloadResult::ParamsError;
		else if (secondLevelCode == 1)
			errorCode = DownloadResult::RequestError;
		else
			errorCode = DownloadResult::OtherError;
		break;
	case 403:
		if (secondLevelCode == 2)
		{
			if (thirdLevelCode != 100)
				errorCode = DownloadResult::PrivilegeError;
			else
				errorCode = DownloadResult::NoLogin;
		}
		else if (secondLevelCode == 3)
			errorCode = DownloadResult::ResourceStatusError;
		break;
	case 500:
		errorCode = DownloadResult::ServerError;
		break;
	}
	for (const auto& callback : callbackList)
		callback(errorCode, QString());
}

void KOnlineTableResManager::onDownloadSuccess(const QString& resId, const QString& filePath)
{
	auto callbackList = m_downloadCallbacks[resId];
	m_downloadCallbacks.remove(resId);
	for (const auto& callback : callbackList)
		callback(DownloadResult::Success, filePath);
}
