#include "stdafx.h"
#include <kso/framework/api/wppapi_old.h>
#ifdef Q_OS_WIN
#include <kso/framework/api/wppapi_old.c>
#endif
#include "kxwpptablestylerecommendhelper.h"
#include "common/request/network/knetworkrequest.h"
#include "kxonlinereshelper.h"

namespace
{
	const QString strMemberIdentity = "menber_identity";
	const QString strUploadResult = "upload_result";
	const QString strErrorCode = "error_code";
	const QString strDcUploadKey = "docer_onlinepic_upload";
};

bool KxWppTableStyleRecommendHelper::m_appExited = false;
KxWppTableStyleRecommendHelper* KxWppTableStyleRecommendHelper::m_ins = nullptr;


KxWppTableStyleRecommendHelper* KxWppTableStyleRecommendHelper::instance()
{
	if (m_appExited)
		return nullptr;

	if (!m_ins)
	{
		m_ins = new KxWppTableStyleRecommendHelper();
		connect(qApp, SIGNAL(aboutToQuit()), m_ins, SLOT(onAppQuit()));
	}

	return m_ins;
}

HRESULT KxWppTableStyleRecommendHelper::getLastSelectedSlide(wppoldapi::_Slide** ppSlide)
{
	ks_stdptr<wppoldapi::SlideRange> spSlideRange;
	if (SUCCEEDED(getSelectedSlideRange(&spSlideRange)))
	{
		long nCnt = 0;
		spSlideRange->get_Count(&nCnt);
		if (nCnt > 0)
			return spSlideRange->get_Item(KComVariant(nCnt), ppSlide);
	}

	return E_FAIL;
}

HRESULT KxWppTableStyleRecommendHelper::getSelectedSlideRange(wppoldapi::SlideRange** ppSlideRng)
{
	IKApplication* pApp = kxApp->coreApplication();
	if (!pApp)
		return E_FAIL;

	ks_stdptr<wppoldapi::Selection> spSelection;
	HRESULT hr = getSelection(pApp, &spSelection);
	if (SUCCEEDED(hr) && spSelection)
	{
		ks_stdptr<IKSelectionInfo> spSelectionInfo = spSelection;
		return spSelectionInfo->GetSlideRange(ppSlideRng);
	}

	*ppSlideRng = NULL;
	return E_FAIL;
}

HRESULT KxWppTableStyleRecommendHelper::getSelection(IKApplication* pCoreApp, wppoldapi::Selection** ppSelection)
{
	ASSERT(pCoreApp);

	ks_stdptr<wppoldapi::_Application> spApiApp = pCoreApp;
	ASSERT(spApiApp);
	if (!spApiApp)
		return S_FALSE;

	ks_stdptr<wppoldapi::DocumentWindow> spWindow;
	HRESULT hr = spApiApp->get_ActiveWindow(&spWindow);
	if (FAILED(hr))
		return hr;

	return spWindow->get_Selection(ppSelection);
}

bool KxWppTableStyleRecommendHelper::isTableAvailable()
{
	IKApplication* pApp = kxApp->coreApplication();
	if (!pApp)
		return false;

	ks_stdptr<wppoldapi::Selection> spSelection;
	getSelection(pApp, &spSelection);
	if (!spSelection)
		return false;
	
	ks_stdptr<wppoldapi::ShapeRange> spKsoShapeRange;
	spSelection->get_ShapeRange(&spKsoShapeRange);
	if (!spKsoShapeRange)
		return false;

	int count = 0;
	spKsoShapeRange->get_Count(&count);
	if (count < 1 || count > 1)
		return false;

	ks_stdptr<oldapi::KsoShape> spShape;
	spKsoShapeRange->_Item(1, &spShape);
	if (!spShape)
		return false;

	KsoShapeType t = ksoShapeTypeMixed;
	HRESULT hr = spShape->get__Type(&t);
	if (FAILED(hr))
		return false;

	return t == ksoTable;
}

HRESULT KxWppTableStyleRecommendHelper::getActivePresentation(wppoldapi::_Presentation** ppPres)
{
	HRESULT hr = E_FAIL;
	if (IKApplication* coreApp = kxApp->coreApplication())
	{
		ks_stdptr<wppoldapi::_Application> spApiApp = coreApp;
		hr = spApiApp->get_ActivePresentation(ppPres);
	}

	return hr;
}

float KxWppTableStyleRecommendHelper::getImageRate(wppoldapi::_Presentation* spPres, const QSize& imageSize)
{
	float imageRate = 1;

	if (imageSize.isEmpty()) return imageRate;
	ks_stdptr<wppoldapi::PageSetup> spSrcPage;
	float oriW = 0;
	float oriH = 0;
	HRESULT hrc = spPres->get_PageSetup(&spSrcPage);
	if (FAILED(hrc)) return imageRate;
	hrc = spSrcPage->get_SlideWidth(&oriW);
	if (FAILED(hrc)) return imageRate;
	hrc = spSrcPage->get_SlideHeight(&oriH);
	if (FAILED(hrc)) return imageRate;
	float dpi = 72.0f / 96.0f;
	imageRate = std::min(imageSize.width() * dpi / oriW, imageSize.height() * dpi / oriH);

	return imageRate;
}

bool KxWppTableStyleRecommendHelper::getLastSlideImg(QImage* img)
{
	ks_stdptr<wppoldapi::_Slide> spSlide;
	getLastSelectedSlide(&spSlide);
	if (!spSlide)
		return false;

	ks_stdptr<wppoldapi::_Presentation> spPres;
	getActivePresentation(&spPres);
	if (!spPres)
		return false;

	float imgRate = getImageRate(spPres, QSize(300, 300));
	HRESULT hr = spSlide->ExportToImage(img, imgRate);
	if (!SUCCEEDED(hr))
		return false;

	return true;
}

bool KxWppTableStyleRecommendHelper::isSlideImageChanged()
{
	QString imgStr = getLastSlideImgBase64();
	if (imgStr.isEmpty())
		return false;

	QByteArray res = QCryptographicHash::hash(imgStr.toLatin1(), QCryptographicHash::Md5).toHex();
	if (res != m_curSlideImgMd5)
	{
		m_curSlideImgMd5 = res;
		m_curSlideImgStr = imgStr;
		return true;
	}

	return false;
}

QString KxWppTableStyleRecommendHelper::getLastSlideImgBase64()
{
	if (!m_curSlideImgStr.isEmpty())
	{
		QString ret = m_curSlideImgStr;
		m_curSlideImgStr.clear();
		return ret;
	}

	QImage img;
	if (getLastSlideImg(&img))
	{
		QByteArray byteArray;
		QBuffer buffer(&byteArray);
		img.save(&buffer, "jpg");
		return byteArray.toBase64();
	}

	return "";
}

KxWppTableStyleRecommendHelper::KxWppTableStyleRecommendHelper()
{

}

KxWppTableStyleRecommendHelper::~KxWppTableStyleRecommendHelper()
{

}

void KxWppTableStyleRecommendHelper::onAppQuit()
{
	m_appExited = true;
	if (m_ins)
	{
		delete m_ins;
		m_ins = nullptr;
	}
}