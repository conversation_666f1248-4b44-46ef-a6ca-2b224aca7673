﻿#include "stdafx.h"
#include "kcomctl/kcommandfactory.h"
#include <kso/api/wpsapi_old.h>
using namespace wpsoldapi;
#include <kxshare/kxsubwindow.h>
#include <kxshare/kxtaskpane.h>
#include "kxtptablestylecommand.h"
#include "ktablecontroller.h"
#include "wpstablestyle.h"
#include "kxonlinereshelper.h"
#include <kso/l10n/wps/wpsuil.h>
#include "tablestyleparser/kxcommontablestyleparser.h"

namespace {
	void updatePartStyleInfo(ThemeTableStylePart& stylePart, const KxCommonTableStyleInfo::PartStyleInfo& styleInfo)
	{
		auto getColor = [](const drawing::Color clr) {
			if(clr.type() == drawing::Color::SRgb)
				return drawing::Color::fromRgb(clr.toRgb().rgb());
			return clr;
		};
		if (!styleInfo.fillColor.isNull())
		{
			drawing::Fill fillColor = styleInfo.fillColor;
			if (fillColor->hasBackgroundColor())
				fillColor->setBackgroundColor(getColor(fillColor->backgroundColor()));
			if(fillColor->hasColor())
				fillColor->setColor(getColor(fillColor.color()));
			stylePart.fill = fillColor;
		}

		if (!styleInfo.fontInfo.fontColor.isEmpty())
			stylePart.font.clr = getColor(styleInfo.fontInfo.fontColor);
		if (styleInfo.fontInfo.bBold)
			stylePart.font.bold = styleInfo.fontInfo.bBold;
		if (styleInfo.fontInfo.bItalic)
			stylePart.font.italic = styleInfo.fontInfo.bItalic;
		if (styleInfo.fontInfo.underLine != 0)
			stylePart.font.underline = 1;
		if (styleInfo.fontInfo.size != 0)
			stylePart.font.size = styleInfo.fontInfo.size;
		
		for (const auto& borderType : styleInfo.borderInfo.keys())
		{
			drawing::Outline line = styleInfo.borderInfo[borderType];
			if(line.isNull())
				continue;
			TableThemeLine* styleLine = nullptr;
			switch (borderType)
			{
			case KxCommonTableStyleInfo::Left:
				styleLine = &stylePart.leftBorder;
				break;
			case KxCommonTableStyleInfo::Right:
				styleLine = &stylePart.rightBorder;
				break;
			case KxCommonTableStyleInfo::Top:
				styleLine = &stylePart.topBorder;
				break;
			case KxCommonTableStyleInfo::Bottom:
				styleLine = &stylePart.bottomBorder;
				break;
			case KxCommonTableStyleInfo::InsideH:
				styleLine = &stylePart.horBorder;
				break;
			case KxCommonTableStyleInfo::InsideV:
				styleLine = &stylePart.verBorder;
				break;
			default:
				break;
			}
			if (styleLine == nullptr)
				continue;
			if (line->hasWidth())
				styleLine->width = line->width();
			if (line->hasFill())
				styleLine->clr = getColor(line->fill().color());
			if (line->hasCompoundLine())
				styleLine->lineStyle = line->compoundLine() == drawing::CompoundLineSingle ? wpsLineStyleSingle : wpsLineStyleDouble;
			if (line->hasDashType())
			{
				drawing::LineDashType lineDashType = line->dashType();
				switch (lineDashType)
				{
				case drawing::LineDashTypeSolid:
					styleLine->lineStyle = wpsLineStyleSingle;
					break;
				case drawing::LineDashTypeSystemDot:
					styleLine->lineStyle = wpsLineStyleDot;
					break;
				case drawing::LineDashTypeSystemDash:
					styleLine->lineStyle = wpsLineStyleDashSmallGap;
					break;
				case drawing::LineDashTypeSystemDashDotDot:
					styleLine->lineStyle = wpsLineStyleDashDotDot;
					break;
				case drawing::LineDashTypeSystemDashDot:
					styleLine->lineStyle = wpsLineStyleDashDot;
					break;
				case drawing::LineDashTypeDash:
					styleLine->lineStyle = wpsLineStyleDashLargeGap;
					break;
				default:
					break;
				}
			}
		}
	}

	bool convertTableStyle(const QByteArray& data, QList<KxCommonTableStyleInfo::PartStyleType>& optionList, const KOTSResourceInfo& resourceInfo, ThemeTableStyleInfo& styleInfo)
	{
		KxCommonTableStyleParser parser;
		parser.setCustomColor(resourceInfo.themeColorKey, resourceInfo.customColor);
		if (!parser.parserContent(data))
			return false;
		optionList = parser.getStyleOptionList();
		const QList<KxCommonTableStyleInfo::PartStyleInfo>& styleList = parser.getStyleList();
		const KxCommonTableStyleInfo::StyleInfo tblStyle = parser.getTableStyle();
		for (const auto& styleType : tblStyle.partStyleIndexMap.keys())
		{
			const auto& partStyle = styleList[tblStyle.partStyleIndexMap[styleType]];
			switch (styleType)
			{
			case KxCommonTableStyleInfo::WholeTable:
				updatePartStyleInfo(styleInfo.WholeTable, partStyle);
				break;
			case KxCommonTableStyleInfo::FirstRow:
				updatePartStyleInfo(styleInfo.FirstRow, partStyle);
				break;
			case KxCommonTableStyleInfo::LastRow:
				updatePartStyleInfo(styleInfo.LastRow, partStyle);
				break;
			case KxCommonTableStyleInfo::FirstCol:
				updatePartStyleInfo(styleInfo.FirstColumn, partStyle);
				break;
			case KxCommonTableStyleInfo::LastCol:
				updatePartStyleInfo(styleInfo.LastColumn, partStyle);
				break;
			case KxCommonTableStyleInfo::Band1H:
				updatePartStyleInfo(styleInfo.Band1H, partStyle);
				break;
			case KxCommonTableStyleInfo::Band2H:
				updatePartStyleInfo(styleInfo.Band2H, partStyle);
				break;
			case KxCommonTableStyleInfo::Band1V:
				updatePartStyleInfo(styleInfo.Band1V, partStyle);
				break;
			case KxCommonTableStyleInfo::Band2V:
				updatePartStyleInfo(styleInfo.Band2V, partStyle);
				break;
			case KxCommonTableStyleInfo::TLCELL:
				updatePartStyleInfo(styleInfo.TLCELL, partStyle);
				break;
			case KxCommonTableStyleInfo::TRCELL:
				updatePartStyleInfo(styleInfo.TRCELL, partStyle);
				break;
			case KxCommonTableStyleInfo::BLCELL:
				updatePartStyleInfo(styleInfo.BLCELL, partStyle);
				break;
			case KxCommonTableStyleInfo::BRCELL:
				updatePartStyleInfo(styleInfo.BRCELL, partStyle);
				break;
			}
		}
		return true;
	}
}

KxTpWpsOnlineTableStyleCommand::KxTpWpsOnlineTableStyleCommand(KxMainWindow* host, QObject* parent)
	: KxDocerTpTableStyleCommand(host, parent)
{

}

OperatorErrorCode KxTpWpsOnlineTableStyleCommand::applyTableStyleDocument(const KOTSResourceInfo& resourceInfo, const QByteArray& content, bool hover)
{
	if (!KxOnlineTableResHelper::wpsHasActiveTable() && !resourceInfo.bInsertTable)
		return InsertOpNoActiveTable;

	KTableStyle tableStyle;
	ThemeTableStyleInfo styleInfo;
	QList<KxCommonTableStyleInfo::PartStyleType> optionList;
	if (!convertTableStyle(content, optionList, resourceInfo, styleInfo))
		return StyleFileInValide;

	tableStyle.Init(styleInfo,
		optionList.contains(KxCommonTableStyleInfo::PartStyleType::FirstRow),
		optionList.contains(KxCommonTableStyleInfo::PartStyleType::LastRow),
		optionList.contains(KxCommonTableStyleInfo::PartStyleType::FirstCol),
		optionList.contains(KxCommonTableStyleInfo::PartStyleType::LastCol),
		optionList.contains(KxCommonTableStyleInfo::PartStyleType::Band1H),
		optionList.contains(KxCommonTableStyleInfo::PartStyleType::Band1V));
	if (!hover)
		onLeaveHoverPreview();
	if (!m_previewTr->isInPreview())
		m_previewTr->beginPreview(resourceInfo.bInsertTable ? TxVersion_InsertTable : TxVersion_ApplyTableStyle);

	bool bSuccess = true;
	if (resourceInfo.bInsertTable)
		bSuccess = insertTableByCommand();

	CWpsTableStyle ts;
	if (bSuccess)
		bSuccess = ts.applyStyle(&tableStyle);

	if(!hover)
		m_previewTr->endPreview(bSuccess);
	if (bSuccess)
	{
		//刷新DocView
		KxMainWindow* pMainWnd = qobject_cast<KxMainWindow*>(host());
		if (pMainWnd != NULL)
		{
			IKView* pDocView = pMainWnd->getActiveCoreView();
			if (pDocView != NULL)
				pDocView->Invalidate(NULL);
		}
	}
	return bSuccess ? Success : InsertOpError;
}