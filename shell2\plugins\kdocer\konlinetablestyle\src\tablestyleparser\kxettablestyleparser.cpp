﻿#include "stdafx.h"
#include <public_header/kso/krepl/ksorepl.h>
#include <src/tablestyleparser/kxettablestyleparser.h>
#include <ooxml/mapping/ns/ns.h>
#include <package/openXmlServer.h>
#include <mso/io/basic/length_unit.h>
#include <mso/io/basic/lenthconv.h>
#include <applogic/etapi_old.h>
using namespace etoldapi;
#include "incbackup/incbackuphelper.h"

using namespace std;
using namespace mso::ooxml;

void Decoed_xHHHH_Str(const WStr& wsStr, ks_wstring& wsRet, bool isAppend)
{
	struct _hlp
	{
		static bool IsHexChar(WCHAR ch)
		{
			return ((ch >= '0' && ch <= '9') ||
				(ch >= 'a' && ch <= 'f') ||
				(ch >= 'A' && ch <= 'F'));
		}

		static bool IsAllHexNumber(const WCHAR* p, UINT* pNum)
		{
			static WCHAR buf[5] = { 0 };
			for (int k = 0; k < 4; ++k)
			{
				if (!IsHexChar(*p))
				{
					return false;
				}
				buf[k] = *p++;
			}
			buf[4] = 0;
			*pNum = xstrtol(buf, NULL, 16);
			return true;
		}
	};

	if (!isAppend)
		wsRet.clear();
	size_t nCount = wsStr.size();
	if (wsStr.empty() || nCount < 7)
	{
		wsRet.append(wsStr.c_str(), wsStr.length());
		return;
	}

	const WCHAR* pCur = wsStr.c_str();
	const WCHAR* pBegin = pCur;
	size_t len = 0;

	for (size_t i = 0; i < nCount; ++i, ++pCur)
	{
		if (i + 6 >= nCount)
		{
			len += nCount - i;
			break;
		}
		if (*pCur == '_' && *(pCur + 1) == 'x' && *(pCur + 6) == '_')
		{
			wsRet.append(pBegin, len);
			len = 0;
			pBegin = pCur + 7;

			i += 2; pCur += 2;
			UINT tmp;
			if (_hlp::IsAllHexNumber(pCur, &tmp))
			{
				i += 4; pCur += 4;
				wsRet.push_back(tmp);
				continue;
			}

			--i; --pCur;
			pBegin = pCur - 1;
			len = 2;
		}
		else
		{
			++len;
		}
	}

	wsRet.append(pBegin, len);
}

void DecodeIndexedColor(UINT32 icv, EtColor& clr)
{
	if (icv < 64)
	{
		clr.setICV(static_cast<BYTE>(icv));
	}
	else if (icv == 64 || icv == 72 || icv == 77 ||
		icv == 0x7FFF)
	{
		clr.setAUTO();
	}
	else if (icv == 65 || icv == 75 || icv == 78)
	{
		clr.setNONE();
	}
	else if (icv < 80)
	{
		clr.setICV(static_cast<BYTE>(icv));
	}
	else
	{
		ASSERT(icv == 0x51);
		clr.setAUTO();
	}
}

void XlsNumFmt2ETNumFmt(PCWSTR pwszNumFmt, ks_wstring& wstrNumFmt)
{
	kfc::nf::NFHANDLE hNumFmt = NULL;
	HRESULT hr = kfc::nf::_XNFCompileForExcel(pwszNumFmt, &hNumFmt);

	if (SUCCEEDED(hr))
	{
		BSTR bstrNumFmt = NULL;
		if (SUCCEEDED(kfc::nf::_XNFUnCompile(hNumFmt, &bstrNumFmt, gGetNF_FORMAT_PARAM())))
		{
			wstrNumFmt = bstrNumFmt;
			::SysFreeString(bstrNumFmt);
		}

		kfc::nf::_XNFRelease(hNumFmt);
		return;
	}
	wstrNumFmt = pwszNumFmt;
}

DWORD styleFormatToStyleOption(TABLE_STYLE_FORMAT tsf)
{
	switch (tsf)
	{
	case TSF_HeaderRowXF:
		return TSO_HEADER_ROW;
	case TSF_TotalRowXF:
		return TSO_TOTAL_ROW;
	case TSF_FirstRowStripeXF:
	case TSF_SecondRowStripeXF:
		return TSO_BANDED_ROWS;
	case TSF_FirstColStripeXF:
	case TSF_SecondColStripeXF:
		return TSO_BANDED_COLS;
	case TSF_FirstColXF:
		return TSO_FIRST_COL;
	case TSF_LastColXF:
		return TSO_LAST_COL;
	default:
		return INV_TABLESTYLE_OPTS;
	}
}


//KxtableDxfsParser
KxtableDxfsParser::KxtableDxfsParser()
	:KxlslParser()
	, m_iDXf(0)
{
	ks_stdptr<IKWorkbook> spWorkbook = kxApp->coreApplication()->GetActiveDocument();
	ASSERT(NULL != spWorkbook);
	if (spWorkbook)
	{
		m_ptrBook = spWorkbook->GetBook();
	}
}

bool KxtableDxfsParser::parserElement(const QDomElement& element)
{
	//attr
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::count:
		case x14::count:
			m_vecTableXf.resize(attr.value().toUInt());
			break;
		}
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;

	//child element
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::dxf:
			return collectDxf(childElement);
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

void KxtableDxfsParser::setDxfCustomColor(const int themeColorKey, const QString& customColor)
{
	m_themeColorKey = themeColorKey;
	m_CustomColor = customColor;
}

vector<io_utils::DXF> KxtableDxfsParser::getTableXf() const
{
	return m_vecTableXf;
}

bool KxtableDxfsParser::collectDxf(const QDomElement& element)
{
	io_utils::DXF& xf = m_vecTableXf[m_iDXf++];
	initDXF(xf);

	return parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::font:
			return CollectDXfFont(childElement, xf);
		case s::numFmt:
			return CollectDXfNumFmt(childElement, xf);
		case s::fill:
			return CollectDXfFill(childElement, xf);
		case s::border:
			return CollectDXfBorder(childElement, xf);
		case s::alignment:
			return CollectDXfAlignment(childElement, xf);
		case s::protection:
			return CollectDXfProtection(childElement, xf);
		}
		return true;
	});
}

bool KxtableDxfsParser::CollectDXfFont(const QDomElement& element, io_utils::DXF& xf)
{
	QDomAttr valAttr;
	return parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::name:
		{
			if (fetchVal(childElement, valAttr))
			{
				ks_wstring strName;
				Decoed_xHHHH_Str(krt::utf16(valAttr.value()), strName, false);
				if (strName.length() > MAX_FONTNAME_CCH)
					kcwcsncpy_s(xf.font.name, strName.c_str(), MAX_FONTNAME_CCH);
				else
					kcwcscpy_s(xf.font.name, strName.c_str());
			}
		}
		break;
		case s::charset:
		{
			xf.mask.inc_bCharSet = 1;
			if (fetchVal(childElement, valAttr))
			{
				xf.font.bCharSet = valAttr.value().toUInt();
			}
		}
		break;
		case s::family:
		{
			xf.mask.inc_bFamily = 1;
			if (fetchVal(childElement, valAttr))
			{
				xf.font.bFamily = valAttr.value().toUInt();
			}
		}
		break;
		case s::b:
		{
			xf.mask.inc_bls = 1;
			if (fetchVal(childElement, valAttr))
			{
				xf.font.bls = onOff(valAttr.value());
			}
			else {
				xf.font.bls = TRUE;
			}
		}
		break;
		case s::i:
		{
			xf.mask.inc_fItalic = 1;
			if (fetchVal(childElement, valAttr))
			{
				xf.font.fItalic = onOff(valAttr.value());
			}
			else {
				xf.font.fItalic = TRUE;
			}
		}
		break;
		case s::strike:
		{
			xf.mask.inc_fStrikeout = 1;
			if (fetchVal(childElement, valAttr))
			{
				xf.font.fStrikeout = onOff(valAttr.value());
			}
			else {
				xf.font.fStrikeout = TRUE;
			}
		}
		break;
		case s::color:
			xf.mask.inc_clr = 1;
			fetchColor(childElement, xf.font.clr);
			break;
		case s::sz:
		{
			xf.mask.inc_dyHeight = 1;
			if (fetchVal(childElement, valAttr))
			{
				xf.font.dyHeight = LengthValue::pt_to_twip(valAttr.value().toUInt());
			}
		}
		break;
		case s::u:
		{
			xf.mask.inc_uls = 1;
			if (fetchVal(childElement, valAttr))
			{
				xf.font.uls = m_transMap.GetUnderLineStyle(krt::utf16(valAttr.value()));
			}
			else {
				xf.font.uls = ulsSingle;
			}
		}
		break;
		case s::vertAlign:
		{
			if (fetchVal(childElement, valAttr))
			{
				xf.font.sss = m_transMap.GetSubscript(krt::utf16(valAttr.value()));
			}
			else {
				xf.font.sss = ssSuperscript;
			}

			if (ssNone != xf.font.sss)
				xf.mask.inc_sss = 1;
		}
		break;
		}
		return true;
	});
}

bool KxtableDxfsParser::CollectDXfNumFmt(const QDomElement& element, io_utils::DXF& xf)
{
	return parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::formatCode:
		{
			ks_wstring wsNf;
			XlsNumFmt2ETNumFmt(krt::utf16(attr.value()), wsNf);
			if (wsNf.size() <= MAX_NUMBERFMT_CCH)
			{
				xf.mask.inc_pNumFmt = 1;
				kcwcscpy_s(xf.numfmt.fmt, wsNf.c_str());
			}
		}
		break;
		}
		return true;
		});
}

bool KxtableDxfsParser::CollectDXfFill(const QDomElement& element, io_utils::DXF& xf)
{
	bool bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::patternFill:
			return collectPatternFill(childElement, &xf.mask, xf.fill);
		case s::gradientFill:
			return collectGradientFill(childElement, &xf.mask, xf.fill);
		}
		return true;
		});

	ASSERT(bRet);
	if (!bRet)
		return false;

	if (xf.fill.getType() == eftPatternSolid)
		return true;

	if ((xf.mask.inc_clrBack == 1 || xf.mask.inc_clrFore == 1) && xf.mask.inc_eft == 0)
	{
		xf.mask.inc_eft = 1;
		xf.fill.setType(eftPatternSolid);
	}

	if (!xf.fill.isGradient() && xf.mask.inc_clrBack == 1)
	{
		if (xf.fill.getBack().getType() == ectNONE ||
			(xf.fill.getBack().getType() == ectAUTO && xf.fill.getBack().getTint() == 0))
		{
			xf.mask.inc_clrBack = 0;
		}
	}

	return true;
}

bool KxtableDxfsParser::collectPatternFill(const QDomElement& element, XFMASK* pMask, EtFill& f)
{
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::patternType:
			pMask->inc_eft = 1;
			pMask->inc_clrFore = 1;
			f.setType(m_transMap.GetBkPattern(krt::utf16(attr.value())));
			f.setBack(ectAUTO);
			f.setFore(ectAUTO);
			break;
		}
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;

	//child
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::fgColor:
		{
			pMask->inc_clrFore = 1;
			EtColor clr;
			fetchColor(childElement, clr);
			f.setFore(clr);
		}
		break;
		case s::bgColor:
		{
			pMask->inc_clrBack = 1;
			EtColor clr;
			fetchColor(childElement, clr);
			f.setBack(clr);
		}
		break;
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxtableDxfsParser::collectGradientFill(const QDomElement& element, XFMASK* pMask, EtFill& f)
{
	std::pair<bool, bool> prFlag;
	prFlag.first = prFlag.second = false;
	pMask->inc_eft = 1;
	pMask->inc_clrFore = pMask->inc_clrBack = 0;
	f.setType(eftGradientFillLinear);
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::type:
			if (0 == xstrcmp(__X("linear"), krt::utf16(attr.value())))
				f.setType(eftGradientFillLinear);
			else if (0 == xstrcmp(__X("path"), krt::utf16(attr.value())))
				f.setType(eftGradientFillPath);
			else
				ASSERT(FALSE);
			break;
		case s::degree:
			f.gfpX()->setDegree(attr.value().toDouble());
			break;
		case s::left:
		{
			double d = attr.value().toDouble();
			if (d >= 0 && d <= 1)
				f.gfpX()->setLeft(d);
		}
		break;
		case s::right:
		{
			double d = attr.value().toDouble();
			if (d >= 0 && d <= 1)
				f.gfpX()->setRight(d);
		}
		break;
		case s::top:
		{
			double d = attr.value().toDouble();
			if (d >= 0 && d <= 1)
				f.gfpX()->setTop(d);
		}
		break;
		case s::bottom:
		{
			double d = attr.value().toDouble();
			if (d >= 0 && d <= 1)
				f.gfpX()->setBottom(d);
		}
		break;
		};
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;

	//child
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::stop:
		{
			EtGradStop gs;
			if (!fetchGradStop(childElement, gs, prFlag))
				return false;
			f.addStop(gs);
		}
		break;
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxtableDxfsParser::CollectDXfBorder(const QDomElement& element, io_utils::DXF& xf)
{
	bool fUp = false, fDown = false;
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::diagonalUp:
			if (onOff(attr.value()))
				fUp = true;
			break;
		case s::diagonalDown:
			if (onOff(attr.value()))
				fDown = true;
			break;
		}
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;

	//child
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		BYTE dg = 0;
		EtColor clr(ectAUTO);

		switch (xn)
		{
		case s::left:
			CollectDXfBorderLine(childElement, dg, clr);
			xf.mask.inc_dgLeft = 1;
			xf.mask.inc_clrLeft = 1;
			xf.dgLeft = dg;
			xf.clrLeft = clr;
			break;
		case s::right:
			CollectDXfBorderLine(childElement, dg, clr);
			xf.mask.inc_dgRight = 1;
			xf.mask.inc_clrRight = 1;
			xf.dgRight = dg;
			xf.clrRight = clr;
			break;
		case s::top:
			CollectDXfBorderLine(childElement, dg, clr);
			xf.mask.inc_dgTop = 1;
			xf.mask.inc_clrTop = 1;
			xf.dgTop = dg;
			xf.clrTop = clr;
			break;
		case s::bottom:
			CollectDXfBorderLine(childElement, dg, clr);
			xf.mask.inc_dgBottom = 1;
			xf.mask.inc_clrBottom = 1;
			xf.dgBottom = dg;
			xf.clrBottom = clr;
			break;
		case s::diagonal:
		{
			CollectDXfBorderLine(childElement, dg, clr);
			if (fUp)
			{
				xf.mask.inc_dgDiagUp = 1;
				xf.mask.inc_clrDiagUp = 1;
				xf.dgDiagUp = dg;
				xf.clrDiagUp = clr;
			}
			if (fDown)
			{
				xf.mask.inc_dgDiagDown = 1;
				xf.mask.inc_clrDiagDown = 1;
				xf.dgDiagDown = dg;
				xf.clrDiagDown = clr;
			}
		}
		break;
		case s::horizontal:
			CollectDXfBorderLine(childElement, dg, clr);
			xf.inc_dgHorz = 1;
			xf.inc_clrHorz = 1;
			xf.dgHorz = dg;
			xf.clrHorz = clr;
			break;
		case s::vertical:
			CollectDXfBorderLine(childElement, dg, clr);
			xf.inc_dgVert = 1;
			xf.inc_clrVert = 1;
			xf.dgVert = dg;
			xf.clrVert = clr;
			break;
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxtableDxfsParser::CollectDXfBorderLine(const QDomElement& element, BYTE& dg, EtColor& clr)
{
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::style:
			dg = m_transMap.GetBorderLineStyle(krt::utf16(attr.value()));
			break;
		}
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;

	//child
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::color:
			return fetchColor(childElement, clr);
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxtableDxfsParser::CollectDXfAlignment(const QDomElement& element, io_utils::DXF& xf)
{
	return parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::horizontal:
			xf.alcH = m_transMap.GetHAlignment(krt::utf16(attr.value()));
			xf.mask.inc_alcH = 1;
			break;
		case s::vertical:
			xf.alcV = m_transMap.GetVAlignment(krt::utf16(attr.value()));
			xf.mask.inc_alcV = 1;
			break;
		case s::textRotation:
			xf.trot = attr.value().toUInt();
			xf.mask.inc_trot = 1;
			break;
		case s::wrapText:
			xf.fWrap = onOff(attr.value());
			xf.mask.inc_fWrap = 1;
			break;
		case s::indent:
			xf.cIndent = attr.value().toUInt();
			xf.mask.inc_cIndent = 1;
			break;
		case s::shrinkToFit:
			xf.fShrinkToFit = onOff(attr.value());
			xf.mask.inc_fShrinkToFit = 1;
			break;
		case s::readingOrder:
			xf.iReadingOrder = m_transMap.GetReadingOrder(krt::utf16(attr.value()));
			xf.mask.inc_iReadingOrder = 1;
			break;
		}
		return true;
	});
}

bool KxtableDxfsParser::CollectDXfProtection(const QDomElement& element, io_utils::DXF& xf)
{
	return parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::locked:
			xf.fLocked = onOff(attr.value());
			xf.mask.inc_fLocked = 1;
			break;
		case s::hidden:
			xf.fHidden = onOff(attr.value());
			xf.mask.inc_fHidden = 1;
			break;
		}
		return true;
		});
}

void KxtableDxfsParser::initDXF(io_utils::DXF& xf)
{
	// font
	// ----------------------------------------------------------------------
	InitFONT(xf.font);

	// ----------------------------------------------------------------------
	// align
	xf.trot = 0;
	xf.alcH = haGeneral;
	xf.alcV = vaCenter;
	xf.fWrap = 0;
	xf.fShrinkToFit = 0;
	xf.cIndent = 0;
	xf.iReadingOrder = roContext;

	// borders
	xf.dgLeft = blsNone;
	xf.dgRight = blsNone;
	xf.dgTop = blsNone;
	xf.dgBottom = blsNone;
	xf.dgDiagUp = blsNone;
	xf.dgDiagDown = blsNone;
	xf.clrLeft.setAUTO();
	xf.clrRight.setAUTO();
	xf.clrTop.setAUTO();
	xf.clrBottom.setAUTO();
	xf.clrDiagUp.setAUTO();
	xf.clrDiagDown.setAUTO();

	// pattern
	xf.fill.setType(eftPatternNone);

	// protection
	xf.fLocked = 1;
	xf.fHidden = 0;

	// number format
	ks_stdptr<INumberFmts> ptrNumFmts;
	VS(m_ptrBook->GetNumberFmts(&ptrNumFmts));

	const NUMFMT* pNumFmt = NULL;
	VS(ptrNumFmts->GetGeneral(&pNumFmt, NULL));
	kcwcscpy_s(xf.numfmt.fmt, pNumFmt->fmt);

	// set mask
	xf.getmask()._cats = XFMASK::_catNone;
	xf.getmask()._catsFont = XFMASK::_catFontNone;
}

void KxtableDxfsParser::InitFONT(FONT& font)
{
	font.themeFontType = 0;
	font.bls = 0;
	font.fItalic = 0;
	font.fStrikeout = 0;
	font.reserved1 = 0;
	font.sss = ssNone;
	font.uls = ulsNone;
	font.clr.setAUTO();

	font.bFamily = 0;
	font.bCharSet = csANSIChineseGBK;
	font.dyHeight = 10 * TWIPS_PER_POINT;
	kcwcscpy_s(font.name, __X("ËÎÌå"));
}

bool KxtableDxfsParser::fetchVal(const QDomElement& element, QDomAttr& outAttr)
{
	if (!element.hasAttributes())
		return false;
	return parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::val:
		{
			outAttr = attr;
		}
		break;
		}
		return true;
	});
}

bool KxtableDxfsParser::fetchColor(const QDomElement& element, EtColor& clr)
{
	return parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::indexed:
		case x14::index:
			DecodeIndexedColor(attr.value().toUInt(), clr);
			break;
		case s::rgb:
		case x14::rgb:
			clr.setARGB(hexLong(attr.value()) | 0xff000000);
			break;
		case s::_auto:
		case x14::_auto:
			clr.setAUTO();
			break;
		case s::theme:
		case x14::theme:
		{
			UINT thmColorIndx = attr.value().toUInt();
			double curTint = clr.getTint();
			if (thmColorIndx == ettThemeAccent1)	//特殊处理着色1，允许被替换颜色
			{
				if (m_themeColorKey >= 1 && m_themeColorKey <= 6)
				{
					clr.setTheme((ETTHEMETYPE)(ettThemeAccent1 + m_themeColorKey - 1), curTint);		//主题色1值为1，故进行-1定位
					break;
				}
				else if (m_themeColorKey == 7)	//替换颜色为 深色 1
				{
					clr.setTheme(ettThemeLight1, curTint);
					break;
				}
				else if (!m_CustomColor.isEmpty())
				{
					clr.setARGB(hexLong(m_CustomColor.replace("#", "FF")) | 0xff000000, curTint);
					break;
				}
			}

			if (thmColorIndx < ETT_THEME_TYPE_COUNT)
				clr.setTheme((ETTHEMETYPE)thmColorIndx, curTint);
		}
		break;
		case s::tint:
		case x14::tint:
			clr.setTint(attr.value().toDouble());
			break;
		}
		return true;
	});
}

bool KxtableDxfsParser::fetchGradStop(const QDomElement& element, EtGradStop& gs, std::pair<bool, bool>& pr)
{
	EtColor clr(ectEMPTY);

	//attr
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::position:
		{
			double dVal = attr.value().toDouble();
			if (dVal >= 0 && dVal <= 1)
			{
				gs.pos = dVal;
			}
			else
			{
				ASSERT(FALSE);
				return false;
			}
		}
		break;
		}
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;

	//child
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::color:
			fetchColor(childElement, gs.clr);
			break;
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

//KxtableStyleParser
KxtableStyleParser::KxtableStyleParser()
	:KxlslParser()
{}

bool KxtableStyleParser::parserElement(const QDomElement& element)
{
	//child element
	return parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::dxfs:
			return m_dxfParser.parserElement(childElement);//collectDxfs(element);
		case s::tableStyle:
			m_tblStyElems.clear();
			return collectTableStyle(childElement);
		default:
			if (childElement.tagName() == "tableStyleEx")
				return collectTableStyle(childElement);
			else if (childElement.tagName() == "styleSwitch")
				return collectTableStyleOption(childElement);
		}
		return true;
	});
}

vector<TableStyleElementIO> KxtableStyleParser::getTableStyleElement() const
{
	return m_tblStyElems;
}

vector<io_utils::DXF> KxtableStyleParser::getTableXf() const
{
	return m_dxfParser.getTableXf();
}

const std::map<KxtableStyleParser::KAIETStyleType, INT>& KxtableStyleParser::getKaietStyleElement() const
{
	return m_kaietStyleElems;
}

DWORD KxtableStyleParser::getStyleOption() const
{
	if (m_styleOption != 0)
		return m_styleOption;
	DWORD styleOptions = 0;
	for (UINT idx = 0; idx < m_tblStyElems.size(); ++idx)
	{
		DWORD styleOption = styleFormatToStyleOption(m_tblStyElems[idx].tsf);
		if (styleOption != INV_TABLESTYLE_OPTS)
			styleOptions |= styleOption;
	}

	return styleOptions ? styleOptions : INV_TABLESTYLE_OPTS;
}

void KxtableStyleParser::setCustomColor(const int themeColorKey, const QString& customColor)
{
	m_dxfParser.setDxfCustomColor(themeColorKey, customColor);
}

bool KxtableStyleParser::collectTableStyle(const QDomElement& element)
{
	DWORD dwUsed = TST_USED_BIFF;
	ks_wstring wstrName;

	//attr
	bool bRet = parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::name:
			Decoed_xHHHH_Str(krt::utf16(attr.value()), wstrName, false);
			break;
		case s::pivot:
			alg::SetBit(dwUsed, TST_PIVOT_STYLE, onOff(attr.name()));
			break;
		case s::table:
			alg::SetBit(dwUsed, TST_TABLE_STYLE, onOff(attr.name()));
			break;
		}
		return true;
	});

	ASSERT(bRet);
	if (!bRet)
		return false;

	//child element
	bRet = parserChild(element, [&](XmlName xn, const QDomElement& childElement)->bool {
		switch (xn)
		{
		case s::tableStyleElement:
			return CollectTableStyleElement(childElement);
		}
		return true;
	});

	ASSERT(bRet);
	return bRet;
}

bool KxtableStyleParser::CollectTableStyleElement(const QDomElement& element)
{
	INT nDxfID = -1;
	INT nSize = -1;
	TABLE_STYLE_FORMAT tsf = TSF_None;
	KAIETStyleType kaietst = TSF_KAIET_None;

	//attr
	parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		switch (xn)
		{
		case s::dxfId:
			nDxfID = attr.value().toUInt();
			break;
		case s::size:
			nSize = attr.value().toUInt();
			break;
		case s::type:
			tsf = m_transMap.GetTableStyleType(krt::utf16(attr.value()));
			if (attr.value() == "bigTitle")
				kaietst = TSF_KAIET_BigTitle;
			else if (attr.value() == "info")
				kaietst = TSF_KAIET_Info;
			else if (attr.value() == "bigTitleEnd")
				kaietst = TSF_KAIET_BigTitleEnd;
			else if (attr.value() == "infoEnd")
				kaietst = TSF_KAIET_InfoEnd;
			break;
		}
		return true;
	});

	if (TSF_None == tsf && TSF_KAIET_None == kaietst)
		return false;

	if (tsf != TSF_None)
		m_tblStyElems.push_back(TableStyleElementIO(nDxfID, nSize, tsf));
	else if (kaietst != TSF_KAIET_None)
		m_kaietStyleElems[kaietst] = nDxfID;
	return true;
}

bool KxtableStyleParser::collectTableStyleOption(const QDomElement& element)
{
	if (m_styleOption != 0)
		return true;
	parserAttr(element, [&](XmlName xn, const QDomAttr& attr)->bool {
		if (attr.value() == "0")
			return true;
		switch (xn)
		{
		case s::showFirstColumn:
			m_styleOption |= TSO_FIRST_COL;
			break;
		case s::showLastColumn:
			m_styleOption |= TSO_LAST_COL;
			break;
		case s::showRowStripes:
			m_styleOption |= TSO_BANDED_ROWS;
			break;
		case s::showColumnStripes:
			m_styleOption |= TSO_BANDED_COLS;
			break;
		case s::headerRowCount:
			m_styleOption |= TSO_HEADER_ROW;
			break;
		case s::totalsRowCount:
			m_styleOption |= TSO_TOTAL_ROW;
			break;
		}
		return true;
	});
	return true;
}

bool newEtOnlineTableStyle(const QString& name, KxtableStyleParser& parser, UINT* outId)
{
	ks_stdptr<IKWorkbook> workbook = kxApp->coreApplication()->GetActiveDocument();
	if (!workbook)
		return false;

	ks_stdptr<IBook> spBook = workbook->GetBook();
	if (!spBook)
		return false;

	ks_stdptr<ICoreTableStyles> spTblStys;
	spBook->GetExtDataItem(edBookTableStyles, (IUnknown**)&spTblStys);
	if (!spTblStys)
		return false;

	DWORD dwUsed = TST_USED_BIFF;
	alg::SetBit(dwUsed, TST_TABLE_STYLE, 1);

	//new tablestyle
	ks_stdptr<ICoreTableStyle> spTblSty;
	//table alread exsit
	spTblStys->FindItem(krt::utf16(name), outId, &spTblSty);
	if (spTblSty)
		return true;

	KIncBackupRecordNone br_none;
	spTblStys->AddStyle(krt::utf16(name), outId, dwUsed, &spTblSty);
	if (!spTblSty)
		return false;

	ks_bstr bstrName(krt::utf16(name), name.size());
	{
		KINC_RECORD2(KEtIncRecoverFuncProxy, TableStyle_Add, (BSTR), (DWORD),
			(KIncRecordInfo<decltype(&KEtIncRecoverFuncProxy::TableStyle_Add)>),
			nullptr, workbook.get(), bstrName, dwUsed);
	}

	const std::vector<TableStyleElementIO>& tblStyElems = parser.getTableStyleElement();
	const std::vector<io_utils::DXF>& vecDXFs = parser.getTableXf();

	for (size_t i = 0, n = tblStyElems.size(); i < n; ++i)
	{
		KTableXF txf;
		const TableStyleElementIO& tse = tblStyElems[i];
		ASSERT(tse.tsf != TSF_None);
		if (tse.dxfId >= 0 && tse.dxfId < static_cast<INT>(vecDXFs.size()))
		{
			vecDXFs[tse.dxfId].ToKTableXF(txf);
			spTblSty->SetFormat(tse.tsf, &txf);

			KINC_RECORD4(KEtIncRecoverFuncProxy, TableStyle_SetFormat, (BSTR), (TABLE_STYLE_FORMAT), (UINT),
				(KIncFunction<const KTableXF*, KTableXF*>), (KIncRecordInfo<decltype(&KEtIncRecoverFuncProxy::TableStyle_SetFormat)>),
				nullptr, workbook.get(), bstrName, tse.tsf, tse.size,
				incbackup::SeriHelperCheat<const KTableXF*>(&txf, KEtIncBackupHelper::SeriKTableXF, CusObjPT_Ptr));
		}

		if (tse.size > 0)
			spTblSty->SetStripeSize(tse.tsf, tse.size);
	}

	return true;
}
