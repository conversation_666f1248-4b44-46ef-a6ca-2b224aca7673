﻿#pragma once

class KxQuickHelpBarContainer;
class KTableBeautifyPreviewHelper : public QObject
{
public:
	explicit KTableBeautifyPreviewHelper(QObject* parent = nullptr);
	~KTableBeautifyPreviewHelper();

	static KTableBeautifyPreviewHelper* instance();

	bool isInPreview();
	void beginPreview(QWidget* webView, const QString& desc);
	void endPreview(bool isCommit = false);

protected:
	bool eventFilter(QObject* o, QEvent* e) override;

	void blockQhBarUpdate();
	void unblockQhBarUpdate();

private:
	QString m_commitDesc;
	KsoTriState m_oldState;
	QPointer<QWidget> m_pWebView;
	ks_stdptr<IKTransactionTool> m_spTransTool;
	QList<QPointer<KxQuickHelpBarContainer>> m_qhBars;
};
