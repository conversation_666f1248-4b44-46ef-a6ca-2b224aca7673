﻿#pragma once
#include <QWidget>
#include "kxonlinetablestyledefine.h"

class KLiteWaitingWidget;
class KxDocerTableStyleGalleryModel;
class KxDocerTableStyleWidget : public QWidget
{
	Q_OBJECT
public:
	KxDocerTableStyleWidget(QWidget* parent, KCommand* cmd, const QString& cmdName);
	virtual ~KxDocerTableStyleWidget();

	virtual QSize sizeHint() const override;
	virtual void customEvent(QEvent* e) override;
	virtual void showEvent(QShowEvent* event) override;
	virtual void hideEvent(QHideEvent* event) override;
private:
	int getActiveGalleryItemCount();
	void initWidget();
	void openDocerFeatureTp();

protected:
	QWidget* createGalleryWidget();
	QWidget* createLoadingWidget(QWidget* parent);
	QWidget* createErrorWidget(QWidget* parent, const QString& text);

	QString adaptiveSizeName() const;

protected slots:
	void onRetryClicked();
	void onStateChanged(int status);
	void onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);
	void onDownloadError(const KOTSResourceInfo& resourceInfo);

public slots:
	void closePopupWidget();

signals:
	void sigInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);

private:
	QString m_cmdName;
	QStackedLayout* m_resourceLayout = nullptr;
	KxDocerTableStyleGalleryModel* m_model = nullptr;
	DWORD m_dwLastTick;
	DWORD m_dwLoadTick;
	KCommand* m_cmd;
};