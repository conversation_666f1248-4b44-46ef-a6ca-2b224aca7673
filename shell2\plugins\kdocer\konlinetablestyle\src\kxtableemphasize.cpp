﻿#include "stdafx.h"
#include "kxtableemphasize.h"
#include "kxshare/formatting_helper.h"
#include "kso/api/wpsapi_old.h"
#ifdef Q_OS_WIN
#include "kso/api/wpsapi_old.c"
#endif
#include "ksolite/kxjsonhelper.h"
#include "Coding/core_bundle/office/wps/include/wpscorenotify.h"
#include "kso/framework/corenotify.h"
#include "kdocertoolkit/kdocerutils.h"
#include "kxwpstablebeautifyhelper.h"

namespace 
{
	const QString strRow = "row";
	const QString strColumn = "column";
	const QString strEmphasizeNum = "emphasizeNum";
	const QString strIntersectCellInfo = "intersectCellInfo";

};

///////////////////////////KEmphasizeHandler////////////////////////////////
KEmphasizeHandler::KEmphasizeHandler()
{

}

KEmphasizeHandler::~KEmphasizeHandler()
{

}

bool KEmphasizeHandler::emphasizeTable(const KEmphasizeInfo& emInfo, KEmphasizeIntersectCellInfo& cellInfo,
	KEmphasizeStyleInfo& targetStyle)
{
	targetStyle = getTableStyleToApply(emInfo.originStyle);
	if (!targetStyle.bValid)
		return false;

	return _emphasizeTableNew(targetStyle);
}

KEmphasizeStyleInfo KEmphasizeHandler::getTableStyleToApply(const KEmphasizeStyleInfo& originInfo)
{
	return KEmphasizeStyleInfo();
}

bool KEmphasizeHandler::_emphasizeTable(const KEmphasizeStyleInfo& info)
{
	return false;
}

bool KEmphasizeHandler::_emphasizeTableNew(const KEmphasizeStyleInfo& info)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = KEmphasizeTableHelper::getCurSelection();
	if (!spApiSelection)
		return false;

	KEmphasizeTableHelper::setSelFontStyle(spApiSelection, info.fontColor, info.bBoldFont);
	emphasizeFill(spApiSelection, info);
	emphasizeBorders(spApiSelection, info);
	return true;
}

void KEmphasizeHandler::setBorderStyle(wpsoldapi::Borders* borders, const KBorderStyleInfo& info)
{
	ks_stdptr<wpsoldapi::Border> spBorder;
	drawing::Color col;
	col.setRgb(info.borderColor.rgba());
	HRESULT hr = borders->Item(info.borderType, &spBorder);
	if (SUCCEEDED(hr) && spBorder)
	{
		spBorder->put_LineStyle(info.borderLineStyle);
		spBorder->put_LineWidth(info.borderLineWidth);
		ks_stdptr<IKBorderColor>spBorderColor = spBorder;
		if (spBorderColor)
			spBorderColor->SetColor(col);
	}
}

bool KEmphasizeHandler::emphasizeFill(wpsoldapi::Selection* selection, const KEmphasizeStyleInfo& info)
{
	ks_stdptr<wpsoldapi::Shading> spCellsShading;
	KEmphasizeTableHelper::getSelShading(selection, &spCellsShading);
	setCellsFillColor(spCellsShading, info);
	return true;
}

void KEmphasizeHandler::setCellsFillColor(wpsoldapi::Shading* cellsShading, const KEmphasizeStyleInfo& info)
{
	ks_stdptr<IKShadingColor> spShadingColor = cellsShading;
	if (!spShadingColor)
		return;

	if (!info.backgroundColor.isValid())
		return;

	drawing::Color tmp;
	tmp.setRgb(info.backgroundColor.rgba());
	spShadingColor->SetBackgroundPatternColor(tmp);
}

bool KEmphasizeHandler::emphasizeBorders(wpsoldapi::Selection* selection, const KEmphasizeStyleInfo& info)
{
	ks_stdptr<wpsoldapi::Borders> borders;
	HRESULT hr = selection->get_Borders(&borders);
	if (FAILED(hr) || !borders)
		return false;

	setBordersStyle(borders, info);
	return true;
}

void KEmphasizeHandler::setBordersStyle(wpsoldapi::Borders* borders, const KEmphasizeStyleInfo& info)
{
	foreach(KBorderStyleInfo bInfo, info.borderStyleLst.values())
		setBorderStyle(borders, bInfo);
}

////////////////////////////////////////////////////////////////////////////

///////////////////////////KEmphasizeBordersHanlder/////////////////////////
KEmphasizeBordersHanlder::KEmphasizeBordersHanlder()
{

}

KEmphasizeBordersHanlder::~KEmphasizeBordersHanlder()
{

}

KEmphasizeStyleInfo KEmphasizeBordersHanlder::getTableStyleToApply(const KEmphasizeStyleInfo& originInfo)
{
	KEmphasizeStyleInfo ret;
	ks_stdptr<wpsoldapi::Shading> spCellsShading;
	KEmphasizeTableHelper::getShading(&spCellsShading);
	if (!spCellsShading)
		return ret;

	drawing::Color color;
	color.setRgb(originInfo.backgroundColor.rgba());
	QColor qCol(KEmphasizeTableHelper::getColorGray(color, 5));
	ret.borderStyleLst.insert(wpsBorderLeft, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth225pt, wpsBorderLeft, qCol));
	ret.borderStyleLst.insert(wpsBorderTop, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth225pt, wpsBorderTop, qCol));
	ret.borderStyleLst.insert(wpsBorderRight, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth225pt, wpsBorderRight, qCol));
	ret.borderStyleLst.insert(wpsBorderBottom, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth225pt, wpsBorderBottom, qCol));
	ret.backgroundColor = QColor(0xffffff);
	ret.fontColor = qCol;
	ret.bBoldFont = true;
	ret.bValid = true;

	return ret;
}

bool KEmphasizeBordersHanlder::_emphasizeTable(const KEmphasizeStyleInfo& info)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = KEmphasizeTableHelper::getCurSelection();
	if (!spApiSelection)
		return false;

	KEmphasizeTableHelper::setSelFontStyle(spApiSelection, info.fontColor, info.bBoldFont);
	return emphasizeBorders(spApiSelection, info);
}
////////////////////////////////////////////////////////////////////////////

///////////////////////////KEmphasizeBtmBorderHanlder///////////////////////
KEmphasizeBtmBorderHanlder::KEmphasizeBtmBorderHanlder()
{

}

KEmphasizeBtmBorderHanlder::~KEmphasizeBtmBorderHanlder()
{

}

KEmphasizeStyleInfo KEmphasizeBtmBorderHanlder::getTableStyleToApply(const KEmphasizeStyleInfo& originInfo)
{
	KEmphasizeStyleInfo ret;
	ks_stdptr<wpsoldapi::Shading> spCellsShading;
	KEmphasizeTableHelper::getShading(&spCellsShading);
	if (!spCellsShading)
		return ret;

	drawing::Color color;
	color.setRgb(originInfo.backgroundColor.rgba());
	QColor qCol(KEmphasizeTableHelper::getColorGray(color, 4));
	ret.borderStyleLst.insert(wpsBorderBottom, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth225pt, wpsBorderBottom, qCol)); 
	ret.borderStyleLst.insert(wpsBorderBottom, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth100pt, wpsBorderBottom, qCol));
	ret.borderStyleLst.insert(wpsBorderBottom, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth100pt, wpsBorderBottom, qCol));
	ret.borderStyleLst.insert(wpsBorderBottom, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth100pt, wpsBorderBottom, qCol));
	ret.fontColor = qCol;
	ret.backgroundColor = QColor(KEmphasizeTableHelper::getColorGray(color, 1));
	ret.bBoldFont = true;
	ret.bValid = true;

	return ret;
}

bool KEmphasizeBtmBorderHanlder::_emphasizeTable(const KEmphasizeStyleInfo& info)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = KEmphasizeTableHelper::getCurSelection();
	if (!spApiSelection)
		return false;

	KEmphasizeTableHelper::setSelFontStyle(spApiSelection, info.fontColor, info.bBoldFont);
	return emphasizeBorder(spApiSelection,info);
}

bool KEmphasizeBtmBorderHanlder::emphasizeBorder(wpsoldapi::Selection* selection, const KEmphasizeStyleInfo& info)
{
	ks_stdptr<wpsoldapi::Borders> borders;
	HRESULT hr = selection->get_Borders(&borders);
	if (FAILED(hr) || !borders)
		return false;

	if (info.borderStyleLst.contains(wpsBorderBottom))
		setBorderStyle(borders, info.borderStyleLst[wpsBorderBottom]);

	return true;
}

////////////////////////////////////////////////////////////////////////////

///////////////////////////KEmphasizeFillHanlder////////////////////////////
KEmphasizeFillHanlder::KEmphasizeFillHanlder()
{

}

KEmphasizeFillHanlder::~KEmphasizeFillHanlder()
{

}

KEmphasizeStyleInfo KEmphasizeFillHanlder::getTableStyleToApply(const KEmphasizeStyleInfo& originInfo)
{
	KEmphasizeStyleInfo ret;
	ks_stdptr<wpsoldapi::Shading> spCellsShading;
	KEmphasizeTableHelper::getShading(&spCellsShading);
	if (!spCellsShading)
		return ret;

	drawing::Color color;
	color.setRgb(originInfo.backgroundColor.rgba());
	ret.fontColor = QColor(0xffffff);
	ret.borderStyleLst.insert(wpsBorderLeft, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth100pt, wpsBorderLeft, ret.fontColor));
	ret.borderStyleLst.insert(wpsBorderTop, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth100pt, wpsBorderTop, ret.fontColor));
	ret.borderStyleLst.insert(wpsBorderRight, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth100pt, wpsBorderRight, ret.fontColor));
	ret.borderStyleLst.insert(wpsBorderBottom, KBorderStyleInfo(wpsLineStyleSingle, wpsLineWidth100pt, wpsBorderBottom, ret.fontColor));
	ret.backgroundColor = QColor(KEmphasizeTableHelper::getColorGray(color, 4));
	ret.bBoldFont = true;
	ret.bValid = true;

	return ret;
}

bool KEmphasizeFillHanlder::_emphasizeTable(const KEmphasizeStyleInfo& info)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = KEmphasizeTableHelper::getCurSelection();
	if (!spApiSelection)
		return false;

	KEmphasizeTableHelper::setSelFontStyle(spApiSelection, info.fontColor, info.bBoldFont);
	return emphasizeFill(spApiSelection, info);
}
////////////////////////////////////////////////////////////////////////////

///////////////////////////KEmphasizeNoneHanlder////////////////////////////
KEmphasizeNoneHandler::KEmphasizeNoneHandler(bool bHor)
	:m_bHor(bHor)
{

}

KEmphasizeNoneHandler::~KEmphasizeNoneHandler()
{

}

bool KEmphasizeNoneHandler::emphasizeTable(const KEmphasizeInfo& emInfo, KEmphasizeIntersectCellInfo& cellInfo,
	KEmphasizeStyleInfo& targetStyle)
{
	targetStyle.bValid = false;
	if (!emInfo.bValid)
		return true;

	if (!_emphasizeTable(emInfo.originStyle))
		return false;

	if (!_emphasizeColHeaderCell(emInfo))
		return false;

	if (cellInfo.row != -1 && cellInfo.column != -1)
	{
		if (KEmphasizeTableHelper::selectCellWithIndex(cellInfo.row, cellInfo.column))
		{
			if (m_bHor)
			{
				if (cellInfo.colStyle.bValid)
					_emphasizeTable(cellInfo.colStyle);

				cellInfo.colStyle.bValid = false;
			}
			else
			{
				if (cellInfo.rowStyle.bValid)
					_emphasizeTable(cellInfo.rowStyle);

				cellInfo.rowStyle.bValid = false;
			}
		}

		cellInfo.bValid = false;
	}

	if (m_bHor)
	{
		cellInfo.row = -1;
	}
	else
	{
		cellInfo.column = -1;
	}

	return true;
}

KEmphasizeStyleInfo KEmphasizeNoneHandler::getTableStyleToApply(const KEmphasizeStyleInfo& originInfo)
{
	return KEmphasizeStyleInfo();
}

bool KEmphasizeNoneHandler::_emphasizeTable(const KEmphasizeStyleInfo& info)
{
	if (!KEmphasizeFillHanlder::_emphasizeTable(info))
		return false;

	if (!KEmphasizeBordersHanlder::_emphasizeTable(info))
		return false;

	return true;
}

bool KEmphasizeNoneHandler::_emphasizeColHeaderCell(const KEmphasizeInfo& emInfo)
{
	if (!emInfo.columnHeaderCellStyle.bValid)
		return true;

	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return false;

	ks_stdptr<wpsoldapi::Cell> spCell;
	hr = spTable->Cell(1, emInfo.emphasizeNum, &spCell);
	if (FAILED(hr) || !spTable)
		return false;

	spCell->Select();
	return _emphasizeTable(emInfo.columnHeaderCellStyle);
}

////////////////////////////////////////////////////////////////////////////

///////////////////////////KTableEmphasizer/////////////////////////////////
KTableEmphasizer::KTableEmphasizer()
	: m_emphasizeType(TET_Invalid)
	, m_emphasizeNum(0)
	, m_rowCnt(0)
	, m_colCnt(0)
	, m_selectedRow(0)
	, m_selectedCol(0)
{

}

KTableEmphasizer::~KTableEmphasizer()
{
	resetHandler();
}

void KTableEmphasizer::addHandler(KEmphasizeHandler* handler)
{
	m_handlers.append(handler);
}

void KTableEmphasizer::resetHandler()
{
	QList<KEmphasizeHandler*>::iterator iter = m_handlers.begin();
	while (iter != m_handlers.end())
	{
		if ((*iter))
		{
			delete (*iter);
		}
		iter = m_handlers.erase(iter);
	}
}

void KTableEmphasizer::setEmphasizeInfo(const KTableEmphasizeType type, const int num)
{
	m_emphasizeType = type;
	m_emphasizeNum = num;
	resetHandler();
	createHandler(type);
}

void KTableEmphasizer::setEmphaiszeInfo(const bool bClear)
{
	m_emInfo.emphasizeNum = m_emphasizeNum;
	m_emInfo.emphasizeType = m_emphasizeType;
	QString strEmInfo = KEmphasizeTableHelper::readEmphasizeInfo();
	QString finalStr;
	if (createEmphasizeStr(strEmInfo, finalStr, bClear))
		KEmphasizeTableHelper::writeEmphasizeInfo(finalStr);
}

bool KTableEmphasizer::updateRowColInfo()
{
	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return false;

	return KEmphasizeTableHelper::getTableColRowInfo(spTable, m_rowCnt, m_colCnt);
}

void KTableEmphasizer::collapseSelection()
{
	if (m_selectedCol > 0 && m_selectedCol <= m_colCnt
		&& m_selectedRow > 0 && m_selectedRow < m_rowCnt)
	{
		KEmphasizeTableHelper::selectCellWithIndex(m_selectedRow, m_selectedCol);
	}

	ks_stdptr<wpsoldapi::Selection> spApiSelection = KEmphasizeTableHelper::getCurSelection();
	if (spApiSelection)
		spApiSelection->Collapse(wpsCollapseStart);
}

bool KTableEmphasizer::emphasizeTable(const KEmphasizeIntersectCellInfo& intersectCellInfo)
{
	if (m_emphasizeNum <= 0)
	{
		if (!getSelectCellIndex(m_emphasizeNum))
			return false;
	}

	getSelectCellInfo();
	if (!updateRowColInfo())
		return false;

	IKTransactionTool* pTransTool = KDocerUtils::getActiveTransTool();
	if (!pTransTool)
		return false;
	pTransTool->StartTrans();

	m_intersectCellInfo = intersectCellInfo;
	bool justClearEmphasize = true;
	bool bChanged = false;
	bool bSuccess = false;
	foreach(KEmphasizeHandler * handler, m_handlers)
	{
		if (dynamic_cast<KEmphasizeNoneHandler*>(handler))
		{
			isEmphasizeValid();
			continue;	//屏蔽取消强调功能

			if (!selectRowOrCol(m_emInfo.emphasizeNum))
				break;

			bSuccess = false;
		}
		else
		{
			if (!getOriginStyleInfo())
				break;

			getIntersectCellStyleInfo();
			if (!selectRowOrCol(m_emphasizeNum))
				break;

			justClearEmphasize = false;
			bSuccess = false;
		}

		KEmphasizeStyleInfo target;
		if (!handler->emphasizeTable(m_emInfo, m_intersectCellInfo, target))
			break;

		bChanged = true;
		bSuccess = true;
	}

	if (bChanged)
		setEmphaiszeInfo(justClearEmphasize);

	collapseSelection();
	if (bSuccess)
	{
		pTransTool->CommitTrans(krt::utf16(KTableSelectionNotify::tr("emphasize")), ksoCommit, false);
	}
	else
	{
		pTransTool->Rollback();
	}
	
	return bSuccess;
}

bool KTableEmphasizer::selectRowOrCol(int index)
{
	return false;
}

bool KTableEmphasizer::getSelectCellIndex(int& index)
{
	return false;
}

bool KTableEmphasizer::isEmphasizeValid()
{
	return false;
}

KEmphasizeIntersectCellInfo KTableEmphasizer::getIntersectCellInfo()
{
	return m_intersectCellInfo;
}

void KTableEmphasizer::createHandler(const KTableEmphasizeType type)
{

}

bool KTableEmphasizer::createEmphasizeStr(const QString& origin, QString& strInfo, const bool bClear)
{
	return false;
}

bool KTableEmphasizer::getOriginStyleInfo()
{
	if (!selectCell())
		return false;

	m_emInfo.originStyle.borderStyleLst.clear();
	if (!getCurrentUsedStyleInfo(m_emInfo.originStyle))
		return false;

	m_emInfo.bValid = true;
	return true;
}

bool KTableEmphasizer::getIntersectCellStyleInfo()
{
	return false;
}

void KTableEmphasizer::updateIntersectCellStyleInfo(const KEmphasizeStyleInfo& styleInfo)
{

}

bool KTableEmphasizer::getCurrentUsedStyleInfo(KEmphasizeStyleInfo& styleInfo)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = KEmphasizeTableHelper::getCurSelection();
	if (!spApiSelection)
		return false;

	if (!KEmphasizeTableHelper::getSelCellsFillColor(spApiSelection, styleInfo.backgroundColor))
		return false;

	if (!KEmphasizeTableHelper::getSelFontStyle(spApiSelection, styleInfo.fontColor, styleInfo.bBoldFont))
		return false;

	if (!KEmphasizeTableHelper::getSelBorderStyle(spApiSelection, styleInfo.borderStyleLst))
		return false;

	styleInfo.bValid = true;
	return true;
}

void KTableEmphasizer::getSelectCellInfo()
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = KEmphasizeTableHelper::getCurSelection();
	if (!spApiSelection)
		return;

	ks_stdptr<wpsoldapi::Cells> spCells;
	HRESULT hr = spApiSelection->get_Cells(&spCells);
	if (FAILED(hr) || !spCells)
		return;

	ks_stdptr<wpsoldapi::Cell> spCell;
	hr = spCells->Item(1, &spCell);
	if (FAILED(hr) || !spCell)
		return;

	hr = spCell->get_RowIndex((long*)&m_selectedRow);
	if (FAILED(hr))
		m_selectedRow = 0;

	hr = spCell->get_ColumnIndex((long*)&m_selectedCol);
	if (FAILED(hr))
		m_selectedCol = 0;
}

bool KTableEmphasizer::selectCell()
{
	return false;
}

const KEmphasizeInfo& KTableEmphasizer::getEmphasizeInfo()
{
	return m_emInfo;
}

////////////////////////////////////////////////////////////////////////////

///////////////////////////KTableRowEmphasizer//////////////////////////////
KTableRowEmphasizer::KTableRowEmphasizer()
{

}

KTableRowEmphasizer::~KTableRowEmphasizer()
{

}

bool KTableRowEmphasizer::selectRowOrCol(int index)
{
	if (index <= 0)
		return false;

	return selectRow(index);
}

bool KTableRowEmphasizer::getSelectCellIndex(int& index)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = KEmphasizeTableHelper::getCurSelection();
	if (!spApiSelection)
		return false;

	ks_stdptr<wpsoldapi::Cells> spCells;
	HRESULT hr = spApiSelection->get_Cells(&spCells);
	if (FAILED(hr) || !spCells)
		return false;

	ks_stdptr<wpsoldapi::Cell> spCell;
	hr = spCells->Item(1, &spCell);
	if (FAILED(hr) || !spCell)
		return false;

	hr = spCell->get_RowIndex((long*)&index);
	if (FAILED(hr))
	{
		index = 0;
		return false;
	}

	return true;
}

bool KTableRowEmphasizer::isEmphasizeValid()
{
	m_emInfo.bValid = false;
	QJsonObject originObj = JsonHelper::convertStringToQJson(KEmphasizeTableHelper::readEmphasizeInfo());
	if (originObj.contains(strIntersectCellInfo))
		m_intersectCellInfo = KEmphasizeIntersectCellInfo::fromJsonObject(originObj[strIntersectCellInfo].toObject());

	if (m_intersectCellInfo.column == -1)
	{
		if (originObj.contains(strColumn))
		{
			QJsonObject colInfo = originObj[strColumn].toObject();
			if (colInfo.contains(strEmphasizeNum))
				m_intersectCellInfo.column = colInfo.value(strEmphasizeNum).toInt(-1);
		}
	}

	if (!originObj.contains(strRow))
		return false;

	m_emInfo = KEmphasizeInfo::fromJsonObject(originObj[strRow].toObject());
	return m_emInfo.bValid;
}

bool KTableRowEmphasizer::getIntersectCellStyleInfo()
{
	if (m_intersectCellInfo.column == -1)
		return true;

	m_intersectCellInfo.row = m_emphasizeNum;
	m_intersectCellInfo.colStyle.borderStyleLst.clear();
	m_intersectCellInfo.bValid = true;
	if (KEmphasizeTableHelper::selectCellWithIndex(m_emphasizeNum, m_intersectCellInfo.column))
		return getCurrentUsedStyleInfo(m_intersectCellInfo.colStyle);

	return true;
}

void KTableRowEmphasizer::updateIntersectCellStyleInfo(const KEmphasizeStyleInfo& styleInfo)
{
	m_intersectCellInfo.rowStyle = styleInfo;
	switch (m_emphasizeType)
	{
	case TET_TableBorder:
	{
		QList<WpsBorderType> dealBorderTypes;
		if (m_intersectCellInfo.column == 1)
		{
			dealBorderTypes.append(wpsBorderRight);
		}
		else if (m_intersectCellInfo.column == m_colCnt)
		{
			dealBorderTypes.append(wpsBorderLeft);
		}
		else
		{
			dealBorderTypes.append(wpsBorderLeft);
			dealBorderTypes.append(wpsBorderRight);
		}

		foreach(WpsBorderType item, dealBorderTypes)
		{
			m_intersectCellInfo.rowStyle.borderStyleLst.remove(item);
			if (m_emInfo.originStyle.borderStyleLst.contains(item))
				m_intersectCellInfo.rowStyle.borderStyleLst[item] = m_emInfo.originStyle.borderStyleLst[item];
		}

		m_intersectCellInfo.rowStyle.backgroundColor = m_emInfo.originStyle.backgroundColor;
		break;
	}

	case TET_TableBtmBorder:
	{
		QList<WpsBorderType> dealBorderTypes = { wpsBorderLeft, wpsBorderRight , wpsBorderTop };
		foreach(WpsBorderType item, dealBorderTypes)
		{
			m_intersectCellInfo.rowStyle.borderStyleLst.remove(item);
			if (m_emInfo.originStyle.borderStyleLst.contains(item))
				m_intersectCellInfo.rowStyle.borderStyleLst[item] = m_emInfo.originStyle.borderStyleLst[item];
		}

		m_intersectCellInfo.rowStyle.backgroundColor = m_emInfo.originStyle.backgroundColor;
		break;
	}

	case TET_TableFill:
		m_intersectCellInfo.colStyle.borderStyleLst = m_emInfo.originStyle.borderStyleLst;
		break;
	}

	if (m_emphasizeNum == 1)
		m_intersectCellInfo.rowOriginStyle = m_emInfo.originStyle;
}

void KTableRowEmphasizer::createHandler(const KTableEmphasizeType type)
{
	switch (type)
	{
	case TET_TableBorder:
		addHandler(new KEmphasizeNoneHandler(true));
		addHandler(new KEmphasizeBordersHanlder());
		break;

	case TET_TableBtmBorder:
		addHandler(new KEmphasizeNoneHandler(true));
		addHandler(new KEmphasizeBtmBorderHanlder());
		break;

	case TET_TableFill:
		addHandler(new KEmphasizeNoneHandler(true));
		addHandler(new KEmphasizeFillHanlder());
		break;

	case TET_None:
		addHandler(new KEmphasizeNoneHandler(true));
		break;
	}
}

bool KTableRowEmphasizer::createEmphasizeStr(const QString& origin, QString& strInfo, const bool bClear)
{
	if (!m_emInfo.bValid && !bClear)
		return false;

	QJsonObject originObj;
	if (!origin.isEmpty())
		originObj = JsonHelper::convertStringToQJson(origin);

	if (bClear)
	{
		originObj.remove(strRow);
	}
	else
	{
		originObj[strRow] = m_emInfo.toJsonObject();
	}

	if (m_intersectCellInfo.bValid)
	{
		originObj[strIntersectCellInfo] = m_intersectCellInfo.toJsonObject();
	}
	else
	{
		originObj.remove(strIntersectCellInfo);
	}

	if (!originObj.isEmpty())
		strInfo = JsonHelper::convertQJsonToString(originObj);

	return true;
}

bool KTableRowEmphasizer::selectRow(const int index)
{
	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return false;

	ks_stdptr<wpsoldapi::Rows> spRows;
	hr = spTable->get_Rows(&spRows);
	if (FAILED(hr) || !spRows)
		return false;

	ks_stdptr<wpsoldapi::Row> spRow;
	hr = spRows->Item(index, &spRow);
	if (FAILED(hr) || !spRow)
		return false;

	spRow->Select();
	return true;
}

bool KTableRowEmphasizer::selectCell()
{
	if (m_emphasizeNum <= 0 || m_rowCnt <= 0 || m_colCnt <= 0)
		return false;

	int baseCol = 1;
	if (m_intersectCellInfo.column > 0)
	{
		if (m_colCnt < 3)
		{
			baseCol = m_colCnt;
		}
		else if (m_intersectCellInfo.column + 2 <= m_colCnt)
		{
			baseCol = m_intersectCellInfo.column + 2;
		}
		else if (m_intersectCellInfo.column - 2 >= 1)
		{
			baseCol = m_intersectCellInfo.column - 2;
		}
	}

	return KEmphasizeTableHelper::selectCellWithIndex(m_emphasizeNum, baseCol);
}
////////////////////////////////////////////////////////////////////////////

///////////////////////////KTableColEmphasizer//////////////////////////////
KTableColEmphasizer::KTableColEmphasizer()
{

}

KTableColEmphasizer::~KTableColEmphasizer()
{

}

bool KTableColEmphasizer::selectRowOrCol(int index)
{
	if (index <= 0)
	{
		if (!getSelectCellIndex(index))
			return false;
	}

	return selectCol(index);
}

bool KTableColEmphasizer::getSelectCellIndex(int& index)
{
	ks_stdptr<wpsoldapi::Selection> spApiSelection = KEmphasizeTableHelper::getCurSelection();
	if (!spApiSelection)
		return false;

	ks_stdptr<wpsoldapi::Cells> spCells;
	HRESULT hr = spApiSelection->get_Cells(&spCells);
	if (FAILED(hr) || !spCells)
		return false;

	ks_stdptr<wpsoldapi::Cell> spCell;
	hr = spCells->Item(1, &spCell);
	if (FAILED(hr) || !spCell)
		return false;

	hr = spCell->get_ColumnIndex((long*)&index);
	if (FAILED(hr))
	{
		index = 0;
		return false;
	}

	return true;
}

bool KTableColEmphasizer::isEmphasizeValid()
{
	m_emInfo.bValid = false;
	QJsonObject originObj = JsonHelper::convertStringToQJson(KEmphasizeTableHelper::readEmphasizeInfo());
	if (originObj.contains(strIntersectCellInfo))
		m_intersectCellInfo = KEmphasizeIntersectCellInfo::fromJsonObject(originObj[strIntersectCellInfo].toObject());

	if (m_intersectCellInfo.row == -1)
	{
		if (originObj.contains(strRow))
		{
			QJsonObject rowInfo = originObj[strRow].toObject();
			if (rowInfo.contains(strEmphasizeNum))
				m_intersectCellInfo.row = rowInfo.value(strEmphasizeNum).toInt(-1);
		}
	}

	if (!originObj.contains(strColumn))
		return false;

	m_emInfo = KEmphasizeInfo::fromJsonObject(originObj[strColumn].toObject());
	return m_emInfo.bValid;
}

bool KTableColEmphasizer::getIntersectCellStyleInfo()
{
	if (m_intersectCellInfo.row == -1)
		return true;

	m_intersectCellInfo.column = m_emphasizeNum;
	m_intersectCellInfo.rowStyle.borderStyleLst.clear();
	m_intersectCellInfo.bValid = true;
	if (KEmphasizeTableHelper::selectCellWithIndex(m_intersectCellInfo.row, m_emphasizeNum))
		return getCurrentUsedStyleInfo(m_intersectCellInfo.rowStyle);

	return true;
}

void KTableColEmphasizer::updateIntersectCellStyleInfo(const KEmphasizeStyleInfo& styleInfo)
{
	m_intersectCellInfo.colStyle = styleInfo;
	switch (m_emphasizeType)
	{
	case TET_TableBorder:
	{
		QList<WpsBorderType> dealBorderTypes;
		if (m_intersectCellInfo.row == 1)
		{
			dealBorderTypes.append(wpsBorderBottom);
		}
		else if (m_intersectCellInfo.row == m_rowCnt)
		{
			dealBorderTypes.append(wpsBorderTop);
		}
		else
		{
			dealBorderTypes.append(wpsBorderTop);
			dealBorderTypes.append(wpsBorderBottom);
		}

		foreach(WpsBorderType item, dealBorderTypes)
		{
			m_intersectCellInfo.colStyle.borderStyleLst.remove(item);
			if (m_emInfo.originStyle.borderStyleLst.contains(item))
				m_intersectCellInfo.colStyle.borderStyleLst[item] = m_emInfo.originStyle.borderStyleLst[item];
		}

		m_intersectCellInfo.colStyle.backgroundColor = m_emInfo.originStyle.backgroundColor;
		break;
	}

	case TET_TableFill:
		m_intersectCellInfo.colStyle.borderStyleLst = m_emInfo.originStyle.borderStyleLst;
		break;
	}
}

void KTableColEmphasizer::createHandler(const KTableEmphasizeType type)
{
	switch (type)
	{
	case TET_TableBorder:
		addHandler(new KEmphasizeNoneHandler(false));
		addHandler(new KEmphasizeBordersHanlder());
		break;

	case TET_TableFill:
		addHandler(new KEmphasizeNoneHandler(false));
		addHandler(new KEmphasizeFillHanlder());
		break;

	case TET_None:
		addHandler(new KEmphasizeNoneHandler(false));
		break;
	}
}

bool KTableColEmphasizer::createEmphasizeStr(const QString& origin, QString& strInfo, const bool bClear)
{
	if (!m_emInfo.bValid && !bClear)
		return false;

	QJsonObject originObj;
	if (!origin.isEmpty())
		originObj = JsonHelper::convertStringToQJson(origin);

	if (bClear)
	{
		originObj.remove(strColumn);
	}
	else
	{
		originObj[strColumn] = m_emInfo.toJsonObject();
	}

	if (m_intersectCellInfo.bValid)
	{
		originObj[strIntersectCellInfo] = m_intersectCellInfo.toJsonObject();
	}
	else
	{
		originObj.remove(strIntersectCellInfo);
	}

	if (!originObj.isEmpty())
		strInfo = JsonHelper::convertQJsonToString(originObj);

	return true;
}

bool KTableColEmphasizer::getOriginStyleInfo()
{
	if (!KTableEmphasizer::getOriginStyleInfo())
		return false;

	if (!KEmphasizeTableHelper::selectCellWithIndex(1, m_emphasizeNum))
		return false;

	m_emInfo.columnHeaderCellStyle.borderStyleLst.clear();
	if (!getHeaderCellStyleInfo(m_emInfo.columnHeaderCellStyle))
		return false;

	m_emInfo.bValid = true;
	return true;
}

bool KTableColEmphasizer::selectCol(const int index)
{
	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return false;

	ks_stdptr<wpsoldapi::Columns> spCols;
	hr = spTable->get_Columns(&spCols);
	if (FAILED(hr) || !spCols)
		return false;

	ks_stdptr<wpsoldapi::Column> spCol;
	hr = spCols->Item(index, &spCol);
	if (FAILED(hr) || !spCol)
		return false;

	spCol->Select();
	return true;
}

bool KTableColEmphasizer::getHeaderCellStyleInfo(KEmphasizeStyleInfo& styleInfo)
{
	if (m_intersectCellInfo.row == 1)
	{
		if (m_intersectCellInfo.rowOriginStyle.bValid)
		{
			m_emInfo.columnHeaderCellStyle = m_intersectCellInfo.rowOriginStyle;
			return true;
		}
	}

	if (!getCurrentUsedStyleInfo(m_emInfo.columnHeaderCellStyle))
		return false;

	if (m_intersectCellInfo.row == 2)
	{
		m_emInfo.columnHeaderCellStyle.borderStyleLst.remove(wpsBorderBottom);
		if (m_emInfo.originStyle.borderStyleLst.contains(wpsBorderBottom))
			m_emInfo.columnHeaderCellStyle.borderStyleLst[wpsBorderBottom] = m_emInfo.originStyle.borderStyleLst[wpsBorderBottom];
	}
	return true;
}

bool KTableColEmphasizer::selectCell()
{
	if (m_emphasizeNum <= 0 || m_rowCnt <= 0 || m_colCnt <= 0)
		return false;

	int baseRow = 2;
	if (m_intersectCellInfo.row > 0)
	{
		if (m_rowCnt <= 3)
		{
			baseRow = m_rowCnt;
		}
		else if (m_intersectCellInfo.row + 2 <= m_rowCnt)
		{
			baseRow = m_intersectCellInfo.row + 2;
		}
		else if (m_intersectCellInfo.row - 2 > 1)
		{
			baseRow = m_intersectCellInfo.row - 2;
		}
	}

	return KEmphasizeTableHelper::selectCellWithIndex(baseRow, m_emphasizeNum);
}
////////////////////////////////////////////////////////////////////////////

///////////////////////////KTableSelectionNotify////////////////////////////
KTableSelectionNotify::KTableSelectionNotify(QObject* parent /*= 0*/)
	:QObject(parent)
	, m_bInited(false)
	, m_bRegistered(false)
{

}

KTableSelectionNotify::~KTableSelectionNotify()
{

}

void KTableSelectionNotify::registerNotifyFilter()
{
	if (!KDocerUtils::getCoreApplication(this))
		return;

	if (!m_bRegistered)
	{
		KDocerUtils::getCoreApplication(this)->RegisterNotifyFilter(this);
		m_bRegistered = true;
	}

	IKDocuments* documents = KDocerUtils::getCoreApplication(this)->GetDocuments();
	if (!documents)
		return;

	documents->RegisterNotifyFilter(this);
	for (int i = 0; i < documents->GetDocumentCount(); i++)
	{
		IKDocument* document = documents->GetDocument(i);
		if (NULL != document)
		{
			ks_stdptr<IKView> spView = document->GetActiveView();
			if (spView)
				spView->RegisterNotifyFilter(this);
		}
	}
}

void KTableSelectionNotify::unRegisterNotifyFilter()
{
	ks_stdptr<IKWpsApplication> pApplication = KDocerUtils::getCoreApplication(this);
	if (!pApplication)
		return;

	if (KDocerUtils::getCoreApplication(this)->GetDocuments())
		KDocerUtils::getCoreApplication(this)->GetDocuments()->UnRegisterNotifyFilter(this);

	if (m_bRegistered)
	{
		pApplication->UnRegisterNotifyFilter(this);
		m_bRegistered = false;
	}
}

STDMETHODIMP_(BOOL) KTableSelectionNotify::OnFilterNotify(ksoNotify* ne)
{
	if (!ne)
		return TRUE;
	switch (ne->notify)
	{
	case ksoWpsAppNotify::ksoWpsBeforeWindowSelectionChange:
		emit sigSelectionChanged(false);
		break;
	case ksoWpsMainWindowNotify::ksoWpsMainWinCompareSideBySide:
		emit sigSelectionChanged(false);
		break;
	case ksoNotify::ksoAddItem:
	{
		IKDocument* pDoc = ks_stdptr<IKDocument>(ne->coreObj);
		if (!pDoc)
			return TRUE;
		ks_stdptr<IKView> spView = pDoc->GetActiveView();
		if (spView)
			spView->RegisterNotifyFilter(this);
		break;
	}
	case ksoNotify::ksoRemoveItem:
	{
		IKDocument* pDoc = ks_stdptr<IKDocument>(ne->coreObj);
		if (!pDoc)
			return TRUE;
		ks_stdptr<IKView> spView = pDoc->GetActiveView();
		if (spView)
			spView->UnRegisterNotifyFilter(this);
		break;
	}
	case ksoNotify::ksoTransaction:
	{
		emit sigSelectionChanged(true);
		break;
	}
	default:
		break;
	}

	return TRUE;
}

void KTableSelectionNotify::init()
{
	connectCoreSignals();
}

void KTableSelectionNotify::onCoreInited()
{
	if (!m_bInited)
	{
		registerNotifyFilter();
		m_bInited = true;
		connect(kxApp, SIGNAL(aboutToQuit()),
			this, SLOT(onDestroyed()), Qt::UniqueConnection);
	}
}

void KTableSelectionNotify::onDestroyed()
{
	unRegisterNotifyFilter();
}

void KTableSelectionNotify::connectCoreSignals()
{
	if (kxApp->isCoreInited())
	{
		onCoreInited();
	}
	else
	{
		connect(kxApp, SIGNAL(coreInited()), this, SLOT(onCoreInited()));
	}
}
