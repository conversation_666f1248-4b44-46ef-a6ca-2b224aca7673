﻿#pragma once
#include <kcomctl/kgallerymodelitem.h>
#include "kxonlinetablestyledefine.h"
#include "kdocerresnetwork/kdocerresnetwork.h"


#include <kxshare/kxcommands.h>
class KxDocerTableStyleGalleryView : public KGalleryView
{
	Q_OBJECT

public:
	KxDocerTableStyleGalleryView(KGalleryAbstractModel* model, QWidget* parent, KGalleryView::ViewType viewType = KGalleryView::COMPLETE);

signals:
	void leaveOnlineResource();
	void sigItemPress(int);
	void sigItemClick(int);

protected:
	virtual void leaveEvent(QEvent*);
	virtual void mousePressEvent(QMouseEvent*) override;
	virtual void mouseReleaseEvent(QMouseEvent*) override;

	int hitTest(const QPoint& viewPos);
protected slots:
	void onIndexHovered(int idx);
	void onLeaveHoverTimerOut();

private:
	QTimer m_leaveHoverTimer;

};

/////////////////////////////////////////////////////////////////////////
class KxDocerTableStyleGalleryModel : public KGalleryAbstractModel
{
	Q_OBJECT
public:
	enum State { Loading = 0, GalleryItem, NoResource, NoInternet, InternetTimeout };

	KxDocerTableStyleGalleryModel(
		IKApplication* coreApp,
		QObject* parent, 
		KCommand* cmd,
		int activeGalleryItemCnt,
		const QString& cmdName,
		bool bInsert);

protected:
	virtual bool prepareItems() override;
	void setState(State state);
	void initGalleryItem(const QList<KTableStyleResourceInfo>& resourceList);

signals:
	void stateChanged(int state);
	void sigDownloadError(const KOTSResourceInfo& resourceInfo);

private:
	int m_state = 0;
	int m_activeGalleryItemCnt = 0;
	int m_headerIdx = -1;
	bool m_bRequesting = false;
	bool m_bInsert = false;
	QString m_cmdName;
	KCommand* m_cmd;
};

/////////////////////////////////////////////////////////////////////////
class KxDocerTableStyleGalleryItem : public KGalleryModelAbstractItem
{
	Q_OBJECT
public:
	enum LoadState {
		IconLoading = 0,
		IconLoadError,
		IconReloading,
		IconLoadFinish,
		ResourceLoading,
		ResourceLoadFinish
	};

	KxDocerTableStyleGalleryItem(
		KGalleryAbstractModel* model, 
		KTableStyleResourceInfo resourceInfo,
		const drawing::Color& color, 
		KCommand* tableStyleCmd, 
		const QString& cmdName, 
		bool bInsert,
		ThumbType thumbType);
	virtual ~KxDocerTableStyleGalleryItem();

	void setLoadState(LoadState state);

	virtual QSize sizeHint() const override;

	virtual bool isTriggerable() const override;
	virtual bool isExpanding(KGalleryView::ViewType type) const override;
	virtual void timerEvent(QTimerEvent* e) override;
	virtual bool hitTestSubIcon(const QPoint& itemPos, const QPoint& mousePos) override;

	virtual void drawItem(QPainter& dc, const QRect& rc, KGalleryView::ViewType type) const override;

	virtual void drawItem(QPainter& dc, const QRect& rc) const;
	virtual void drawPreview(QPainter& dc, const QRect& rc) const;
	virtual void drawHoverBackground(QPainter& dc, const QRect& rc) const;
	virtual void drawLoading(QPainter& dc, const QRect& rc) const;
	virtual void drawRetry(QPainter& dc, const QRect& rc) const;
	virtual void drawVipIcon(QPainter& dc, const QRect& rc) const;

	void hover();
	void setLoading(bool bLoading);
	virtual void reportDisplayInfo();
signals:
	void insertOnlineResource(const KOTSResourceInfo& info, const ReportInfo& reportInfo, const QString& filePath, bool hover);
	void sigDownloadError(const KOTSResourceInfo& resourceInfo);
	void sigLeavePreview();

protected:
	ReportInfo getReportInfo(bool bClicked);
	KOTSResourceInfo getResourceInfo(bool bHover);
	virtual TableDownloadReportInfo getTableDownloadInfo();
	virtual void getCustomResourceReportInfo(QHash<QString, QString>& args);
	bool getHasMultiChildTable();
	QRect getDrawRect(const QRect& rct) const;

	virtual bool isClickable() const override;
	virtual void click() override;
	virtual void downloadResource();

public slots:
	virtual void changeIconColor(const drawing::Color& color);
	virtual void onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);
	
protected:
	KGalleryAbstractModel* m_model = nullptr;
	QString m_cmdName;
	QString m_resourceUrl;
	KTableStyleResourceInfo m_resourceInfo;
	ThumbType m_thumbType = ThumbType::Small;

	QIcon m_icon;
	drawing::Color m_currentColor;
	LoadState m_loadState = IconLoading;
	int m_loadingRotation = 0;
	bool m_bRetryHover = false;
	bool m_bVipIconHover = false;
	bool m_bInsert = false;
	int m_loadingTimerId = -1;
	KCommand* m_featureCmd = nullptr;
};