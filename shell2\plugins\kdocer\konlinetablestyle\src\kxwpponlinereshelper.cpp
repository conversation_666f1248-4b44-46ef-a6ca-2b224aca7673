#include "stdafx.h"
#include "kxonlinereshelper.h"
#include <kso/framework/api/wppapi_old.h>
using namespace wppoldapi;

namespace KxOnlineTableResHelper
{
	IKView* _getCoreView(QObject* host)
	{
		KxMainWindow* win = dynamic_cast<KxMainWindow*>(host);
		if (!win)
		{
			return NULL;
		}
		return win->getActiveCoreView();
	}

	bool _getShapeRange(QObject* host, KsoShapeRange** pp)
	{
		IKView* coreView = _getCoreView(host);
		if (!coreView)
		{
			return false;
		}
		ks_stdptr<IKSelection> spSelection = coreView->GetSelection();
		if (!spSelection)
		{
			return false;
		}
		VARIANT_BOOL bHasChildShapeRange = VARIANT_FALSE;
		spSelection->HasLeafChildShapeRange(&bHasChildShapeRange);

		ks_stdptr<KsoShapeRange> spShapeRange;
		if (bHasChildShapeRange == VARIANT_TRUE)
			spSelection->GetLeafChildShapeRange(&spShapeRange);
		else
			spSelection->GetLeafShapeRange(&spShapeRange);
		if (spShapeRange)
		{
			*pp = spShapeRange.detach();
			return true;
		}
		return false;
	}

	bool wppHasActiveTable()
	{
		KxMainWindow* mainWnd = kxApp->currentMainWindow();
		if (!mainWnd)
		{
			return false;
		}
		ks_stdptr<KsoShapeRange> spShapeRange;
		if (!_getShapeRange(mainWnd, &spShapeRange))
		{
			return false;
		}
		KsoShapeType type;
		HRESULT hr = spShapeRange->get__Type(&type);
		if (FAILED(hr))
			return false;
		int nCount = 0;
		hr = spShapeRange->get_Count(&nCount);
		return SUCCEEDED(hr) && type == ksoTable && nCount == 1;
		return true;
	}

	void wppUndo(int setup)
	{
		ks_stdptr<_Application> pApp = kxApp->coreApplication();
		if (pApp == NULL)
			return;
		ks_stdptr<_Presentation> spActivePres;
		pApp->get_ActivePresentation(&spActivePres);
		if (spActivePres == NULL)
			return;
		if (!wppHasActiveTable())
		{
			return;
		}
		spActivePres->Undo(1);
	}

	KxView* wppGetView(int paneID, QObject* obj)
	{
		if (!obj)
			return nullptr;

		KxMainWindow* mainWnd = kxApp->findRelativeMainWindowX(obj);
		if (!mainWnd)
			return nullptr;

		IKMainWindow* coreMainWnd = mainWnd->coreMainWindow();
		if (!coreMainWnd)
			return nullptr;

		IKWindow* window = coreMainWnd->GetActiveWindow();
		if (!window || !mainWnd->currentSubWindow())
			return nullptr;

		ks_stdptr<IKWppWindow> wppWindow = window;
		if (!wppWindow)
			return nullptr;

		IKWppView* wppView = wppWindow->GetViewByID(PANEID(paneID));
		if (!wppView)
			return nullptr;

		return static_cast<KxView*>(wppView->Get_Canvas());
	}

	void wppActiveNormalView(QObject* obj)
	{
		KxView* view = wppGetView(PANEID_NORMAL_SLIDE, obj);
		if (view && view->coreView())
			view->coreView()->_Active();
	}

	void getCurSelectSlide(wppoldapi::_Slide** slide)
	{
		IKApplication* pApp = kxApp->coreApplication();
		if (!pApp)
			return;

		ks_stdptr<_Application> spApiApp = pApp;
		if (!spApiApp)
			return;

		ks_stdptr<DocumentWindow> spWindow;
		HRESULT hr = spApiApp->get_ActiveWindow(&spWindow);
		if (FAILED(hr) || !spWindow)
			return;

		ks_stdptr<Selection> spSelection;
		hr = spWindow->get_Selection(&spSelection);
		if (FAILED(hr) || !spSelection)
			return;
		ks_stdptr<IKSelectionInfo> spSelectionInfo = spSelection;

		ks_stdptr<SlideRange> spSlideRange;
		spSelectionInfo->GetSlideRange(&spSlideRange);
		if (!spSlideRange)
			return;

		long nCnt = 0;
		spSlideRange->get_Count(&nCnt);
		if (nCnt <= 0)
			return;

		ks_stdptr<_Slide> spSlide;
		spSlideRange->get_Item(KComVariant(nCnt), &spSlide);
		if (!spSlide)
			return;

		*slide = spSlide.detach();
	}

	bool slideHasTable()
	{
		ks_stdptr<_Slide> spSlide;
		getCurSelectSlide(&spSlide);
		if (!spSlide)
			return false;
		ks_stdptr<Shapes> spShapes;
		spSlide->get_Shapes(&spShapes);
		if (!spShapes)
			return false;

		int shapeCnt = 0;
		spShapes->get_Count(&shapeCnt);
		for (int i = 1; i <= shapeCnt; i++)
		{
			ks_stdptr<Shape> spShape;
			spShapes->Item(KComVariant(i), &spShape);
			ks_stdptr<Shape> spKsoShape = spShape;
			if (!spKsoShape)
				continue;

			KsoShapeType type = ksoAutoShape;
			HRESULT hr = spKsoShape->get_Type(&type);
			if (FAILED(hr))
				continue;

			if (type == ksoTable)
				return true;
		}

		return false;
	}

	long getCurSelectSlideId()
	{
		long slideId = 0;
		ks_stdptr<wppoldapi::_Slide> spSlide;
		getCurSelectSlide(&spSlide);
		if (spSlide)
		{
			if (FAILED(spSlide->get_SlideID(&slideId)))
				return 0;
		}

		return slideId;
	}
};