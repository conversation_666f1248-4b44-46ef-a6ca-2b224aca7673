﻿#include "stdafx.h"
#include "kxwpstablebeautifymgr.h"
#include "kxshare/kxapplication.h"
#include "kcomctl/kcommands.h"
#include "kcomctl/kcommand.h"
#include "common/request/network/knetworkrequest.h"
#include "kxonlinereshelper.h"
#include "kso/api/wpsapi_old.h"
#include "kdocertoolkit/kdocerutils.h"
#include "kxtableemphasize.h"
#include "ksolite/kdocer/kdocerresnetwork/kdocernetworkwrapper.h"
#include "kdocerresnetwork/kdocerresnetwork.h"
using namespace kdocerresnetwork;


namespace onekeybeautify
{
	#define CHECK_RET(hr) {{if (FAILED(hr)) {return hr; }}}

	HRESULT getTableColWidthAndRowHeight(QVector<single>& colWidth, QVector<single>& rowHeight)
	{
		ks_stdptr<wpsoldapi::Table> spTable;
		HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
		if (FAILED(hr) || !spTable)
			return E_FAIL;
		ks_stdptr<wpsoldapi::Columns> spColumns;
		hr = spTable->get_Columns(&spColumns);
		CHECK_RET(hr);

		long colCnt;
		hr = spColumns->get_Count(&colCnt);
		colWidth.reserve(colCnt);
		CHECK_RET(hr);
		for (int i = 0; i < colCnt; i++)
		{
			single width;
			ks_stdptr<wpsoldapi::Column> spCol;
			hr = spColumns->Item(i + 1, &spCol);
			CHECK_RET(hr);
			hr = spCol->get_Width(&width);
			CHECK_RET(hr);
			colWidth.push_back(width);
		}

		ks_stdptr<wpsoldapi::Rows> spRows;
		hr = spTable->get_Rows(&spRows);
		CHECK_RET(hr);
		long rowCnt;
		hr = spRows->get_Count(&rowCnt);
		rowHeight.reserve(rowCnt);
		CHECK_RET(hr);
		for (int i = 0; i < rowCnt; i++)
		{
			single height;
			ks_stdptr<wpsoldapi::Row> spRow;
			hr = spRows->Item(i + 1, &spRow);
			CHECK_RET(hr);
			hr = spRow->get_Height(&height);
			CHECK_RET(hr);
			rowHeight.push_back(height);
		}
		return S_OK;
	}
	bool isTableHorCenter()
	{    
		ks_stdptr<wpsoldapi::Table> spTable;
		HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
		if (FAILED(hr) || !spTable)
			return false;
		ks_stdptr<wpsoldapi::Rows> spRows;
		hr = spTable->get_Rows(&spRows);
		if (FAILED(hr) || !spRows)
			return false;

		WpsRowAlignment align = wpsAlignRowLeft;
		spRows->get_Alignment(&align);
		if (align != wpsAlignRowCenter)
			return false;

		long prop = -1;
		spRows->get_WrapAroundText(&prop);
		if (prop != 0)
			return false;

		return true;
	}
	bool isAdjustedTable(QVector<single>& preColWidths, QVector<single>& preRowHeights)
	{
		QVector<single> colWidths, rowHeights;
		HRESULT hr = getTableColWidthAndRowHeight(colWidths, rowHeights);
		if (FAILED(hr))
			return false;
		int rowCnt = rowHeights.size(), colCnt = colWidths.size();
		if (colCnt != preColWidths.size() || rowCnt != preRowHeights.size())
			return true;
		for (int i = 0; i < colCnt; i++)
			if (abs(preColWidths[i] - colWidths[i]) > 0.1)
				return true;
		for (int i = 0; i < rowCnt; i++)
			if (abs(preRowHeights[i] - rowHeights[i]) > 0.1)
				return true;
		return false;
	}
}

namespace
{
	const char* const strRow = "row";
	const char* const strColumn = "column";
	const char* const strHorizontal = "horizontal";
	const char* const strVertical = "vertical";
	const char* const strStyleFilename = "style.tablestyle";
	const char* const strData = "data";
	const char* const strResult = "result";
	const char* const strStorageUrl = "storage_url";
	const char* const strStyleId = "styleId";
};

///////////////////////////KTableEmphasizeMgr//////////////////////////////////
bool KTableEmphasizeMgr::emphasizeHorBorders(const int rowIndex /*= 0*/)
{
	return emphasizeTable(m_rowEmphasizer, rowIndex, TET_TableBorder);
}

bool KTableEmphasizeMgr::emphasizeHorBtmBorder(const int rowIndex /*= 0*/)
{
	return emphasizeTable(m_rowEmphasizer, rowIndex, TET_TableBtmBorder);
}

bool KTableEmphasizeMgr::emphasizeHorFill(const int rowIndex /*= 0*/)
{
	return emphasizeTable(m_rowEmphasizer, rowIndex, TET_TableFill);
}

bool KTableEmphasizeMgr::emphasizeHorNone(const int rowIndex /*= 0*/)
{
	return emphasizeTable(m_rowEmphasizer, rowIndex, TET_None);
}

bool KTableEmphasizeMgr::emphasizeVerBorders(const int colIndex /*= 0*/)
{
	return emphasizeTable(m_colEmphasizer, colIndex, TET_TableBorder);
}

bool KTableEmphasizeMgr::emphasizeVerFill(const int colIndex /*= 0*/)
{
	return emphasizeTable(m_colEmphasizer, colIndex, TET_TableFill);
}

bool KTableEmphasizeMgr::emphasizeVerNone(const int colIndex /*= 0*/)
{
	return emphasizeTable(m_colEmphasizer, colIndex, TET_None);
}

QString KTableEmphasizeMgr::getTableSeletedInfo()
{
	ks_stdptr<wpsoldapi::Selection>spSelection = KEmphasizeTableHelper::getCurSelection();
	KsoSelectionType nType;
	ks_stdptr<IKTxSelection> spTxSelection = spSelection;
	if (!spTxSelection)
		return "";

	HRESULT hr = spTxSelection->GetType(&nType);
	if (FAILED(hr))
		return "";

	UINT nMinorType = KsoSelectionTypeMinor(nType);
	if (nMinorType == TxSelectType_TableRow)
		return strRow;

	if (nMinorType == TxSelectType_TableColumn)
		return strColumn;

	return "";
}

QMap<QString, KEmphasizeInfo> KTableEmphasizeMgr::getEmphasizeInfo()
{
	QMap<QString, KEmphasizeInfo> ret;
	if (m_rowEmphasizer)
	{
		if (m_rowEmphasizer->isEmphasizeValid())
			ret.insert(strHorizontal, m_rowEmphasizer->getEmphasizeInfo());
	}

	if (m_colEmphasizer)
	{
		if (m_colEmphasizer->isEmphasizeValid())
			ret.insert(strVertical, m_colEmphasizer->getEmphasizeInfo());
	}

	return ret;
}

bool KTableEmphasizeMgr::getTableInfo(KTableRowColInfo& tbInfo)
{
	ks_stdptr<wpsoldapi::Table> table;
	KEmphasizeTableHelper::getCurSelectTable(&table);
	if (!table)
		return false;

	if (!KEmphasizeTableHelper::getTableColRowInfo(table, tbInfo.rowCnt, tbInfo.colCnt))
		return false;

	if (!KEmphasizeTableHelper::getCurSelectIndex(tbInfo.curSelectRow, tbInfo.curSelectCol))
		return false;

	return true;
}

bool KTableEmphasizeMgr::emphasizeTable(const int index, const int style, const QString& type)
{
	if (type == strHorizontal)
	{
		KTableEmphasizeType emType = (KTableEmphasizeType)style;
		switch (emType)
		{
		case TET_TableBorder:
			return emphasizeHorBorders(index);

		case TET_TableBtmBorder:
			return emphasizeHorBtmBorder(index);

		case TET_TableFill:
			return emphasizeHorFill(index);

		case TET_None:
			return emphasizeHorNone(index);
		}
	}
	else if (type == strVertical)
	{
		KTableEmphasizeType emType = (KTableEmphasizeType)(style - 3);
		switch (emType)
		{
		case TET_TableBorder:
			return emphasizeVerBorders(index);

		case TET_TableFill:
			return emphasizeVerFill(index);

		case TET_None:
			return emphasizeVerNone(index);
		}
	}

	return false;
}

bool KTableEmphasizeMgr::emphasizeTable(KTableEmphasizer* emphasizer, int index, KTableEmphasizeType type)
{
	emphasizer->setEmphasizeInfo(type, index);
	bool ret = emphasizer->emphasizeTable(m_intersectCellInfo);
	m_intersectCellInfo = emphasizer->getIntersectCellInfo();
	return ret;
}

bool KTableEmphasizeMgr::emphasizeNone(const QString& type)
{
	if (type == strHorizontal)
		return emphasizeHorNone();

	if (type == strVertical)
		return emphasizeVerNone();

	return false;
}

bool KTableEmphasizeMgr::isTableValid()
{
	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return false;

	return true;
}

void KTableEmphasizeMgr::clearEmphasizeInfo()
{
	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return;

	QJsonObject originObj = JsonHelper::convertStringToQJson(KEmphasizeTableHelper::readEmphasizeInfo());
	QJsonObject obj;
	if (originObj.contains(strStyleId))
		obj[strStyleId] = originObj.value(strStyleId).toInt(1);

	ks_bstr desc(krt::utf16(JsonHelper::convertQJsonToString(obj)));
	spTable->put_Descr(desc);
}

void KTableEmphasizeMgr::onSelectionChanged(bool bForce)
{
	if (bForce)
	{
		emit sigSelectionChanged();
		return;
	}

	static IKCoreObject* lastSelectObj = nullptr;
	static long rgStart = 0;
	static long rgEnd = 0;

	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
	{
		if (lastSelectObj)
			emit sigSelectionChanged();

		lastSelectObj = nullptr;
		rgStart = 0;
		rgEnd = 0;
		return;
	}
	lastSelectObj = spTable.get();

	ks_stdptr<wpsoldapi::Range> spRange;
	hr = spTable->get_Range(&spRange);
	if (FAILED(hr) || !spRange)
	{
		if (lastSelectObj)
			emit sigSelectionChanged();

		lastSelectObj = nullptr;
		return;
	}

	long start = 0;
	long end = 0;
	spRange->get_Start(&start);
	spRange->get_End(&end);
	if (rgStart != start || rgEnd != end)
	{
		emit sigSelectionChanged();
		rgStart = start;
		rgEnd = end;
	}
}

KTableEmphasizeMgr::KTableEmphasizeMgr()
	: m_rowEmphasizer(new KTableRowEmphasizer())
	, m_colEmphasizer(new KTableColEmphasizer())
	, m_notify(new KTableSelectionNotify(this))
{
	m_notify->init();
	connect(m_notify, SIGNAL(sigSelectionChanged(bool)),
		this, SLOT(onSelectionChanged(bool)));
}

KTableEmphasizeMgr::~KTableEmphasizeMgr()
{
	if (m_colEmphasizer)
	{
		delete m_colEmphasizer;
		m_colEmphasizer = nullptr;
	}

	if (m_rowEmphasizer)
	{
		delete m_rowEmphasizer;
		m_rowEmphasizer = nullptr;
	}
}

KTableEmphasizeMgr* KTableEmphasizeMgr::getInstance()
{
	static KTableEmphasizeMgr ins;
	return &ins;
}
////////////////////////////////////////////////////////////////////////////

///////////////////////////KxWpsOneKeyTableArrangeMgr///////////////////////
KWpsOneKeyTableArrangeMgr::KWpsOneKeyTableArrangeMgr()
	: m_jsApi(nullptr)
{

}

KWpsOneKeyTableArrangeMgr::~KWpsOneKeyTableArrangeMgr()
{

}

KWpsOneKeyTableArrangeMgr* KWpsOneKeyTableArrangeMgr::getInstance()
{
	static KWpsOneKeyTableArrangeMgr instance;
	return &instance;
}

void KWpsOneKeyTableArrangeMgr::oneKeyTableArrange(ksolite::KxCommonJsApi* jsApi)
{
	ks_stdptr<wpsoldapi::Table> spTable;
	HRESULT hr = KEmphasizeTableHelper::getCurSelectTable(&spTable);
	if (FAILED(hr) || !spTable)
		return;

	if (!kxApp || !kxApp->coreApplication() ||
		!KDocerUtils::isWpsApp(kxApp->coreApplication()))
		return;

	KxMainWindow* mainWnd = kxApp->currentMainWindow();
	if (mainWnd)
	{
		IKTransactionTool* pTransTool = KDocerUtils::getActiveTransTool();
		if (!pTransTool)
			return;

		pTransTool->StartTrans();

		setTableAutoFit(spTable);
		setRowHeightFitContent(spTable);
		makeTableHorCenter(spTable);
		m_jsApi = jsApi;

		pTransTool->CommitTrans(krt::utf16(tr("table beautify")), ksoCommit, false);
	}
}

void KWpsOneKeyTableArrangeMgr::makeTableToCenter(wpsoldapi::Table* table)
{
	if (!table)
		return;

	ks_stdptr<wpsoldapi::Rows> spRows;
	HRESULT hr = table->get_Rows(&spRows);
	if (FAILED(hr) || !spRows)
		return;

	spRows->put_RelativeHorizontalPosition(wpsRelativeHorizontalPositionMargin);
	spRows->put_RelativeVerticalPosition(wpsRelativeVerticalPositionParagraph);
	spRows->put_HorizontalPosition(0);
	spRows->put_VerticalPosition(0);
}

void KWpsOneKeyTableArrangeMgr::makeTableHorCenter(wpsoldapi::Table* table)
{
	makeTableToCenter(table);
	if (!table)
		return;

	ks_stdptr<wpsoldapi::Rows> spRows;
	HRESULT hr = table->get_Rows(&spRows);
	if (FAILED(hr) || !spRows)
		return;
	spRows->put_Alignment(wpsAlignRowCenter);
	spRows->put_WrapAroundText(0);
}

void KWpsOneKeyTableArrangeMgr::setRowHeightFitContent(wpsoldapi::Table* table)
{
	if (!table)
		return;

	HRESULT hr = E_FAIL;
	ks_stdptr<wpsoldapi::Rows> spRows;
	hr = table->get_Rows(&spRows);
	if (FAILED(hr) || !spRows)
		return;

	spRows->put_HeightRule(wpsRowHeightAtLeast);
	double rowHeight = 0.01;
	spRows->put_Height(rowHeight);
}

void KWpsOneKeyTableArrangeMgr::setTableAutoFit(wpsoldapi::Table* table)
{
	if (!table)
		return;

	table->AutoFitBehavior(wpsAutoFitContent);
	table->AutoFitBehavior(wpsAutoFitWindow);
}

void KWpsOneKeyTableArrangeMgr::applyTableStyleRes(const QString& filePath)
{
	KOTSResourceInfo resInfo;
	ReportInfo repInfo;
	repInfo.from = "oneKeyBeautify";
	emit applyTableStyle(resInfo, repInfo, filePath, false, m_jsApi);
}

QList<int>& KWpsOneKeyTableArrangeMgr::getStyleIdLst()
{
	static QList<int> styleIdLst = { 0, 20458659, 20393392, 20496621 };
	return styleIdLst;
}

void KWpsOneKeyTableArrangeMgr::setCurStyleIdIndex(const int index)
{
	getStyleIdLst().replace(0, index);
}

int KWpsOneKeyTableArrangeMgr::readCurStyleIdIndex()
{
	QJsonObject originObj = JsonHelper::convertStringToQJson(KEmphasizeTableHelper::readEmphasizeInfo());
	int curId = 1;
	if (originObj.contains(strStyleId))
		curId = originObj.value(strStyleId).toInt(1);

	setCurStyleIdIndex(curId);
	return curId;
}

////////////////////////////////////////////////////////////////////////////