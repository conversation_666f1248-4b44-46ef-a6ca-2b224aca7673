﻿#pragma once

#include <kso/framework/api/wppapi_old.h>
using namespace wppoldapi;

#define KAiBeautify_ChangeTableSkin		krt::kCachedTr("kaiwpp_translate", "change table skin", "KAiBeautify_ChangeTableSkin")
#define KAiBeautify_TableEmphaszie		krt::kCachedTr("kaiwpp_translate", "set table emphasis", "KAiBeautify_TableEmphaszie")

QT_BEGIN_NAMESPACE
class QNetworkReply;
class QNetworkAccessManager;
QT_END_NAMESPACE

class KTableBeautify : public QObject
{
	Q_OBJECT

public:
	KTableBeautify();
	~KTableBeautify();

	static KTableBeautify* instance();
public:
	bool isAvaliable();
	bool isEmptyTable();
	bool getTableInfo(QString* id);
	bool getSkinInfo(int* skinIdx, int* onekeySkinIdx, QString* colorIdx);
	bool getTableInfo(int* rowCnt, int* colCnt, int* curRow, int* curCol);
	bool getEmphasizeInfo(const QString& type, int* pos, int* styleId);
	bool getSelectedCellRange(int* rowBegin, int* rowEnd, int* colBegin, int* colEnd);

	bool onekeyBeautify();
	bool setSkin(int skinIdx, const QString& colorIdx, const QVariantList& rgbs, bool useTrans = true);
	bool setEmphasize(const QString& type, int row, int styleId);
	bool clearEmphasize(const QString& type);
	bool setEmphasizeColor(const QString& color);
	void cancelOnekeyBeautify();
	void notifySelectionTableChanged();

	bool applyFont(const QString& name);
signals:
	void selectionTableChanged();

private:
	bool invokeCommandMethod(const char* member,
		const QGenericArgument& val0 = QGenericArgument(0),
		const QGenericArgument& val1 = QGenericArgument(),
		const QGenericArgument& val2 = QGenericArgument(),
		const QGenericArgument& val3 = QGenericArgument(),
		const QGenericArgument& val4 = QGenericArgument(),
		const QGenericArgument& val5 = QGenericArgument(),
		const QGenericArgument& val6 = QGenericArgument(),
		const QGenericArgument& val7 = QGenericArgument(),
		const QGenericArgument& val8 = QGenericArgument(),
		const QGenericArgument& val9 = QGenericArgument());
	KCommand* getCommand();

private:
	QPointer<KCommand> m_cmd;
};

class KTableBeautifyResMgr : public QObject
{
	Q_OBJECT

public:
	KTableBeautifyResMgr();

	static KTableBeautifyResMgr* instance();

	bool getNextResource(int* index, int* skinIdx, QString* colorIdx, QVariantList* colorRgbs);
	void downloadResources();

public slots:
	void onDownloadTimeout();
	void onDownloadFinished(QNetworkReply* reply);

signals:
	void downloadFailed();
	void downloadSucceeded();

private:
	bool parseResource(const QByteArray& byte);
	QString rmsp();

private:
	struct SkinResource
	{
		int id;
		int skinIdx;
		QVariantList colorRgbs;
	};

	QList<SkinResource> m_resource;

	bool m_isHandled;
	QTimer* m_timer;
	QNetworkAccessManager* m_network;
};

class KTableBeautifyTip;
class KTableOnekeyBeautifyHelper : public QObject
{
	Q_OBJECT

public:
	explicit KTableOnekeyBeautifyHelper(QObject* parent);

	void trigger(const QString& source);

signals:
	void finished(bool success);

private slots:
	void doOnekeyBeautify();
	void doOnekeyBeautifyFailed();

private:
	KTableBeautifyTip* getTipWidget();

private:
	QString m_source;
	QPointer<KTableBeautifyTip> m_tip;
};

class KxQuickHelpBarContainer;
class KPreviewHelper : public QObject
{
public:
	explicit KPreviewHelper(QObject* parent = nullptr);
	~KPreviewHelper();

	static KPreviewHelper* instance();

	bool isInPreview();
	void beginPreview(QWidget* webView, LPCWSTR desc);
	void endPreview(bool isCommit = false);

protected:
	bool eventFilter(QObject* o, QEvent* e) override;

	void blockQhBarUpdate();
	void unblockQhBarUpdate();

private:
	LPCWSTR m_commitDesc;
	KsoTriState m_oldState;
	QPointer<QWidget> m_pWebView;
	ks_stdptr<_Presentation> m_spPres;
	ks_stdptr<IKTransactionTool> m_spTransTool;
	QList<QPointer<KxQuickHelpBarContainer>> m_qhBars;
};

class KTableBeautifyTip : public QWidget
{
	Q_OBJECT

public:
	explicit KTableBeautifyTip(QWidget* parent);

	void showProcessing();
	void showProcessError();
	void showNetworkError();

	void paintEvent(QPaintEvent* e) override;

public slots:
	void cancel();

signals:
	void retryClicked();

private:
	bool isDarkTheme() const;
	QIcon getIcon(const QString& iconName) const;
	QColor getColor(const QString& propName) const;
	QString getRgba(const QString& propName) const;
	QRgb getDarkSkinColor(const QString& propName) const;
	QRgb getWhiteSkinColor(const QString& propName) const;

	QWidget* createProcessingWidget(QWidget* parent) const;
	QWidget* createProcessErrorWidget(QWidget* parent) const;
	QWidget* createNetworkErrorWidget(QWidget* parent) const;
	QLabel* createIconLabel(const QIcon& icon, QWidget* parent) const;
	QLabel* createTextLabel(const QString& text, QWidget* parent) const;
	QPushButton* createLinkButton(const QString& text, QWidget* parent) const;
	QPushButton* createCloseButton(QWidget* parent) const;

private:
	QPointer<QStackedLayout> m_stackedLayout;
};

class KQhPopupHelper : public QObject
{
	Q_OBJECT

public:
	KQhPopupHelper(QWidget* w);

	void trigger();
	bool eventFilter(QObject* o, QEvent* e) override;

signals:
	void aboutPopup();

private slots:
	void onPopup();

private:
	bool update();
	void doPopup();
	bool isPopped(QObject* o);

private:
	QPointer<QWidget> m_widget;
	QElapsedTimer m_elaspedTimer;
};

class KxTriStateButton : public QPushButton
{
public:
	explicit KxTriStateButton(QWidget* parent);

	void setHoverIcon(const QIcon& icon) { m_hoverIcon = icon; }
	void setPressIcon(const QIcon& icon) { m_pressIcon = icon; }
	void setNormalIcon(const QIcon& icon) { m_normalIcon = icon; }

	void paintEvent(QPaintEvent*) override;

private:
	QIcon m_normalIcon;
	QIcon m_hoverIcon;
	QIcon m_pressIcon;
};
