﻿#include "stdafx.h"

#include <kxshare/kxapplication.h>
#include "kaietrecognizedata.h"
#include "kxonlinetablestyledefine.h"
#include <common/identifytable.h>
#include <ksolite/kdocer/kdocercoreitef.h>

#define StrMaxCellCount "maxCellCount"
#define MaxCellCount 5000000

namespace
{
	bool readString(
		const QJsonObject& obj,
		const QString& key,
		QString* value)
	{
		QJsonObject::const_iterator it = obj.constFind(key);
		if (it != obj.constEnd() && it.value().isString())
		{
			*value = it.value().toString();
			return true;
		}

		return false;
	};

	bool readInteger(
		const QJsonObject& obj,
		const QString& key,
		int* value,
		int defaultValue = -1)
	{
		QJsonObject::const_iterator it = obj.constFind(key);
		if (it != obj.constEnd() && it.value().isDouble())
		{
			*value = it.value().toInt(defaultValue);
			return true;
		}

		*value = defaultValue;
		return false;
	};

	bool readStringList(const QJsonObject& obj,
		const QString& key,
		QStringList* list)
	{
		QJsonObject::const_iterator it = obj.constFind(key);
		if (it != obj.constEnd() && it.value().isArray())
		{
			QJsonArray array = it.value().toArray();
			for (int i = 0; i < array.size(); i++)
			{
				list->append(array.at(i).toString());
			}

			return true;
		}

		return false;
	}

	template <class T>
	void parseListObject(const QJsonObject& obj,
		const QString& key,
		T** t)
	{
		QJsonObject::const_iterator it = obj.constFind(key);
		if (it != obj.constEnd() && it.value().isArray())
			*t = new T(it.value().toArray());
	}

	const QString colIndexToName(int col)
	{
		QString r;
		while (col >= 0)
		{
			if (col < 26)
			{
				char c = 'A' + col;
				return c + r;
			}
			else
			{
				int b = col % 26;
				col = (col / 26) - 1;
				char c = 'A' + b;
				r = c + r;
			}
		}

		return r;
	}

	bool isNullTitle(const QString& title)
	{
		if (title.isEmpty())
			return true;
		if (title.trimmed().isEmpty())
			return true;
		if (title.compare("-", Qt::CaseInsensitive) == 0)
			return true;
		if (title.compare("NULL", Qt::CaseInsensitive) == 0)
			return true;

		return false;
	}

	bool isRangeIntersected(const ES_CUBE& cube1, const ES_CUBE& cube2, ES_CUBE* outCube = nullptr)
	{
		QRect rect1(QPoint(cube1.colFrom, cube1.rowFrom), QPoint(cube1.colTo, cube1.rowTo));
		QRect rect2(QPoint(cube2.colFrom, cube2.rowFrom), QPoint(cube2.colTo, cube2.rowTo));
		QRect outRect = rect1.intersected(rect2);
		if (outCube)
		{
			outCube->colFrom = outRect.left();
			outCube->colTo = outRect.right();
			outCube->rowFrom = outRect.top();
			outCube->rowTo = outRect.bottom();
			outCube->sheetFrom = cube1.sheetFrom;
			outCube->sheetTo = cube2.sheetTo;
		}
		return !outRect.isEmpty();
	}

	QPoint calcCell2CubeOffset(const ES_CUBE& cube, int row, int col)
	{
		int offsetX = 0;
		int offsetY = 0;

		if (col < cube.colFrom)
		{
			offsetX = col - cube.colFrom;
		}
		else if (col > cube.colTo)
		{
			offsetX = col - cube.colTo;
		}
		else
		{
			offsetX = 0;
		}

		if (row < cube.rowFrom)
		{
			offsetY = row - cube.rowFrom;
		}
		else if (row > cube.rowTo)
		{
			offsetY = row - cube.rowTo;
		}
		else
		{
			offsetY = 0;
		}

		return QPoint(offsetX, offsetY);
	}

	class KxEtAreaCutSheetEnumAcpt : public ICellValueAcpt {
	public:
		KxEtAreaCutSheetEnumAcpt(std::vector<RANGE> dataTableList) :m_datatableList(dataTableList) {}
		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			for (const auto& item : m_datatableList)
			{
				if (item.Contain(row, col))
					return 0;
			}
			DWORD type = etexec::GetExecTokenMajorType(pToken);
			int length = 0;
			switch (type)
			{
			case etexec::ETP_VSTR:
			{
				alg::const_vstr_token_assist token(pToken);
				length = krt::fromUtf16(token.get_value()).length();
				break;
			}
			case etexec::ETP_VINT:
			case etexec::ETP_VDBL:
			{
				alg::const_vdbl_token_assist token(pToken);
				length = QString::number(token.get_value()).length();
				break;
			}
			}
			if (length > 0)
			{
				bool bHasMacthArea = false;
				for (auto& cube : m_splitArea)
				{
					QPoint offset = calcCell2CubeOffset(cube, row, col);
					if (offset.manhattanLength() > 2 || qAbs(offset.x()) > 1 || qAbs(offset.y()) > 1)
						continue;
					if (offset.x() > 0)
						cube.colTo = col;
					else if (offset.x() < 0)
						cube.colFrom = col;
					if (offset.y() > 0)
						cube.rowTo = row;
					else if (offset.y() < 0)
						cube.rowFrom = row;
					bHasMacthArea = true;
					break;
				}
				if (!bHasMacthArea)
				{
					ES_CUBE cube;
					cube.rowFrom = row;
					cube.rowTo = row;
					cube.colFrom = col;
					cube.colTo = col;
					m_splitArea.append(cube);
				}
			}
			return 0;
		}
		QList<ES_CUBE> cutResult() const { return m_splitArea; }
	private:
		std::vector<RANGE> m_datatableList;
		QList<ES_CUBE> m_splitArea;
	};
}


kaietrecognize::RangeList::RangeList(const QJsonObject& jsonObject, IKWorksheet* pWorkSheet, bool bServiceRecoResult)
	:m_tableCnt(0)
{
	if (!pWorkSheet)
		return;

	if(bServiceRecoResult)
		init(jsonObject, pWorkSheet);
	else
		initByCustomStruct(jsonObject, pWorkSheet);
}

kaietrecognize::RangeList::~RangeList()
{

}

kaietrecognize::TableRangeInfoList& kaietrecognize::RangeList::getTableInfoList()
{
	return m_tableInfoList;
}

void kaietrecognize::RangeList::getTableStructCollectInfo(OUT int& tableCnt, OUT std::vector<TableStructCollectInfo>& tableStructCollectInfoList)
{
	tableCnt = m_tableCnt;
	tableStructCollectInfoList = m_tableStructCollectInfo;
}

void kaietrecognize::RangeList::init(const QJsonObject& jsonObject, IKWorksheet* pWorkSheet)
{
	if (pWorkSheet == nullptr || pWorkSheet->GetSheet() == nullptr)
		return;

	HRESULT hr = E_FAIL;

	IDX iSheet = 0;
	pWorkSheet->GetSheet()->GetIndex(&iSheet);
	QJsonArray tableRangInfoList = jsonObject.value(FatherChildListNode).toArray();
	for (const auto& tableItem : qAsConst(tableRangInfoList))
	{
		QJsonObject tableItemObj = tableItem.toObject();

		QJsonArray totalRangeArea = tableItemObj[FatherRangeNode].toArray();
		QJsonArray childRangeList = tableItemObj[ChildRangeListNode].toArray();
		QJsonArray range2TypeList = tableItemObj[RangeTypeListNode].toArray();
		if(totalRangeArea.isEmpty() || range2TypeList.isEmpty())
			continue;
		ES_CUBE eachZoneRangeCube;
		hr = createRange(totalRangeArea, iSheet, eachZoneRangeCube);
		ParentChildTableInfo eachTableInfoPara;
		if (SUCCEEDED(hr))
		{
			eachTableInfoPara.setWorksheet(pWorkSheet);
			eachTableInfoPara.parentRangeInfo.allRangeInfo = eachZoneRangeCube;
		}
		else
			continue;

		TableStructCollectInfo tableStructCollectInfo;
		for (const auto& typeItem : qAsConst(range2TypeList))
		{
			QJsonArray typeItemArray = typeItem.toArray();
			if (typeItemArray.size() != 2)
				continue;
			Zone eachZone(typeItemArray, iSheet);
			if (eachZone.m_type != Empty)//空白的不处理
			{
				eachTableInfoPara.insertZone(eachZone);
				tableStructCollectInfo.increaseZoneTypeCnt(&eachZone);
			}
		}
		m_tableStructCollectInfo.push_back(tableStructCollectInfo);

		std::vector<RANGE> dataTableRangeList;
		initDataTableRange(pWorkSheet, dataTableRangeList);
		for (const auto& childItem : qAsConst(childRangeList))
		{
			QJsonArray childItemRange = childItem.toArray();
			ES_CUBE childZoneRangeCube;
			hr = createRange(childItemRange, iSheet, childZoneRangeCube);
			if (FAILED(hr))
				continue;
			QVector<ES_CUBE> splitRange;
			splitTableByDataTable(pWorkSheet, dataTableRangeList, childZoneRangeCube, splitRange);
			for (const auto& item : qAsConst(splitRange))
				eachTableInfoPara.insertChildRange(item);
		}

		m_tableInfoList.insertRangeInfo(eachTableInfoPara);
	}
}

void kaietrecognize::RangeList::initByCustomStruct(const QJsonObject& jsonObject, IKWorksheet* pWorkSheet)
{
	if (pWorkSheet == nullptr || pWorkSheet->GetSheet() == nullptr)
		return;

	IDX iSheet = 0;
	pWorkSheet->GetSheet()->GetIndex(&iSheet);
	QJsonArray tableList = jsonObject.value("tableList").toArray();
	if (tableList.isEmpty())
		return;

	QJsonObject fpcCombJsonObj;
	int maxCellCnt = MaxCellCount;
	docer::KDocerCoreLiteInterface* docerCoreLite = docer::KLiteDocerCoreMgr::GetDocerCoreLite();
	if (docerCoreLite && docerCoreLite->isFpcInitFinished())
	{
		fpcCombJsonObj = docerCoreLite->getFpcCombJsonObject(g_strGroupModuleId, g_strNonEmptyCellLimitRecId);
		if (fpcCombJsonObj.contains(StrMaxCellCount))
			maxCellCnt = fpcCombJsonObj.value(StrMaxCellCount).toInt();
	}

	for (const auto& item : qAsConst(tableList))
	{
		ES_CUBE eachZoneRangeCube;
		eachZoneRangeCube.sheetFrom = iSheet;
		eachZoneRangeCube.sheetTo = iSheet;

		QJsonArray tableStart = item["tableStart"].toArray();
		QJsonArray tableEnd = item["tableEnd"].toArray();
		eachZoneRangeCube.rowFrom = tableStart[0].toInt();
		eachZoneRangeCube.rowTo = tableEnd[0].toInt();
		eachZoneRangeCube.colFrom = tableStart[1].toInt();
		eachZoneRangeCube.colTo = tableEnd[1].toInt();
		if (eachZoneRangeCube.rowFrom > eachZoneRangeCube.rowTo
			|| eachZoneRangeCube.colFrom > eachZoneRangeCube.colTo)
			continue;

		int maxRowCnt = maxCellCnt / (eachZoneRangeCube.colTo - eachZoneRangeCube.colFrom + 1);
		int realRowCnt = eachZoneRangeCube.rowTo - eachZoneRangeCube.rowFrom + 1;
		eachZoneRangeCube.rowTo = realRowCnt < maxRowCnt ? eachZoneRangeCube.rowTo : eachZoneRangeCube.rowFrom + maxRowCnt - 1;

		ks_stdptr<etoldapi::Range> spExpRg;
		RANGE rg(eachZoneRangeCube, pWorkSheet->GetSheet()->GetBMP());
		pWorkSheet->GetRangeByData(&rg, &spExpRg);
		int iHeader = app_helper::GuessRangeFilterHeader(spExpRg);

		ES_CUBE titleZoneRangeCube(eachZoneRangeCube);
		titleZoneRangeCube.rowFrom = tableStart[0].toInt() + iHeader;
		titleZoneRangeCube.rowTo = tableStart[0].toInt() + iHeader;
		titleZoneRangeCube.colFrom = tableStart[1].toInt();
		titleZoneRangeCube.colTo = tableEnd[1].toInt();
		if (titleZoneRangeCube.rowTo > eachZoneRangeCube.rowTo)
			continue;

		kaietrecognize::ParentChildTableInfo eachTableInfoPara;
		eachTableInfoPara.setWorksheet(pWorkSheet);
		eachTableInfoPara.parentRangeInfo.allRangeInfo = eachZoneRangeCube;

		if (titleZoneRangeCube.rowTo < eachZoneRangeCube.rowTo)
		{
			ES_CUBE contentZoneRangeCube(eachZoneRangeCube);
			contentZoneRangeCube.rowFrom = titleZoneRangeCube.rowTo + 1;

			kaietrecognize::Zone eachZone;
			eachZone.m_type = kaietrecognize::Content;
			eachZone.m_range = contentZoneRangeCube;
			eachTableInfoPara.insertZone(eachZone);
		}

		kaietrecognize::Zone titleZone;
		titleZone.m_type = kaietrecognize::RowTitle;
		titleZone.m_range = titleZoneRangeCube;
		eachTableInfoPara.insertZone(titleZone);

		std::vector<RANGE> dataTableRangeList;
		initDataTableRange(pWorkSheet, dataTableRangeList);

		QVector<ES_CUBE> splitRange;
		splitTableByDataTable(pWorkSheet, dataTableRangeList, eachZoneRangeCube, splitRange);
		for (const auto& splitRangeItem : qAsConst(splitRange))
			eachTableInfoPara.insertChildRange(splitRangeItem);

		m_tableInfoList.insertRangeInfo(eachTableInfoPara);
	}
}

void kaietrecognize::RangeList::splitTableByDataTable(IKWorksheet* pSheet, const std::vector<RANGE>& dataTableRgList, ES_CUBE subRangeCube, QVector<ES_CUBE>& outRanges)
{
	outRanges.push_back(subRangeCube);
	if (!pSheet || !pSheet->GetSheet() || dataTableRgList.size() == 0)
		return;
	RANGE subRange(subRangeCube, pSheet->GetSheet()->GetBMP());
	std::vector<RANGE> matchRanges;
	for (const auto& item : dataTableRgList)
	{
		if (subRange.Contain(item))
			matchRanges.push_back(item);
	}
	if (matchRanges.size() > 0)
	{
		et_sptr<ISheetEnum> spEnum;
		HRESULT hr = pSheet->GetSheet()->CreateEnum(&spEnum);
		if (FAILED(hr) || !spEnum)
			return;
		outRanges.clear();
		KxEtAreaCutSheetEnumAcpt areaCutEnum(dataTableRgList);
		spEnum->EnumCellValueRowbyRow(subRange, &areaCutEnum);
		for (const auto& item : areaCutEnum.cutResult())
		{
			ES_CUBE cube = item;
			cube.sheetFrom = subRangeCube.sheetFrom;
			cube.sheetTo = subRangeCube.sheetTo;
			outRanges.append(cube);
		}
		for (const auto& item : matchRanges)
		{
			ES_CUBE cube;
			cube.colFrom = item.ColFrom();
			cube.colTo = item.ColTo();
			cube.rowFrom = item.RowFrom();
			cube.rowTo = item.RowTo();
			cube.sheetFrom = item.SheetFrom();
			cube.sheetTo = item.SheetTo();
			outRanges.append(cube);
		}
	}
}

void kaietrecognize::RangeList::initDataTableRange(IKWorksheet* pWorksheet, std::vector<RANGE>& dataTableList)
{
	if (pWorksheet == nullptr || pWorksheet->GetSheet() == nullptr)
		return;
	ks_stdptr <etoldapi::_Worksheet> spWorkSheet = pWorksheet;
	if (!spWorkSheet)
		return;
	ks_stdptr<etoldapi::ListObjects> spListObjects;
	HRESULT hr = spWorkSheet->get_ListObjects(&spListObjects);
	if (FAILED(hr) || spListObjects == nullptr)
		return;
	long nCnt = 0;
	hr = spListObjects->get_Count(&nCnt);
	if (FAILED(hr) || nCnt <= 0)
		return;
	for (long i = 1; i <= nCnt; ++i)
	{
		KComVariant varI(i, VT_I4);
		ks_stdptr<etoldapi::ListObject> spListObject;
		hr = spListObjects->get_Item(varI, &spListObject);
		if (FAILED(hr) || spListObject == nullptr)
			continue;
		RANGE rg(pWorksheet->GetSheet()->GetBMP());
		hr = spListObject->get_Range(&rg);
		if (SUCCEEDED(hr))
			dataTableList.push_back(rg);
	}
}

kaietrecognize::Zone::Zone(const QJsonArray& eachZone, IDX iSheet)
{
	if (eachZone.size() != 2)//暂定只有类型和范围
		return;
	QString zoneType = eachZone.at(0).toString();
	QJsonArray eachZoneRange = eachZone.at(1).toArray();
	HRESULT hr = createRange(eachZoneRange, iSheet, m_range);
	if (FAILED(hr))
		return;

	if (zoneType == "rowTitle")//行标题
		m_type = RowTitle;
	else if (zoneType == "bigTitle")//大标题
		m_type = BigTitle;
	else if (zoneType == "content")//内容
		m_type = Content;
	else if (zoneType == "subTitle")//副标题
		m_type = SubTitle;
	else if (zoneType == "tableInfo")//表信息
		m_type = Info;
	else if (zoneType == "empty")//空白
		m_type = Empty;
	else if (zoneType == "other")//其他
		m_type = Other;
	else
		m_type = Empty;

}

kaietrecognize::Zone::Zone()
{

}

HRESULT kaietrecognize::createRange(IN const QJsonArray& rangeArray, IDX iSheet, OUT ES_CUBE& cube)
{
	if (rangeArray.size() != 4)//暂时目前还没指定sheet
	{
		return E_FAIL;
	}
	cube.sheetFrom = iSheet;
	cube.sheetTo = iSheet;
	cube.rowFrom = rangeArray.at(0).toInt();
	cube.rowTo = rangeArray.at(1).toInt();
	cube.colFrom = rangeArray.at(2).toInt();
	cube.colTo = rangeArray.at(3).toInt();
	//处理服务端返回的异常数据
	if (cube.rowFrom > cube.rowTo)
	{
		QMessageBox::information(NULL, "data invalided", "row begin > row end", QMessageBox::Yes);
		return E_FAIL;
	}
	if (cube.colFrom > cube.colTo)
	{
		QMessageBox::information(NULL, "data invalided", "col begin > col end", QMessageBox::Yes);
		return E_FAIL;
	}

	return S_OK;
}

void kaietrecognize::TableRangeInfo::setWorksheet(IKWorksheet* pWorkSheet)
{
	m_pWorkSheet = pWorkSheet;
}

IKWorksheet* kaietrecognize::TableRangeInfo::getWorkSheet()
{
	return m_pWorkSheet;
}

IKWorkbook* kaietrecognize::TableRangeInfo::getWorkBook()
{
	return m_pWorkSheet->GetWorkbook();
}

kaietrecognize::TableRangeInfo::TableRangeInfo()
{

}

bool kaietrecognize::TableRangeInfo::isEmptyTableInfo()
{
	if (titleRangeInfoVec.isEmpty() && headRangeInfoVec.isEmpty() &&
		contentRangeInfoVec.isEmpty() && subTitleRangeInfoVec.isEmpty() && otherRangeInfoVec.isEmpty() && infoRangeInfoVec.isEmpty())
	{
		return true;
	}
	return false;
}

void kaietrecognize::TableRangeInfo::insertZone(const Zone& zone)
{
	ZoneType type = zone.m_type;
	ES_CUBE cube = zone.m_range;
	if (cube.rowFrom < allRangeInfo.rowFrom)
		cube.rowFrom = allRangeInfo.rowFrom;
	if (cube.rowTo > allRangeInfo.rowTo)
		cube.rowTo = allRangeInfo.rowTo;
	if (cube.colFrom < allRangeInfo.colFrom)
		cube.colFrom = allRangeInfo.colFrom;
	if (cube.colTo > allRangeInfo.colTo)
		cube.colTo = allRangeInfo.colTo;

	switch (type)
	{
	case kaietrecognize::RowTitle:
		headRangeInfoVec.push_back(cube);
		break;
	case kaietrecognize::BigTitle:
		titleRangeInfoVec.push_back(cube);
		break;
	case kaietrecognize::Content:
		contentRangeInfoVec.push_back(cube);
		break;
	case kaietrecognize::SubTitle:
		subTitleRangeInfoVec.push_back(cube);
		break;
	case kaietrecognize::Other:
		otherRangeInfoVec.push_back(cube);
		break;
	case kaietrecognize::Info:
		infoRangeInfoVec.push_back(cube);
		break;
	default:
		break;
	}
}

kaietrecognize::ZoneType kaietrecognize::TableRangeInfo::getCellZoneType(int row, int col)
{
	kaietrecognize::ZoneType type = Content;
	if (!isCellInZone(row, col, allRangeInfo))
		return type;
	//内容区域的概率大点 放前面命中概率较大;
	for (int i = 0; i < contentRangeInfoVec.size(); i++)
	{
		if (isCellInZone(row, col, contentRangeInfoVec[i]))
			return Content;
	}
	for (int i = 0; i < titleRangeInfoVec.size(); i++)
	{
		if (isCellInZone(row, col, titleRangeInfoVec[i]))
			return BigTitle;
	}
	for (int i = 0; i < headRangeInfoVec.size(); i++)
	{
		if (isCellInZone(row, col, headRangeInfoVec[i]))
			return RowTitle;
	}
	for (int i = 0; i < infoRangeInfoVec.size(); i++)
	{
		if (isCellInZone(row, col, infoRangeInfoVec[i]))
			return Info;
	}
	for (int i = 0; i < subTitleRangeInfoVec.size(); i++)
	{
		if (isCellInZone(row, col, subTitleRangeInfoVec[i]))
			return SubTitle;
	}
	for (int i = 0; i < otherRangeInfoVec.size(); i++)
	{
		if (isCellInZone(row, col, otherRangeInfoVec[i]))
			return Other;
	}

	return Empty;

}

ES_CUBE kaietrecognize::TableRangeInfo::getFillAlterArea()
{
	ES_CUBE ret = allRangeInfo;

	if(!titleRangeInfoVec.isEmpty())
		ret.rowFrom = titleRangeInfoVec[0].rowTo + 1;
	if (!infoRangeInfoVec.isEmpty())
		ret.rowFrom = infoRangeInfoVec[0].rowTo + 1;
	if (!headRangeInfoVec.isEmpty())
		ret.rowFrom = headRangeInfoVec[0].rowFrom;

	if (ret.rowFrom > ret.rowTo || ret.rowFrom > allRangeInfo.rowTo)
	{
		//容错
		ret.rowFrom = ret.rowTo;
	}

	return ret;
}

ES_CUBE kaietrecognize::TableRangeInfo::getFillRowAlterArea()
{
	ES_CUBE ret = allRangeInfo;

	if (!titleRangeInfoVec.isEmpty())
		ret.rowFrom = titleRangeInfoVec[0].rowTo + 1;
	if (!infoRangeInfoVec.isEmpty())
		ret.rowFrom = infoRangeInfoVec[0].rowTo + 1;
	if (!headRangeInfoVec.isEmpty())
		ret.rowFrom = headRangeInfoVec[0].rowTo + 1;

	if (ret.rowFrom > ret.rowTo || ret.rowFrom > allRangeInfo.rowTo)
	{
		//容错
		ret.rowFrom = ret.rowTo;
	}

	return ret;
}

int kaietrecognize::TableRangeInfo::getFirstColIdx()
{
	return allRangeInfo.colFrom;
}

int kaietrecognize::TableRangeInfo::getLastColIdx()
{
	return allRangeInfo.colTo;
}

bool kaietrecognize::TableRangeInfo::isCellInZone(int row, int col, const ES_CUBE& cube)
{
	return (row >= cube.rowFrom && row <= cube.rowTo)
		&& (col >= cube.colFrom && col <= cube.colTo);
}

kaietrecognize::TableRangeInfoList::TableRangeInfoList()
{

}

void kaietrecognize::TableRangeInfoList::insertRangeInfo(const ParentChildTableInfo& tableInfo)
{
	//判断是否有重复
	for (int i = 0; i < m_rangeInfoList.size(); i++)
	{
		if (isCubeEqual(tableInfo.parentRangeInfo.allRangeInfo, m_rangeInfoList[i].parentRangeInfo.allRangeInfo))
		{
			return;
		}
	}
	m_rangeInfoList.push_back(tableInfo);
}

int kaietrecognize::TableRangeInfoList::elementCnt()
{
	return m_rangeInfoList.size();
}

kaietrecognize::ParentChildTableInfo& kaietrecognize::TableRangeInfoList::item(int idx)
{
	return m_rangeInfoList[idx];
}

void kaietrecognize::TableRangeInfoList::setZoneTypeInfo(const Zone& zone)
{
	int cnt = elementCnt();
	for (int rangeIdx = 0; rangeIdx < cnt; rangeIdx++)
	{
		//判断区域范围是否属于此RANGE
		if (isRangeIntersected(zone.m_range, m_rangeInfoList[rangeIdx].parentRangeInfo.allRangeInfo))
		{
			m_rangeInfoList[rangeIdx].insertZone(zone);
		}
	}
}

bool kaietrecognize::TableRangeInfoList::isCubeEqual(const ES_CUBE& cube1, const ES_CUBE& cube2)
{
	bool bRet = (cube1.rowFrom == cube2.rowFrom)
		&& (cube1.rowTo == cube2.rowTo)
		&& (cube1.colFrom == cube2.colFrom)
		&& (cube1.colTo == cube2.colTo);
	return bRet;
}

kaietrecognize::StyleZoneType TransformStyleElementType(TABLE_STYLE_FORMAT tsf)
{
	switch (tsf)
	{
	case TSF_HeaderRowXF:
		return kaietrecognize::StyleRowTitle;
	case TSF_FirstColXF:
		return kaietrecognize::StyleFirstCol;
	case TSF_LastColXF:
		return kaietrecognize::StyleLastCol;
	case TSF_FirstRowStripeXF:
		return kaietrecognize::StyleOddRowAlter;
	case TSF_SecondRowStripeXF:
		return kaietrecognize::StyleEvenRowAlter;
	case TSF_FirstColStripeXF:
		return kaietrecognize::StyleOddColAlter;
	case TSF_SecondColStripeXF:
		return kaietrecognize::StyleEvenColAlter;
	case TSF_WholeTableXF:
		return kaietrecognize::StyleAllContent;
	case TSF_TotalRowXF:
		return kaietrecognize::StyleTotalRow;
	}
	return kaietrecognize::StylyUnvaild;
}

bool IsValidStyle(DWORD dw, kaietrecognize::StyleZoneType type)
{
	switch (type)
	{
	case kaietrecognize::StyleZoneType::StyleRowTitle:
		return (dw & TSO_HEADER_ROW);
	case kaietrecognize::StyleZoneType::StyleFirstCol:
		return (dw & TSO_FIRST_COL);
	case kaietrecognize::StyleZoneType::StyleLastCol:
		return (dw & TSO_LAST_COL);
	case kaietrecognize::StyleZoneType::StyleOddRowAlter:
	case kaietrecognize::StyleZoneType::StyleEvenRowAlter:
		return (dw & TSO_BANDED_ROWS);
	case kaietrecognize::StyleZoneType::StyleOddColAlter:
	case kaietrecognize::StyleZoneType::StyleEvenColAlter:
		return (dw & TSO_BANDED_COLS);
	case kaietrecognize::StyleZoneType::StyleAllContent:
		return true;
	}
	return false;
}

void SetRightBorder(io_utils::DXF& dxf, const io_utils::DXF& dxfEnd)
{
	if (dxfEnd.mask.inc_dgRight)
	{
		dxf.mask.inc_dgRight = 1;
		dxf.dgRight = dxfEnd.dgRight;
	}
	if (dxfEnd.mask.inc_clrRight)
	{
		dxf.mask.inc_clrRight = 1;
		dxf.clrRight = dxfEnd.clrRight;
	}
}

kaietrecognize::TableRangeStyle::TableRangeStyle(const KxtableStyleParser& parser)
{
	std::vector<io_utils::DXF> dxfs = parser.getTableXf();
	const std::map<KxtableStyleParser::KAIETStyleType, INT>& mapKaietStyleElement = parser.getKaietStyleElement();
	if (mapKaietStyleElement.find(KxtableStyleParser::TSF_KAIET_BigTitle) != mapKaietStyleElement.end())
	{
		INT iXF = mapKaietStyleElement.at(KxtableStyleParser::TSF_KAIET_BigTitle);
		if (iXF >= 0 && iXF < (INT)dxfs.size())
		{
			io_utils::DXF dxf = dxfs[iXF];
			KXFMASK mask(dxf.mask._cats & (XFMASK::_catBorder | XFMASK::_catFills), dxf.mask._catsFont & (XFMASK::_cat_bls | XFMASK::_cat_fItalic | XFMASK::_cat_fStrikeout | XFMASK::_cat_uls | XFMASK::_cat_clr));
			dxf.mask = mask;
			dxf.alcH = haCenter;
			dxf.mask.inc_alcH = 1;
			if (mapKaietStyleElement.find(KxtableStyleParser::TSF_KAIET_BigTitleEnd) != mapKaietStyleElement.end())
			{
				INT iEndXF = mapKaietStyleElement.at(KxtableStyleParser::TSF_KAIET_BigTitleEnd);
				if (iEndXF >= 0 && iEndXF < (INT)dxfs.size())
					SetRightBorder(dxf, dxfs[iEndXF]);
			}
			m_TableRangeStyleMap[StyleBigTitle] = dxf;
		}
	}
	if (mapKaietStyleElement.find(KxtableStyleParser::TSF_KAIET_Info) != mapKaietStyleElement.end())
	{
		INT iXF = mapKaietStyleElement.at(KxtableStyleParser::TSF_KAIET_Info);
		if (iXF >= 0 && iXF < (INT)dxfs.size())
		{
			io_utils::DXF dxf = dxfs[iXF];
			KXFMASK mask(dxf.mask._cats & (XFMASK::_catBorder | XFMASK::_catFills), dxf.mask._catsFont & (XFMASK::_cat_bls | XFMASK::_cat_fItalic | XFMASK::_cat_fStrikeout | XFMASK::_cat_uls | XFMASK::_cat_clr));
			dxf.mask = mask;
			if (mapKaietStyleElement.find(KxtableStyleParser::TSF_KAIET_InfoEnd) != mapKaietStyleElement.end())
			{
				INT iEndXF = mapKaietStyleElement.at(KxtableStyleParser::TSF_KAIET_InfoEnd);
				if (iEndXF >= 0 && iEndXF < (INT)dxfs.size())
					SetRightBorder(dxf, dxfs[iEndXF]);
			}
			m_TableRangeStyleMap[StyleInfo] = dxf;
		}
	}
	std::vector<TableStyleElementIO> vecStyleElement = parser.getTableStyleElement();
	for (size_t i = 0; i < vecStyleElement.size(); ++i)
	{
		INT iXF = vecStyleElement[i].dxfId;
		kaietrecognize::StyleZoneType type = TransformStyleElementType(vecStyleElement[i].tsf);
		if (iXF >= 0 && iXF < (INT)dxfs.size() && type != StylyUnvaild && IsValidStyle(parser.getStyleOption(), type))
			m_TableRangeStyleMap[type] = dxfs[iXF];
	}
	m_styleOption = parser.getStyleOption();
}

kaietrecognize::TableRangeStyle::~TableRangeStyle()
{
}

const io_utils::DXF* kaietrecognize::TableRangeStyle::getTableRangeStyle(StyleZoneType type)
{
	if (m_TableRangeStyleMap.find(type) != m_TableRangeStyleMap.end())
	{
		return &m_TableRangeStyleMap[type];
	}
	return nullptr;
}

kaietrecognize::ParentChildTableInfo::ParentChildTableInfo()
{
}

void kaietrecognize::ParentChildTableInfo::setWorksheet(IKWorksheet* pWorkSheet)
{
	parentRangeInfo.setWorksheet(pWorkSheet);
	for (auto& childRangeInfo :m_childRangeInfoList)
		childRangeInfo.setWorksheet(pWorkSheet);
}

IKWorksheet* kaietrecognize::ParentChildTableInfo::getWorkSheet()
{
	return parentRangeInfo.getWorkSheet();
}

IKWorkbook* kaietrecognize::ParentChildTableInfo::getWorkBook()
{
	return parentRangeInfo.getWorkBook();
}

bool kaietrecognize::ParentChildTableInfo::isEmptyTableInfo()
{
	return parentRangeInfo.isEmptyTableInfo();
}

void kaietrecognize::ParentChildTableInfo::insertZone(const Zone& zone)
{
	parentRangeInfo.insertZone(zone);
	for (auto& childRangeInfo : m_childRangeInfoList)
	{
		Zone intersectZone;
		intersectZone.m_type = zone.m_type;
		if (isRangeIntersected(zone.m_range, childRangeInfo.allRangeInfo, &intersectZone.m_range))
		{
			childRangeInfo.insertZone(intersectZone);
		}
	}
}

void kaietrecognize::ParentChildTableInfo::insertChildRange(const ES_CUBE& cube)
{
	TableRangeInfo childRangInfo;
	childRangInfo.allRangeInfo = cube;
	auto addIntersectZoneFunc = [=](const QVector<ES_CUBE>& inRange, QVector<ES_CUBE>& outRange) {
		for (const auto& inCube : inRange)
		{
			ES_CUBE intersectCube;
			if (isRangeIntersected(inCube, cube, &intersectCube))
				outRange.append(intersectCube);
		}
	};
	addIntersectZoneFunc(parentRangeInfo.contentRangeInfoVec, childRangInfo.contentRangeInfoVec);
	addIntersectZoneFunc(parentRangeInfo.headRangeInfoVec, childRangInfo.headRangeInfoVec);
	addIntersectZoneFunc(parentRangeInfo.titleRangeInfoVec, childRangInfo.titleRangeInfoVec);
	addIntersectZoneFunc(parentRangeInfo.infoRangeInfoVec, childRangInfo.infoRangeInfoVec);
	addIntersectZoneFunc(parentRangeInfo.subTitleRangeInfoVec, childRangInfo.subTitleRangeInfoVec);
	m_childRangeInfoList.append(childRangInfo);
}

kaietrecognize::ZoneType kaietrecognize::ParentChildTableInfo::getCellZoneType(int row, int col)
{
	return parentRangeInfo.getCellZoneType(row, col);
}

ES_CUBE kaietrecognize::ParentChildTableInfo::getFillAlterArea()
{
	return parentRangeInfo.getFillAlterArea();
}

ES_CUBE kaietrecognize::ParentChildTableInfo::getFillRowAlterArea()
{
	return parentRangeInfo.getFillRowAlterArea();
}

int kaietrecognize::ParentChildTableInfo::getFirstColIdx()
{
	return parentRangeInfo.getFirstColIdx();
}

int kaietrecognize::ParentChildTableInfo::getLastColIdx()
{
	return parentRangeInfo.getLastColIdx();
}

QVector<kaietrecognize::TableRangeInfo> kaietrecognize::ParentChildTableInfo::childRangeList()
{
	return m_childRangeInfoList;
}
