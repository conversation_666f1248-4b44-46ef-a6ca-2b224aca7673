<?xml version="1.0" encoding="UTF-8"?>
<svg width="196px" height="138px" viewBox="0 0 196 138" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 60 (88103) - https://sketch.com -->
    <title>编组</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <polygon id="path-1" points="47.0986486 3.62297297 48.1337838 3.62297297 48.1337838 96.7851351 47.0986486 96.7851351"></polygon>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="1.03513514" height="93.1621622" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <polygon id="path-3" points="47.0986486 29.5013514 48.1337838 29.5013514 48.1337838 121.628378 47.0986486 121.628378"></polygon>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="1.03513514" height="92.127027" fill="white">
            <use xlink:href="#path-3"></use>
        </mask>
        <polygon id="path-5" points="46.5810811 52.7918919 47.6162162 52.7918919 47.6162162 145.954054 46.5810811 145.954054"></polygon>
        <mask id="mask-6" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="1.03513514" height="93.1621622" fill="white">
            <use xlink:href="#path-5"></use>
        </mask>
        <rect id="path-7" x="93.1621622" y="0" width="1.03513514" height="124.216216"></rect>
        <mask id="mask-8" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="1.03513514" height="124.216216" fill="white">
            <use xlink:href="#path-7"></use>
        </mask>
        <rect id="path-9" x="47.0986486" y="0" width="1.03513514" height="124.216216"></rect>
        <mask id="mask-10" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="1.03513514" height="124.216216" fill="white">
            <use xlink:href="#path-9"></use>
        </mask>
        <linearGradient x1="4.55403646%" y1="51.1862833%" x2="100%" y2="51.1862833%" id="linearGradient-11">
            <stop stop-color="#F46D43" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#F46D43" offset="100%"></stop>
        </linearGradient>
        <filter x="-24.7%" y="-4.7%" width="149.3%" height="109.3%" filterUnits="objectBoundingBox" id="filter-12">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="交互状态合集" transform="translate(-78.000000, -768.000000)">
            <g id="编组-5" transform="translate(78.000000, 768.000000)">
                <g id="编组" transform="translate(0.000000, -0.000000)">
                    <g id="编组-3" transform="translate(3.105405, 5.175676)">
                        <path d="M93.2408831,0 L187.359459,0 L187.359459,124.216216 L1.03513514,124.216216 L1.03513514,0.37689205" id="路径" stroke="#D7D9DE" fill="#FFFFFF" stroke-dasharray="2,2"></path>
                        <polyline id="路径" stroke="#D7D9DE" fill="#FFFFFF" points="94.1972973 0 187.359459 0 187.359459 124.216216 94.1972973 124.216216"></polyline>
                        <path d="M35.1945946,16.5621622 L60.0378378,16.5621622 L60.0378378,108.689189 L35.1945946,108.689189 L35.1945946,16.5621622 Z" id="矩形备份-29" fill="#F5F7F9" transform="translate(47.616216, 62.625676) rotate(90.000000) translate(-47.616216, -62.625676) "></path>
                        <path d="M35.1945946,65.2135135 L60.0378378,65.2135135 L60.0378378,157.340541 L35.1945946,157.340541 L35.1945946,65.2135135 Z" id="矩形备份-30" fill="#F5F7F9" transform="translate(47.616216, 111.277027) rotate(90.000000) translate(-47.616216, -111.277027) "></path>
                        <use id="矩形备份-26" stroke="#D7D9DE" mask="url(#mask-2)" stroke-width="2" stroke-dasharray="2,2" transform="translate(47.616216, 50.204054) rotate(90.000000) translate(-47.616216, -50.204054) " xlink:href="#path-1"></use>
                        <use id="矩形备份-27" stroke="#D7D9DE" mask="url(#mask-4)" stroke-width="2" stroke-dasharray="2,2" transform="translate(47.616216, 75.564865) rotate(90.000000) translate(-47.616216, -75.564865) " xlink:href="#path-3"></use>
                        <use id="矩形备份-28" stroke="#D7D9DE" mask="url(#mask-6)" stroke-width="2" stroke-dasharray="2,2" transform="translate(47.098649, 99.372973) rotate(90.000000) translate(-47.098649, -99.372973) " xlink:href="#path-5"></use>
                        <use id="矩形备份-23" stroke="#D7D9DE" mask="url(#mask-8)" stroke-width="2" stroke-dasharray="2,2" xlink:href="#path-7"></use>
                        <g id="编组-2" transform="translate(11.386486, 5.175676)" font-family="MicrosoftYaHei, Microsoft YaHei" font-size="10" font-weight="normal">
                            <text id="数值1" fill="#36425A">
                                <tspan x="0" y="12.2810811">数值1</tspan>
                            </text>
                            <text id="254备份" line-spacing="22" fill="#4F5D79">
                                <tspan x="4.14054054" y="34.1594595">254</tspan>
                            </text>
                            <text id="51" line-spacing="22" fill="#4F5D79">
                                <tspan x="7.24594595" y="57.9675676">51</tspan>
                            </text>
                            <text id="235" line-spacing="22" fill="#4F5D79">
                                <tspan x="4.14054054" y="82.8108108">235</tspan>
                            </text>
                            <text id="70" line-spacing="22" fill="#4F5D79">
                                <tspan x="7.24594595" y="107.654054">70</tspan>
                            </text>
                            <text id="389" opacity="0.85" line-spacing="22" fill="#4F5D79">
                                <tspan x="98.3378378" y="33.1243243">389</tspan>
                            </text>
                            <text id="308" opacity="0.85" line-spacing="22" fill="#4F5D79">
                                <tspan x="143.883784" y="33.1243243">308</tspan>
                            </text>
                            <text id="111" opacity="0.85" line-spacing="22" fill="#4F5D79">
                                <tspan x="143.883784" y="57.9675676">111</tspan>
                            </text>
                            <text id="34" opacity="0.85" line-spacing="22" fill="#4F5D79">
                                <tspan x="145.954054" y="82.8108108">34</tspan>
                            </text>
                            <text id="110" opacity="0.85" line-spacing="22" fill="#4F5D79">
                                <tspan x="142.848649" y="107.654054">110</tspan>
                            </text>
                            <text id="224" opacity="0.85" line-spacing="22" fill="#4F5D79">
                                <tspan x="98.3378378" y="57.9675676">224</tspan>
                            </text>
                            <text id="365" opacity="0.85" line-spacing="22" fill="#4F5D79">
                                <tspan x="98.3378378" y="82.8108108">365</tspan>
                            </text>
                            <text id="47" opacity="0.85" line-spacing="22" fill="#4F5D79">
                                <tspan x="101.443243" y="107.654054">47</tspan>
                            </text>
                            <text id="数值3" opacity="0.85" fill="#36425A">
                                <tspan x="94.1972973" y="11.2459459">数值3</tspan>
                            </text>
                            <text id="数值4" opacity="0.85" fill="#36425A">
                                <tspan x="139.743243" y="11.2459459">数值4</tspan>
                            </text>
                        </g>
                        <use id="矩形备份-22" stroke="#D7D9DE" mask="url(#mask-10)" stroke-width="2" stroke-dasharray="2,2" xlink:href="#path-9"></use>
                        <text id="308" font-family="MicrosoftYaHei, Microsoft YaHei" font-size="10" font-weight="normal" line-spacing="22" fill="#4F5D79">
                            <tspan x="62.1081081" y="39.3351351">308</tspan>
                        </text>
                        <text id="111" font-family="MicrosoftYaHei, Microsoft YaHei" font-size="10" font-weight="normal" line-spacing="22" fill="#4F5D79">
                            <tspan x="62.1081081" y="63.1432432">111</tspan>
                        </text>
                        <text id="34" font-family="MicrosoftYaHei, Microsoft YaHei" font-size="10" font-weight="normal" line-spacing="22" fill="#4F5D79">
                            <tspan x="65.2135135" y="87.9864865">34</tspan>
                        </text>
                        <text id="110" font-family="MicrosoftYaHei, Microsoft YaHei" font-size="10" font-weight="normal" line-spacing="22" fill="#4F5D79">
                            <tspan x="62.1081081" y="112.82973">110</tspan>
                        </text>
                        <text id="数值2" font-family="MicrosoftYaHei, Microsoft YaHei" font-size="10" font-weight="normal" fill="#36425A">
                            <tspan x="57.9675676" y="17.4567568">数值2</tspan>
                        </text>
                    </g>
                    <path d="M6.21081081,127.321622 L6.21081081,128.356757 L1.035,128.356622 L1.03513514,133.532432 L0,133.532432 L-4.4408921e-16,127.321622 L6.21081081,127.321622 Z" id="形状结合备份-3" fill="#F46D43" transform="translate(3.105405, 130.427027) scale(1, -1) translate(-3.105405, -130.427027) "></path>
                    <path d="M6.21081081,0 L6.21081081,1.03513514 L1.035,1.035 L1.03513514,6.21081081 L8.8817842e-16,6.21081081 L4.4408921e-16,-4.12397337e-16 L6.21081081,0 Z" id="形状结合备份-3" fill="#F46D43"></path>
                    <path d="M194.605405,1.03513514 L194.605405,2.07027027 L189.429595,2.07013514 L189.42973,7.24594595 L188.394595,7.24594595 L188.394595,1.03513514 L194.605405,1.03513514 Z" id="形状结合备份-4" fill="#F46D43" transform="translate(191.500000, 4.140541) scale(-1, 1) translate(-191.500000, -4.140541) "></path>
                    <path d="M195.640541,128.356757 L195.640541,129.391892 L190.46473,129.391757 L190.464865,134.567568 L189.42973,134.567568 L189.42973,128.356757 L195.640541,128.356757 Z" id="形状结合备份-5" fill="#F46D43" transform="translate(192.535135, 131.462162) scale(-1, -1) translate(-192.535135, -131.462162) "></path>
                    <polygon id="矩形备份-16" fill-opacity="0.2" fill="url(#linearGradient-11)" filter="url(#filter-12)" points="72.4594595 4.14054054 96.7851351 4.14054054 96.7851351 132.497297 72.4594595 132.497297"></polygon>
                    <path d="M96.7675676,3.10540541 L96.8027027,3.10540541 C97.0788451,3.10540541 97.3027027,3.32926303 97.3027027,3.60540541 L97.3027027,130.962162 C97.3027027,131.238305 97.0788451,131.462162 96.8027027,131.462162 L96.7675676,131.462162 C96.4914252,131.462162 96.2675676,131.238305 96.2675676,130.962162 L96.2675676,3.60540541 C96.2675676,3.32926303 96.4914252,3.10540541 96.7675676,3.10540541 Z" id="矩形备份-15" fill="#F46D43"></path>
                    <path d="M50.1864865,-10.8864865 L50.1864865,80.2405405 L50.2216216,80.2405405 L50.2216216,-10.8864865 L50.1864865,-10.8864865 Z" id="矩形备份-25" stroke="#36425A" transform="translate(50.204054, 34.677027) rotate(90.000000) translate(-50.204054, -34.677027) "></path>
                    <path d="M50.1864865,-40.9054054 L50.1864865,50.2216216 L50.2216216,50.2216216 L50.2216216,-40.9054054 L50.1864865,-40.9054054 Z" id="矩形备份-31" stroke="#36425A" transform="translate(50.204054, 4.658108) rotate(90.000000) translate(-50.204054, -4.658108) "></path>
                </g>
            </g>
        </g>
    </g>
</svg>