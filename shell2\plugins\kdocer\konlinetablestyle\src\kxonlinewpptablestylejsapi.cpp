﻿#include "stdafx.h"
#include "kxtponlinetablestylewidget.h"
#include "kxonlinetablestylejsapi.h"
#include "kxonlinereshelper.h"
#include <kxshare/kxdocerunifypaydlgproxy.h>
#include <kxshare/kxonlinefontscontroller.h>
#include <kcomctl/kquickhelpbarwidget.h>
#include <kcomctl/kcommandfactory.h>
#include <ksolite/kcommonwebwidget.h>
#include <ksolite/kxjsonhelper.h>
#include <krt/product.h>
#include <include/freetype/fontapi.h>
#include <krt/krtstring.h>
#include <auth/productinfo.h>
#include <krt/dirs.h>
#include <ksolite/kdcinfoc.h>
#include "kdocertoolkit/docerfeature/kxdocertpfeatureutil.h"
#include "ktablebeautify.h"
#include "kxwpptablestylehelper.h"
#include "kso/aibeautifytool/aibeautifytool_i.h"
#include "kxwpptablestylerecommendhelper.h"
#include "wppcorenotify.h"
#include "kso/framework/api/wppapi_old.h"
#include "common/request/network/knetworkrequest.h"
#include "kdocerbasehelper.h"
#include "kdocertoolkit/kdocerutils.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include "utilities/path/module/kcurrentmod.h"
#include "kdocerfunctional.h"
#include "common/adapter/kxonlinereshelper.h"
#include "obfuscator/include/MetaString.h"
#include "kdocerresapply/kdocerresapplyonlinefont.h"
#include "ksolite/kdocer/docerresapply/kdocerresapplywrapper.h"
#include "common/toolkit/algorithm/kxdocershapenotify.h"


using namespace KxOnlineTableResHelper;
namespace
{
	const QString kPasteAction("paste");
	const QString kTableSmartStyle("tablesmartstyle");
	
	bool unZipAndGetTTFFilePath(const QString& zipFilePath, QString& refFontFilePath)
	{
		QFileInfo fontZipFileInfo(zipFilePath);
		QString strFontPath = KxOnlineResHelper::getTTFFilePath(fontZipFileInfo.absolutePath());
		if (QFile::exists(strFontPath))
			QFile(strFontPath).remove();
		if (fontZipFileInfo.exists())
		{
			QStringList outLstFileNames;
			bool bUnzip = KDocerCommonHelper::unZipFile(zipFilePath, fontZipFileInfo.absolutePath(), outLstFileNames);
			if (bUnzip)
			{
				strFontPath = KxOnlineResHelper::getTTFFilePath(fontZipFileInfo.absolutePath());
				if (QFile::exists(strFontPath))
				{
					refFontFilePath = strFontPath;
					QFile::remove(zipFilePath);
					return true;
				}
			}
		}
		return false;
	}

	QString getPrivateFontPath(const QString& relativeDir = QString())
	{
		QChar separator = QDir::separator();
		QString privateFontPath = krt::dirs::officeHome() % separator % "docerFonts" % separator;
		
		if (!relativeDir.isEmpty())
			privateFontPath = privateFontPath % relativeDir % separator;

		if (!QDir(privateFontPath).exists())
			QDir().mkpath(privateFontPath);

		return privateFontPath;
	}
}

KxOnlineTableStyleJsApi::KxOnlineTableStyleJsApi(KxWebViewContainer* webView, KxOnlineTableStyleWidget* onlineWidget)
	: KDocerCommonJsApi(webView, onlineWidget)
	, m_webView(webView)
	, m_onlineWidget(onlineWidget)
	, m_previewWidget(nullptr)
	, m_bShowPreview(false)
{
	kxApp->installEventFilter(this);
	m_emphasizeRow = -1;
	m_emphasizeCol = -1;
	m_onekeyBeautifyHelper = new KTableOnekeyBeautifyHelper(this);
	connect(m_onekeyBeautifyHelper, SIGNAL(finished(bool)), this, SLOT(onekeyBeautifyFinished(bool)));
	connect(KTableBeautify::instance(), SIGNAL(selectionTableChanged()), this, SLOT(selectionChanged()));
	if (auto* w = KTik::findParentByType<KQuickHelpShadowButtonBase>(m_onlineWidget))
	{
		m_qhPopupHelper = new KQhPopupHelper(w);
		connect(m_qhPopupHelper, SIGNAL(aboutPopup()), this, SLOT(notifyOnekeyBeautifyFinished()));
	}
	m_selChangeNotifyFilter.setInterval(100);
	m_selChangeNotifyFilter.setSingleShot(true);
	KxOnlineTableStyleJsApiHelper::Instance().addApi(this);
}

void KxOnlineTableStyleJsApi::isBeautifyAvaliable(KxWebViewJSContext& context)
{
	bool isOk = KDocerUtils::isWppApp(this) && KTableBeautify::instance()->isAvaliable();
	Json::Value res;
	res["isOk"] = isOk;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::isVerticalEmphasizeAvaiable(KxWebViewJSContext& context)
{
	Json::Value res;
	res["isOk"] = true;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::getSkinInfo(KxWebViewJSContext& context)
{
	int skinIdx = -1;
	int onekeySkinIdx = -1;
	QString colorIdx;
	if (KTableBeautify::instance()->getSkinInfo(&skinIdx, &onekeySkinIdx, &colorIdx))
	{
		Json::Value info;
		info["skinIdx"] = skinIdx;
		info["colorIdx"] = colorIdx.toStdString();
		info["onekeySkinIdx"] = onekeySkinIdx;
		setResult(context, info);
	}
	else
	{
		Json::Value res;
		res["isOk"] = false;
		setResult(context, res);
	}
}


void KxOnlineTableStyleJsApi::getTableInfo(KxWebViewJSContext& context)
{
	QString uuid;
	bool isOk = KTableBeautify::instance()->getTableInfo(&uuid);

	QVariantMap res;
	res.insert("isOk", isOk);
	res.insert("uuid", uuid);
	res.insert("selectedType", getTableSelectedType());
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::getEmphasizeInfo(KxWebViewJSContext& context)
{
	int rowCount = 0;
	int colCount = 0;
	int selectedRow = -1;
	int selectedCol = -1;
	KTableBeautify::instance()->getTableInfo(&rowCount, &colCount, &selectedRow, &selectedCol);

	int rowStyle = 0;
	int colStyle = 0;
	int currentRow = 0;
	int currentCol = 0;
	bool isVertOk = KTableBeautify::instance()->getEmphasizeInfo("vertical", &currentCol, &colStyle);
	bool isHorzOk = KTableBeautify::instance()->getEmphasizeInfo("horizontal", &currentRow, &rowStyle);

	QVariantMap res;
	res["isOk"] = true;
	res["rowCount"] = rowCount;
	res["colCount"] = colCount;
	res["selectedRow"] = (m_emphasizeRow != -1) ? m_emphasizeRow : selectedRow;
	res["selectedCol"] = (m_emphasizeCol != -1) ? m_emphasizeCol : selectedCol;

	QVariantMap vert;
	vert["isOk"] = isVertOk;
	vert["pos"] = currentCol;
	vert["styleId"] = colStyle;
	res["vertical"] = vert;

	QVariantMap horz;
	horz["isOk"] = isHorzOk;
	horz["pos"] = currentRow;
	horz["styleId"] = rowStyle;
	res["horizontal"] = horz;

	setResult(context, res);
}

void KxOnlineTableStyleJsApi::onekeyStructBeautify(KxWebViewJSContext& context)
{
	QVariantMap args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	QString source = args.value("source").toString();
	if (source.isEmpty())
		return;

	if (source == "quickmq")
	{
		KQuickHelpShadowButtonBase* btn =
			KTik::findParentByType<KQuickHelpShadowButtonBase>(m_onlineWidget);
		QMetaObject::invokeMethod(btn, "onLButtonClicked");
	}
	m_onekeyBeautifyHelper->trigger(source);
}

void KxOnlineTableStyleJsApi::setSkin(KxWebViewJSContext& context)
{
	//判断是否在预览中
	if (isInPreview())
	{
		endPreview(true);
		Json::Value res;
		res["isOk"] = true;
		setResult(context, res);
		KxOnlineTableStyleJsApiHelper::Instance().onCallBackToJs("slideChanged", "");
		return;
	}

	QVariantMap args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	bool isOk = false;
	int skinIdx = args.value("skinIdx").toInt();
	QString colorIdx = args.value("colorIdx").toString();
	QVariantList arrRgbs = args.value("rgbs").toList();
	if (!colorIdx.isEmpty() && !arrRgbs.isEmpty())
	{
		isOk = KTableBeautify::instance()->setSkin(skinIdx, colorIdx, arrRgbs);
	}

	Json::Value res;
	res["isOk"] = isOk;
	setResult(context, res);
	
	KxOnlineTableStyleJsApiHelper::Instance().onCallBackToJs("slideChanged", "");
}

void KxOnlineTableStyleJsApi::setEmphasize(KxWebViewJSContext& context)
{
	bool isOk = false;

	//判断是否在预览中
	if (isInPreview())
	{
		endPreview(true);
		isOk = true;
	}
	else
	{
		const QVariantMap& args = parseContextArgs(context);
		if (args.isEmpty())
			return;

		int pos = args.value("pos").toInt();
		int styleId = args.value("id").toInt();
		QString type = args.value("type").toString();

		if (pos == 0)
		{
			QString strInfo = tr("To use the advance table emphasize, "
				"insert the table first, and then use the docer table style "
				"after selecting the number of rows and columns to be inserted in Insert-Table.");
			kxApp->messageBox(strInfo, MB_ICONEXCLAMATION | MB_OK);
		}
		else
		{
			if (type == "horizontal")
				m_emphasizeRow = pos;
			else if (type == "vertical")
				m_emphasizeCol = pos;

			isOk = KTableBeautify::instance()->setEmphasize(type, pos, styleId);
		}
	}

	if (isOk)
		KTableBeautify::instance()->notifySelectionTableChanged();

	Json::Value res;
	res["isOk"] = isOk;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::clearEmphasize(KxWebViewJSContext& context)
{
	wppActiveNormalView(this);

	QVariantMap args = parseContextArgs(context);
	QString type = args.value("type").toString();

	bool isOk = KTableBeautify::instance()->clearEmphasize(type);
	if (isOk)
		KTableBeautify::instance()->notifySelectionTableChanged();

	Json::Value res;
	res["isOk"] = isOk;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::beginOpPreview(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	bool isOk = false;
	if (canHover())
	{
		WpViewType viewType = KxWppTableStyleHelper::instance()->getWppViewType();
		if (viewType != wpViewSlideMaster
			&& viewType != wpViewHandoutMaster
			&& viewType != wpViewNotesMaster)
		{
			wppActiveNormalView(this);

			QString action = args.value("action").toString();
			if (action == "emphasize") //强调
			{
				int pos = args.value("pos").toInt();
				int styleId = args.value("id").toInt();
				QString type = args.value("type").toString();

				beginPreview(KAiBeautify_TableEmphaszie);

				isOk = KTableBeautify::instance()->setEmphasize(type, pos, styleId);
			}
			else //设置皮肤
			{
				if (!KxOnlineTableResHelper::hasActiveTable(this))
				{
					Json::Value res;
					res["isOk"] = false;
					res["nextOp"] = getPreviewNextOp().toStdString().c_str();
					setResult(context, res);
					return;
				}

				int skinIdx = args.value("skinIdx").toInt();
				QString colorIdx = args.value("colorIdx").toString();
				QVariantList arrRgbs = args.value("rgbs").toList();
				if (!colorIdx.isEmpty() && !arrRgbs.isEmpty())
				{
					beginPreview(KAiBeautify_ChangeTableSkin);

					isOk = KTableBeautify::instance()->setSkin(skinIdx, colorIdx, arrRgbs, false);
				}
			}

			if (!isOk)
				endPreview();
		}
	}

	Json::Value res;
	res["isOk"] = isOk;
	QString nextOp = "endPreview";
	if (!isOk)
		nextOp = getPreviewNextOp();
	res["nextOp"] = nextOp.toStdString().c_str();
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::endOpPreview(KxWebViewJSContext& context)
{
	endPreview(false);
}

void KxOnlineTableStyleJsApi::getInitTabIndex(KxWebViewJSContext& context)
{
	Json::Value res;
	res["index"] = KxWppTableStylePopupHelper::instance()->getInitTabIndex();
	res["targetId"] = KxWppTableStylePopupHelper::instance()->getInitTabTargetId().toStdString();
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::setNotAutoPopupWithinSevenDays(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	bool checked = args.value("checked").toBool();

	KxWppTableStylePopupHelper::instance()->setNotAutoPopupWithinSevenDays(kTableSmartStyle, kPasteAction, checked);
}

void KxOnlineTableStyleJsApi::isNotAutoPopupWithinSevenDays(KxWebViewJSContext& context)
{
	Json::Value res;
	res["visible"] = KxWppTableStylePopupHelper::instance()->shouldAutoPopup();
	res["checked"] = KxWppTableStylePopupHelper::instance()->isNotAutoPopupWithinSevenDays(kTableSmartStyle, kPasteAction);
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::isTableAvailable(KxWebViewJSContext& context)
{
	QVariantMap res;
	WpViewType viewType = KxWppTableStyleHelper::instance()->getWppViewType();
	if (viewType == wpViewSlideMaster
		|| viewType == wpViewHandoutMaster
		|| viewType == wpViewNotesMaster)
	{
		res["error_code"] = 1;
		res["error_msg"] = tr("This function is not available in the current mode.");
		return setResult(context, res);
	}

	int rowCount = 0;
	int colCount = 0;
	int selectedRow = -1;
	int selectedCol = -1;
	if (!KTableBeautify::instance()->getTableInfo(&rowCount, &colCount, &selectedRow, &selectedCol))
	{
		res["error_code"] = 2;
		res["error_msg"] = tr("There is no table currently. Please insert the table and try again.");
		return setResult(context, res);
	}
	
	res["error_code"] = 0;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::getSupportSkinIdxRange(KxWebViewJSContext& context)
{
	int idxRng = 4;
	QString sharedValue;
	if (auto sharedMemory = getIKDocerSharedMemory())
		sharedValue = sharedMemory->getSharedMemory("kaiwpp", "iskaiwppsupportnewtablestyle");
	if ("1" == sharedValue)
	{
		idxRng = 9;
	}
	QVariantMap args = { {"supportSkinIdxRange", QString::number(idxRng)} };
	setResult(context, formatResult(args));
}

void KxOnlineTableStyleJsApi::getRecommendTableStyle(KxWebViewJSContext& context)
{
	QString imgStr = KxWppTableStyleRecommendHelper::instance()->getLastSlideImgBase64();
	bool res = uploadToKS3(imgStr);

	QVariantMap arg = { {"status", res ? "success" : "failed"} };
	setResult(context, arg);
}

void KxOnlineTableStyleJsApi::applyEx(KxWebViewJSContext& context)
{
	QVariantMap args = parseContextArgs(context);
	if (args.isEmpty())
		return setResult(context, QVariantMap{ {"status", "failed"}, {"msg", "arg is empty"} });

	if (!args.contains("type"))
		return setResult(context, QVariantMap{ {"status", "failed"}, {"msg", "arg does not contain type"} });

	if (args.value("type") == "tablestyle")
	{
		insertOnlineResource(context);
	}
	else
	{
		setSkin(context);
	}
}

void KxOnlineTableStyleJsApi::beginPreviewEx(KxWebViewJSContext& context)
{
	QVariantMap args = parseContextArgs(context);
	if (args.isEmpty())
		return setResult(context, QVariantMap{ {"status", "failed"}, {"msg", "arg is empty"} });

	if (!args.contains("type"))
		return setResult(context, QVariantMap{ {"status", "failed"}, {"msg", "arg does not contain type"} });

	if (args.value("type") == "tablestyle")
	{
		hoverOnlineResource(context);
	}
	else
	{
		beginOpPreview(context);
	}
}

void KxOnlineTableStyleJsApi::endPreviewEx(KxWebViewJSContext& context)
{
	QVariantMap args = parseContextArgs(context);
	if (args.isEmpty())
		return setResult(context, QVariantMap{ {"status", "failed"}, {"msg", "arg is empty"} });

	if (!args.contains("type"))
		return setResult(context, QVariantMap{ {"status", "failed"}, {"msg", "arg does not contain type"} });

	if (args.value("type") == "tablestyle")
	{
		leaveOnlineResource(context);
	}
	else
	{
		endOpPreview(context);
	}
}

void KxOnlineTableStyleJsApi::onDocChanged()
{
	if (!isActiveWindow())
		return;

	callbackToJS("documentChanged", "");
}

void KxOnlineTableStyleJsApi::callbackToJS(const QString& callback, const QString& result, bool bBase64 /*= true*/)
{
	if (callback != "slideChanged")
	{
		KDocerCommonJsApi::callbackToJS(callback, result, bBase64);
		return;
	}

	if (isActiveWindow())
		KDocerCommonJsApi::callbackToJS(callback, result, bBase64);
}

void KxOnlineTableStyleJsApi::notifyGetRecommendRes()
{
	if (!isActiveWindow())
		return;

	if (m_selChangeNotifyFilter.isActive())
		return;
	m_selChangeNotifyFilter.start();

	if (!KxWppTableStyleRecommendHelper::instance()->isTableAvailable())
		return;

	if (KxWppTableStyleRecommendHelper::instance()->isSlideImageChanged())
		KxOnlineTableStyleJsApiHelper::Instance().onCallBackToJs("slideChanged", "");
}

void KxOnlineTableStyleJsApi::onGetUploadFileUrl(const QString& url)
{
	if (!isActiveWindow())
		return;

	QString finalUrl = url;
	if (finalUrl.isEmpty())
		finalUrl = OBFUSCATED("https://img7.file.cache.docer.com/storage/image/2/f/e/7/1/44ab221df013175ae8659c7a78e98e14.png");

	QVariantMap res = { {"status", url.isEmpty() ? "failed" : "success"},
		{"url", finalUrl},
	};
	callbackToJS("onGetUploadFileUrl", formatResult(res));
}

void KxOnlineTableStyleJsApi::selectionChanged()
{
	callbackToJS("selectionChanged", QString());
	notifyGetRecommendRes();
}

void KxOnlineTableStyleJsApi::onekeyBeautifyFinished(bool success)
{
	if (m_qhPopupHelper && success)
	{
		m_qhPopupHelper->trigger();
		emit sigTabIndexChanged(1);
		return;
	}

	notifyOnekeyBeautifyFinished(success);
}

void KxOnlineTableStyleJsApi::notifyOnekeyBeautifyFinished(bool success /*= true*/)
{
	QVariantMap result;
	result["error_code"] = (int)!success;
	callbackToJS("onekeyBeautifyFinished", formatResult(result));
}

QString KxOnlineTableStyleJsApi::getTableSelectedType() const
{
	int rowBegin = 0, rowEnd = 0, colBegin = 0, colEnd = 0;
	KTableBeautify::instance()->getSelectedCellRange(&rowBegin, &rowEnd, &colBegin, &colEnd);
	int selectRow = rowEnd - rowBegin;
	int selectCol = colEnd - colBegin;

	QString selectedType;
	if (selectRow == 1 && selectCol > 1)
	{
		selectedType = "row";
	}
	else if (selectRow > 1 && selectCol == 1)
	{
		selectedType = "col";
	}

	return selectedType;
}

void KxOnlineTableStyleJsApi::changeFont(KxWebViewJSContext& context)
{	
	QVariantMap result;
	result["id"] = 0;
	result["is_ok"] = false;
	QVariantMap args = parseContextArgs(context);
	if (args.isEmpty())
	{
		setResult(context, result);
		return;
	}
	if (m_onlineFontData.id == args.value("templateId").toInt())
		return;
	cancelDownloadFont();
	m_onlineFontData.id = args.value("templateId").toInt();
	result["id"] = m_onlineFontData.id;
	m_onlineFontData.name = args.value("name").toString();
	if (m_onlineFontData.name.isEmpty())
	{
		setResult(context, result);
		return;
	}

	if (KxOnlineFontController::instance()->indexOfFont(m_onlineFontData.name) != -1)
	{
		QString msg = applyFont(m_onlineFontData.name) ? "onApplySuccess" : "onApplyError";
		changeFontMessage(m_onlineFontData.id, msg);
		result["is_ok"] = true;
		setResult(context, result);
		m_onlineFontData.id = -1;
		return;
	}
	
	const QString localPath = getPrivateFontPath() % m_onlineFontData.name % ".ttf";
	QFileInfo info(localPath);
	if (info.isFile() && info.exists())
	{
		applyRes(false);
		result["is_ok"] = true;
		setResult(context, result);
		return;
	}

	QVariantMap data = args.value("data").toMap();
	QString url = data.value("storage_url").toString();
	if (url.isEmpty())
	{
		setResult(context, result);
		return;
	}
	m_onlineFontData.md5 = data.value("md5").toString();
	m_onlineFontData.price = data.value("price").toInt();
	m_onlineFontData.resSize = data.value("file_size", 0).toLongLong();
	m_onlineFontData.downloadKey = data.value("download_key").toString();
	int fontLevel = data.value("level").toInt();
	if (fontLevel == 12)
		m_onlineFontData.vipFont = true;
	if (fontLevel == 6)
		m_onlineFontData.businessFont = true;

	QStringList urlList(url);
	result["is_ok"] = downloadOnlineFont(urlList);
	setResult(context, result);
}

void KxOnlineTableStyleJsApi::onDownloadFontSuccess(const QString& taskId, const QString& fileName)
{
	releasedDownloadFontTask();
	changeFontMessage(m_onlineFontData.id, "onDownloadSuccess");
	applyRes(true);
}

void KxOnlineTableStyleJsApi::onDownloadFontProgress(const QString& id, int nPercent)
{
	QVariantMap result;
	result["id"] = m_onlineFontData.id;
	result["percent"] = nPercent;
	result["onDownloadProgress"] = "onDownloadProgress";
	callbackToJS("changeFontMessage", formatResult(result));
}

void KxOnlineTableStyleJsApi::onDownloadFontError(const QString& id, const DownloadFailInfo& info)
{
	releasedDownloadFontTask();
	changeFontMessage(m_onlineFontData.id, "onDownloadError");
}

QString KxOnlineTableStyleJsApi::getModuleName() const
{
	return "docer_tablestyle";
}

bool KxOnlineTableStyleJsApi::eventFilter(QObject* o, QEvent* e)
{
	if (e->type() == QEvent::MouseButtonPress && m_onlineWidget && (m_emphasizeRow != -1 || m_emphasizeCol != -1))
	{
		QMouseEvent* mouse = static_cast<QMouseEvent*>(e);
		QPoint pos = mouse->globalPos();
		QRect rc = m_onlineWidget->rect();
		QPoint topLeft = m_onlineWidget->mapToGlobal(rc.topLeft());
		rc.moveTopLeft(topLeft);
		if (!rc.contains(pos))
		{
			m_emphasizeRow = -1;
			m_emphasizeCol = -1;
		}
	}
	return false;
}

bool KxOnlineTableStyleJsApi::isInPreview()
{
	if (KPreviewHelper::instance()->isInPreview())
		return true;

	return false;
}

void KxOnlineTableStyleJsApi::beginPreview(LPCWSTR desc)
{
	QWidget* webView = m_webView ? m_webView->getWebView() : nullptr;
	KPreviewHelper::instance()->beginPreview(webView, desc);
}

void KxOnlineTableStyleJsApi::beginPreview(const QString& desc)
{
}

void KxOnlineTableStyleJsApi::endPreview(bool isCommit)
{
	KPreviewHelper::instance()->endPreview(isCommit);
}

bool KxOnlineTableStyleJsApi::isActiveWindow()
{
	m_originWnd = kxApp->findRelativeMainWindow(this);
	if (!m_originWnd)
		return true;

	KMainWindow* curWnd = kxApp->currentMainWindow();
	if (intp(curWnd) == intp(m_originWnd))
		return true;

	return false;
}

docer::base::KDocerWidgetType KxOnlineTableStyleJsApi::getHostType()
{
	KDocerCommonWebWidget* wgt = qobject_cast<KDocerCommonWebWidget*>(m_hostWidget);
	if (wgt)
		return wgt->widgetType();

	return docer::base::KDocerWidgetType_Default;
}

bool KxOnlineTableStyleJsApi::isPopupWidget()
{
	docer::base::KDocerWidgetType type = getHostType();
	return type == docer::base::KDocerWidgetType_Drop || type == docer::base::KDocerWidgetType_Qh;
}

QString KxOnlineTableStyleJsApi::getPreviewNextOp()
{
	QString nextOP = "stop";
	if (!isPopupWidget())
		nextOP = "continue";

	return nextOP;
}

bool KxOnlineTableStyleJsApi::downloadOnlineFont(const QStringList& urls)
{
	DownloadArgs downloadData;
	downloadData.commonArgs.plgName = "konlinewpptablestyle";
	downloadData.commonArgs.plgVer = docer::base::KCurrentModule::getFileVersion();
	downloadData.resourceArgs.md5 = m_onlineFontData.md5;
	downloadData.resourceKey = "font";
	downloadData.resourceArgs.id = QString::number(m_onlineFontData.id);
	downloadData.resourceArgs.resSize = m_onlineFontData.resSize;
	downloadData.resourceArgs.downloadKey = m_onlineFontData.downloadKey;

	m_downloadFontTask = new KDocerDownloader(downloadData, this);
	connect(m_downloadFontTask, &KDocerDownloader::onProgress,
		this, &KxOnlineTableStyleJsApi::onDownloadFontProgress);
	connect(m_downloadFontTask, &KDocerDownloader::onSuccess,
		this, &KxOnlineTableStyleJsApi::onDownloadFontSuccess);
	connect(m_downloadFontTask, &KDocerDownloader::onError,
		this, &KxOnlineTableStyleJsApi::onDownloadFontError);
	QString tmpFontFile = getPrivateFontPath("cache") % "~{" % m_onlineFontData.name % "}";
	auto downloadId = m_downloadFontTask->download(urls, tmpFontFile, "", false);
	return !downloadId.isEmpty();
}

void KxOnlineTableStyleJsApi::applyRes(bool needUnzip)
{
	bool flag = false;
	auto d = defer([&]() {
		changeFontMessage(m_onlineFontData.id, flag ? "onApplySuccess" : "onApplyError");
		m_onlineFontData.id = -1;
		});
	QString fontFilePath = "";
	QString fontCachePath = getPrivateFontPath("cache") % "~{" % m_onlineFontData.name % "}";
	if (needUnzip)
	{
		if (!unZipAndGetTTFFilePath(fontCachePath, fontFilePath))
			return;
		if (!m_onlineFontData.md5.isEmpty())
		{
			QString md5 = KDocerUtils::getFileMd5(fontFilePath);
			if (m_onlineFontData.md5.toUpper() != md5.toUpper())
				return;
		}
	}

	QString name = m_onlineFontData.name;
	float price = m_onlineFontData.price;
	bool vip = m_onlineFontData.vipFont;
	if (price > 0)
		vip = true;

	QVariantMap commonParams;
	commonParams.insert(kdocerresapply::g_strType, kdocerresapply::g_strOnlineFont);
	commonParams.insert(kdocerresapply::g_strReqId, "");
	commonParams.insert(kdocerresapply::g_strAppId, "wps_pc_kdocercore");

	OnlineFontApplyInfo applyParams;
	applyParams.applyAction = OnlineFontApplyAction::AddOnlineFontsData;
	applyParams.fontName = name;
	applyParams.fontID = m_onlineFontData.id;
	applyParams.vipFont = vip;
	applyParams.fontPrice = price;
	applyParams.businessFont = m_onlineFontData.businessFont;
	applyParams.fontFamily = m_onlineFontData.familyName;
	applyParams.fontWeight = m_onlineFontData.weightName;

	QVariantMap emptyParam;
	KDocerResApplySDK::getInstance().docerResApply(commonParams, applyParams.info2Map(), emptyParam);

	applyParams.applyAction = OnlineFontApplyAction::InstallOnlineFont;
	applyParams.fontFilePath = fontFilePath;

	if (docer::FE_SUCCESS == KDocerResApplySDK::getInstance().docerResApply(commonParams, applyParams.info2Map(), emptyParam))
	{
		if (QFile::exists(fontFilePath))
			QFile(fontFilePath).remove();
		flag = applyFont(name);
	}
}

bool KxOnlineTableStyleJsApi::applyFont(const QString& name)
{
	return KTableBeautify::instance()->applyFont(name);
}

void KxOnlineTableStyleJsApi::changeFontMessage(int fontId, const QString& message)
{
	QVariantMap result;
	result["id"] = fontId;
	result[message] = message;
	callbackToJS("changeFontMessage", formatResult(result));
}

void KxOnlineTableStyleJsApi::cancelDownloadFont()
{
	if (m_downloadFontTask)
	{
		m_downloadFontTask->cancel();
		releasedDownloadFontTask();
		changeFontMessage(m_onlineFontData.id, "cancelDownload");
	}
}

void KxOnlineTableStyleJsApi::releasedDownloadFontTask()
{
	if (m_downloadFontTask)
	{
		m_downloadFontTask->deleteLater();
		m_downloadFontTask = nullptr;
	}
}

bool KxOnlineTableStyleJsApi::uploadToKS3(const QString& imgStr)
{
	if (imgStr.isEmpty())
		return false;

	QPointer<KxOnlineTableStyleJsApi> spThis(this);
	QString strDir = QString("tablestylerecommend/%1").arg(QDateTime::currentDateTime().toStringEx("yyyy-MM-dd"));
	docer::ks3Upload(QByteArray::fromBase64(imgStr.toLocal8Bit()), "wps-docer-collection", strDir,
		[=](const QString& fileMd5)
		{
			if (!spThis)
				return;

			QString imgUrl = QString(OBFUSCATED("https://ks3-cn-beijing.ksyun.com/wps-docer-collection/%1/%2")).arg(strDir).arg(fileMd5);
			onGetUploadFileUrl(imgUrl);
		},
		[=](DownloadFailInfo errorInfo)
		{
			if (!spThis)
				return;

			onGetUploadFileUrl("");
		}, 6000, "jpg");

	return true;
}
