wps_package(konlinetablestyle SHARED BUILD_DEST_PATH office6/addons/konlinetablestyle)
	wps_include_directories(
		..
		../..
		../src
		../../../../
		../../../../../
		Coding/core_bundle/include
		Coding/core_bundle/framework
		Coding/core_bundle/framework/ksolite
		Coding/core_bundle/office/include
		../../../include
		Coding/shell_bundle/shell2/include
		Coding/plugin_bundle/shell2/plugins/docer
		Coding/plugin_bundle/shell2/plugins/docer/include
		Coding/core_bundle/office/et
		Coding/core_bundle/office/et/include
		Coding/misc_bundle/3rdparty
		Coding/io_bundle/io
		Coding/io_bundle/io/ooxml
		Coding/io_bundle/io/include
		Coding/io_bundle/io/share
		../../../wpstablestyle
		Coding/core_bundle/office/wps/include
		../../../kaibeautify/kaiet/src
		Coding/core_bundle
		Coding/shell_bundle/shell2
	)

	wps_use_packages(
		curl
		jsoncpp
		Qt5Widgets
		Qt5Svg
		Qt5Xml
		Qt5Network
		cryptopp
		LINUX(libsafec)
		WIN(zip-utils)
		cryptopp
	)
	wps_add_sources(
		PCH stdafx.h stdafx.cpp
		QT_AUTOMOC
		
		../main.cpp
		../src/kundotransaction.cpp
		../src/kundotransaction.h
		../src/kxonlinereshelper.cpp
		../src/kxonlinereshelper.h
		../src/kxonlinetablestylejsapi.cpp
		../src/kxonlinetablestylejsapi.h
		../src/kxonlinetablestyledefine.h
		../src/kxonlinetablestyledefine.cpp
		../src/kxtablestylegallerycommand.cpp
		../src/kxtablestylegallerycommand.h
		../src/ktablestyleinfocollect.cpp
		../src/ktablestyleinfocollect.h
		../src/kxtpettablestylecommand.cpp
		../src/kxtpettablestylecommand.h
		../src/kxtponlinetablestylewidget.cpp
		../src/kxtponlinetablestylewidget.h
		../src/kxtptablestylecommand.cpp
		../src/kxtptablestylecommand.h
		../src/kxtpwpstablestylecommand.cpp
		../src/kxwpsonlinereshelper.cpp
		../src/kxwpponlinereshelper.cpp
		../src/kxtableemphasize.cpp
		../src/kxtableemphasize.h
		../src/ktablebeautifypreviewhelper.cpp
		../src/ktablebeautifypreviewhelper.h
		../src/kxwpstablebeautifyhelper.cpp
		../src/kxwpstablebeautifyhelper.h
		../src/kxwpstablebeautifymgr.cpp
		../src/kxwpstablebeautifymgr.h
		../src/kxonlinewpstablestylejsapi.cpp
		../src/kxtablestylefeaturecommand.cpp
		../src/kxtablestylefeaturecommand.h
		../src/kxetcommonheander.h
		../src/custom/kxdocertablestylewidget.cpp
		../src/custom/kxdocertablestylewidget.h
		../src/custom/kxdocertablestylegalleryitem.cpp
		../src/custom/kxdocertablestylegalleryitem.h
		../src/etarrange/kaietarrangeprocess.cpp
		../src/etarrange/kaietarrangeprocess.h
		../src/etarrange/kaietarrangeprocessdata.cpp
		../src/etarrange/kaietarrangeprocessdata.h
		../src/etarrange/kaietrecognizedata.cpp
		../src/etarrange/kaietrecognizedata.h
		../src/etarrange/kaietarrangehelper.cpp
		../src/etarrange/kaietarrangehelper.h
		../src/tablestyleparser/kxcommontablestyleparser.cpp
		../src/tablestyleparser/kxcommontablestyleparser.h
		../src/tablestyleparser/kxettablestyleparser.cpp
		../src/tablestyleparser/kxettablestyleparser.h
		../src/tablestyleparser/kxtablestyleparserbase.cpp
		../src/tablestyleparser/kxtablestyleparserbase.h
		../src/resmgr/konlinetableresmgr.cpp
		../src/resmgr/konlinetableresmgr.h
		../src/inserttable/kinsertonlinetablewidget.cpp
		../src/inserttable/kinsertonlinetablewidget.h
		../src/inserttable/kinsertonlinetablecmd.cpp
		../src/inserttable/kinsertonlinetablecmd.h
		../src/inserttable/kinsertonlinetablemodel.cpp
		../src/inserttable/kinsertonlinetablemodel.h

		../../../kaibeautify/kaiet/src/smartidentify.h
		../../../kaibeautify/kaiet/src/common/identifytable.cpp
		../../../kaibeautify/kaiet/src/common/identifytable.h
		../../common/toolkit/algorithm/kxdocershapenotify.cpp
		../../common/toolkit/algorithm/kxwpsdocershapenotify.cpp
		../../common/toolkit/algorithm/kxetdocershapenotify.cpp
		../../common/toolkit/algorithm/kxwppdocershapenotify.cpp
		../../common/toolkit/algorithm/kxdocershapenotify.h
		../../common/toolkit/algorithm/kxetdocerdataevent.cpp
		../../common/toolkit/algorithm/kxetdocerdataevent.h

		../../common/base/algorithm/kdocerdschartrule.cpp
		../../common/base/algorithm/kdocerdschartrule.h
	)

	wps_add_resources(
		cfg.ini
		LOCATION ${CMAKE_SOURCE_DIR}/plugin_bundle/shell2/plugins/kdocer/konlinetablestyle
		BUILD_DEST_PATH office6/addons/konlinetablestyle/
	)
	wps_link_packages(
		kso
		kshell
		krt
		ksolite
		algdiag
		kdownload
		ksqlite3
		wpstablestyle
		etcore
		kdocerlegacy
		kdocerrequest
		
		kdocerresourcelib
		
		kdocertoolkit
		
		kdocerjsapi
		docermodulelib
		kuserinfo
		
		kdocerwidget
	)
	
	wps_custom_compile(lrelease
		INPUT ../mui/zh_CN/ts/konlinetablestyle.ts
		OUTPUT office6/addons/konlinetablestyle/mui/zh_CN/konlinetablestyle.qm
	)

	wps_custom_compile(qrc
		INPUT ../mui/default/res/icons.qrc
		OUTPUT office6/addons/konlinetablestyle/mui/default/icons.data
	)

	# konlinetablestyle前端资源
	wps_add_frontend_resource(
		kwebdocer_table
		BUILD_DEST_PATH office6/addons/konlinetablestyle/res/
	)
	wps_add_frontend_resource(
		version
		LOCATION ${WPS_FRONTEND_DIRECTORY}/kwebdocer_table
		BUILD_DEST_PATH office6/addons/konlinetablestyle/res/
	)
	
wps_end_package()