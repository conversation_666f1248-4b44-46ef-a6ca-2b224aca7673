﻿#include "stdafx.h"
#include <kso/framework/api/wppapi_old.h>
#include "kxtptablestylecommand.h"

class KxMainWindow;
class KxTpWppOnlineTableStyleCommand : public KxDocerTpTableStyleCommand
{
public:
	KxTpWppOnlineTableStyleCommand(KxMainWindow* host, QObject* parent);
	virtual PanelEnableStatus judgeEnableInsertTable() override;
	virtual OperatorErrorCode applyTableStyleDocument(const KOTSResourceInfo& resourceInfo, const QByteArray& content, bool hover) override;
	virtual OperatorErrorCode applySystemTableStyleDocument(int resourceIndex, bool hover);
};

