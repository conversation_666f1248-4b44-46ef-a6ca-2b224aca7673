﻿#include "stdafx.h"
#include "kxtponlinetablestylewidget.h"
#include "kxonlinetablestylejsapi.h"
#include "kxonlinereshelper.h"
#include <kxshare/kxdocerunifypaydlgproxy.h>
#include <kcomctl/kquickhelpbarwidget.h>
#include <ksolite/kcommonwebwidget.h>
#include <ksolite/kxjsonhelper.h>
#include <krt/product.h>
#include <auth/productinfo.h>
#include <krt/dirs.h>
#include <ksolite/kdcinfoc.h>
#include "kdocertoolkit/docerfeature/kxdocertpfeatureutil.h"
#include "kxtableemphasize.h"
#include "kso/aibeautifytool/aibeautifytool_i.h"
#include "kdocertoolkit/kdocerutils.h"

#include "ktablebeautifypreviewhelper.h"
#include "kxwpstablebeautifymgr.h"

namespace
{
	const QString strNoTableMsg = "To use the advance table beautify, "
		"table needs to be selected, and then use the docer table style "
		"after selecting the number of rows and columns to be inserted in Insert-Table.";

	const QString strHorizontal = "horizontal";
	const QString strVertical = "vertical";
	const QString strOk = "isOk";
	const QString strStyleId = "styleId";
	const QString strPos = "pos";
};

using namespace KxOnlineTableResHelper;
KxOnlineTableStyleJsApi::KxOnlineTableStyleJsApi(KxWebViewContainer* webView, KxOnlineTableStyleWidget* onlineWidget)
	: KDocerCommonJsApi(webView, onlineWidget)
	, m_webView(webView)
	, m_onlineWidget(onlineWidget)
	, m_previewWidget(nullptr)
	, m_bShowPreview(false)
{
	connect(KWpsOneKeyTableArrangeMgr::getInstance(), SIGNAL(applyTableStyle(const KOTSResourceInfo&, const ReportInfo&, const QString&, bool, ksolite::KxCommonJsApi*)),
		this, SLOT(onApplyTableStyle(const KOTSResourceInfo&, const ReportInfo&, const QString&, bool, ksolite::KxCommonJsApi*)));
	connect(KTableEmphasizeMgr::getInstance(), SIGNAL(sigSelectionChanged()),
		SLOT(selectionChanged()));
	KxOnlineTableStyleJsApiHelper::Instance().addApi(this);
}
void KxOnlineTableStyleJsApi::isBeautifyAvaliable(KxWebViewJSContext& context)
{
	Json::Value res;
	res["isOk"] = true;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::isVerticalEmphasizeAvaiable(KxWebViewJSContext& context)
{
	Json::Value res;
	res["isOk"] = true;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::getSkinInfo(KxWebViewJSContext& context)
{
	Json::Value res;
	res["skinIdx"] = KWpsOneKeyTableArrangeMgr::getInstance()->readCurStyleIdIndex();
	res["isOk"] = true;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::getTableInfo(KxWebViewJSContext& context)
{
	Json::Value res;
	res["isOk"] = true;
	res["selectedType"] = KTableEmphasizeMgr::getInstance()->getTableSeletedInfo().toStdString();
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::getEmphasizeInfo(KxWebViewJSContext& context)
{
	KTableRowColInfo rcInfo; 
	KTableEmphasizeMgr::getInstance()->getTableInfo(rcInfo);
	QVariantMap res;
	res[strOk] = true;
	res["rowCount"] = rcInfo.rowCnt;
	res["colCount"] = rcInfo.colCnt;
	res["selectedRow"] = rcInfo.curSelectRow;
	res["selectedCol"] = rcInfo.curSelectCol;

	QMap<QString, KEmphasizeInfo> emInfo = KTableEmphasizeMgr::getInstance()->getEmphasizeInfo();
	QVariantMap horz = { {strOk, false}, {strPos, 0}, {strStyleId, 0} };
	if (emInfo.contains(strHorizontal))
	{
		horz[strOk] = true;
		horz[strPos] = emInfo[strHorizontal].emphasizeNum;
		horz[strStyleId] = emInfo[strHorizontal].emphasizeType;
		
	}
	res[strHorizontal] = horz;

	QVariantMap vert = { {strOk, false}, {strPos, 0}, {strStyleId, 0} };
	if (emInfo.contains(strVertical))
	{
		vert[strOk] = true;
		vert[strPos] = emInfo[strVertical].emphasizeNum;
		vert[strStyleId] = emInfo[strVertical].emphasizeType + 3;
	}
	res[strVertical] = vert;

	setResult(context, res);
}

void KxOnlineTableStyleJsApi::onekeyStructBeautify(KxWebViewJSContext& context)
{
	if (!KTableEmphasizeMgr::getInstance()->isTableValid())
	{
		kxApp->messageBox(tr(strNoTableMsg.toStdString().c_str()), MB_ICONEXCLAMATION | MB_OK);
		return;
	}

	QVector<single> colWidths, rowHeights;	
	bool adjustTable = false;
	if (onekeybeautify::isTableHorCenter())
		onekeybeautify::getTableColWidthAndRowHeight(colWidths, rowHeights);
	else
		adjustTable = true;
	KWpsOneKeyTableArrangeMgr::getInstance()->oneKeyTableArrange(this);

	if (!adjustTable)
		adjustTable = onekeybeautify::isAdjustedTable(colWidths, rowHeights);
	notifyOnekeyBeautifyFinished(adjustTable);
	selectionChanged();
}

void KxOnlineTableStyleJsApi::setSkin(KxWebViewJSContext& context)
{

}

void KxOnlineTableStyleJsApi::setEmphasize(KxWebViewJSContext& context)
{
	bool bOk = false;
	if (!KTableEmphasizeMgr::getInstance()->isTableValid())
	{
		kxApp->messageBox(tr(strNoTableMsg.toStdString().c_str()), MB_ICONEXCLAMATION | MB_OK);
	}
	else if (isInPreview())
	{
		endPreview(true);
		bOk = true;
	}
	else
	{
		const QVariantMap& args = parseContextArgs(context);
		if (args.isEmpty())
			return;

		int pos = args.value("pos").toInt();
		int styleId = args.value("id").toInt();
		QString type = args.value("type").toString();
		bOk = KTableEmphasizeMgr::getInstance()->emphasizeTable(pos, styleId, type);
	}

	Json::Value res;
	res["isOk"] = bOk;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::clearEmphasize(KxWebViewJSContext& context)
{
	QVariantMap args = parseContextArgs(context);
	QString type = args.value("type").toString();

	bool isOk = KTableEmphasizeMgr::getInstance()->emphasizeNone(type);

	Json::Value res;
	res["isOk"] = isOk;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::beginOpPreview(KxWebViewJSContext& context)
{
	const QVariantMap args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	bool isOk = false;
	if (canHover())
	{
		QString type = args.value("action").toString();
		if (type == "emphasize")
		{
			int pos = args.value("pos").toInt();
			int styleId = args.value("id").toInt();
			QString type = args.value("type").toString();

			beginPreview(KTableSelectionNotify::tr("emphasize"));
			isOk = KTableEmphasizeMgr::getInstance()->emphasizeTable(pos, styleId, type);
		}

		if (!isOk)
			endPreview(false);
	}

	Json::Value res;
	res["isOk"] = isOk;
	setResult(context, res);
}

void KxOnlineTableStyleJsApi::endOpPreview(KxWebViewJSContext& context)
{
	endPreview(false);
}

void KxOnlineTableStyleJsApi::getInitTabIndex(KxWebViewJSContext& context)
{

}

void KxOnlineTableStyleJsApi::setNotAutoPopupWithinSevenDays(KxWebViewJSContext& context)
{

}

void KxOnlineTableStyleJsApi::isNotAutoPopupWithinSevenDays(KxWebViewJSContext& context)
{

}

void KxOnlineTableStyleJsApi::isTableAvailable(KxWebViewJSContext& context)
{
	bool bOk = KTableEmphasizeMgr::getInstance()->isTableValid();
	QVariantMap res;
	
	if (bOk)
	{
		res["error_code"] = 0;
	}
	else
	{
		res["error_code"] = 2;
		res["error_msg"] = tr("There is no table currently. Please insert the table and try again.");
	}
	
	return setResult(context, res);
}

void KxOnlineTableStyleJsApi::selectionChanged()
{
	callbackToJS("selectionChanged", QString());
}

void KxOnlineTableStyleJsApi::onekeyBeautifyFinished(bool success)
{
	notifyOnekeyBeautifyFinished(success);
}

void KxOnlineTableStyleJsApi::notifyOnekeyBeautifyFinished(bool success /*= true*/)
{
	if (isInPreview())
		return;

	QVariantMap result;
	result["error_code"] = (int)!success;
	callbackToJS("onekeyBeautifyFinished", formatResult(result));
}

QString KxOnlineTableStyleJsApi::getTableSelectedType() const
{
	return "";
}

QString KxOnlineTableStyleJsApi::getModuleName() const
{
	return "docer_tablestyle";
}

bool KxOnlineTableStyleJsApi::eventFilter(QObject* o, QEvent* e)
{
	return false;
}

bool KxOnlineTableStyleJsApi::isInPreview()
{
	if (KTableBeautifyPreviewHelper::instance()->isInPreview())
		return true;

	return false;
}

void KxOnlineTableStyleJsApi::beginPreview(LPCWSTR desc)
{
}

void KxOnlineTableStyleJsApi::beginPreview(const QString& desc)
{
	QWidget* webView = m_webView ? m_webView->getWebView() : nullptr;
	KTableBeautifyPreviewHelper::instance()->beginPreview(webView, desc);
}

void KxOnlineTableStyleJsApi::endPreview(bool isCommit)
{
	KTableBeautifyPreviewHelper::instance()->endPreview(isCommit);
}

void KxOnlineTableStyleJsApi::applyEx(KxWebViewJSContext& context)
{
	return insertOnlineResource(context);
}

void KxOnlineTableStyleJsApi::beginPreviewEx(KxWebViewJSContext& context)
{
	return hoverOnlineResource(context);
}

void KxOnlineTableStyleJsApi::endPreviewEx(KxWebViewJSContext& context)
{
	return leaveOnlineResource(context);
}