#include "stdafx.h"
#include <kso/api/wpsapi_old.h>
using namespace wpsoldapi;
#include "kxonlinereshelper.h"

namespace KxOnlineTableResHelper
{
	bool wpsHasActiveTable()
	{
		IKApplication* pCoreApp = kxApp->coreApplication();
		if (!pCoreApp)
			return false;

		IKSelection* pCoreSelection = pCoreApp->GetSelection();
		if (!pCoreSelection)
			return false;

		ks_stdptr<IKTxSelection> spTxSelection;
		HRESULT hr = pCoreSelection->QueryInterface(IID_IKTxSelection, (void**)&spTxSelection);
		if (FAILED(hr) || !spTxSelection)
			return false;

		KsoSelectionType type;
		hr = spTxSelection->GetType(&type);
		if (FAILED(hr))
			return false;

		return (KsoSelectionTypeMajor(type) == ksoselectiontext) &&
			(KsoSelectionTypeMinor(type) & _TxSelectionType_Table) &&
			(KsoSelectionTypeMinor(type) != TxSelectType_TableLineEndIP);
	}

	void wpsUndo(int setup)
	{
		if (!wpsHasActiveTable())
			return;

		ks_stdptr<_Application> pCoreApp = kxApp->coreApplication();
		if (!pCoreApp)
			return;

		ks_stdptr<_Document> spDocument;
		HRESULT hr = pCoreApp->get_ActiveDocument(&spDocument);
		if (FAILED(hr) || !spDocument)
			return;

		VARIANT_BOOL result;
		spDocument->Undo(1, &result);
	}
};