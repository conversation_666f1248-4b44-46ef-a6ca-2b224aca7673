﻿#pragma once
#include <common/jsapi/kdocercommonjsapi.h>
#include <kcomctl/kcloudserviceproxy.h>
#include "kxonlinetablestyledefine.h"
#include "common/jsapi/hoverpreview/kdocercommonpreviewdlg.h"
#include <kso/framework/corenotify.h>

#ifdef WPP_PROJECT
#include "../common/toolkit/algorithm/kxdocershapenotify.h"
#include "kdocerpage/src/kpromedocerapi.h"
class KQhPopupHelper;
class KTableOnekeyBeautifyHelper;
#endif
class KxOnlineTableStyleWidget;


class KxOnlineTableStyleJsApi : public KDocerCommonJsApi, public IKCoreNotifyFilter
{
	Q_OBJECT
public:
	KxOnlineTableStyleJsApi(KxWebViewContainer* webView, KxOnlineTableStyleWidget* host);
	~KxOnlineTableStyleJsApi();

	void RegisterNotifyFilter();
	void UnRegisterNotifyFilter();
signals:
	void showView();
	void insertOnlineResource(const KOTSResourceInfo& info, const ReportInfo& reportInfo, const QString& filePath, bool hover);
	void leaveOnlineResource();

	void insertSystemResource(int index, bool hover);
	void leaveSystemResource();

	void sendDownloadInfo(const QString& strResourceId, const ReportInfo& reportInfo, int errorCode, const QString& errorDesc);
	void sigTabIndexChanged(int index);

	void sigApplyArrange(const QString& type, bool bApplyAll);
	void sigAnalyzeTable(TableAnalyzeFrom from);
	void sigCancelAnalyze();
	void sigUpdateTableView();
	void sigGetHasMultiChildTable(bool bApplyAll);
public:
	void pannelEnable(PanelEnableStatus status);
	void tabIndexChanged(int index);
	void notifyDisplay(const QString& entrance, const QString& detailEntrance, const QString& paySource);
	void gridlineChckedChnage(bool bChecked);
	void identifyResultNotify(TableRecoStaus status, TableAnalyzeFrom from);
	void arrangeResultNotify(bool bSuccess, TableArrangeResult errorType, qint64 totalTimes, TableApplyInfo tableApplyInfo);
	//预览相关
	virtual void notifyJsHidePreview() override;
protected:
	STDPROC_(BOOL) OnFilterNotify(ksoNotify* notify);
	void listenSelectionChanged();
public slots:
	void canHover(KxWebViewJSContext& context);
	void webLoadFinish(KxWebViewJSContext& context);
	void getAppInfo(KxWebViewJSContext& context);
	void openTaskPane(KxWebViewJSContext& context);
	void insertOnlineResource(KxWebViewJSContext& context);
	void hoverOnlineResource(KxWebViewJSContext& context);
	void leaveOnlineResource(KxWebViewJSContext& context);
	void canHoverOnlineResource(KxWebViewJSContext& context);
	void onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);
	void onDownloadProgress(const QString& id, int);
	void onDownloadSuccess(const QString& id, const QString& savePath);
	void onDownloadError(const QString& id, DownloadFailInfo info);
	void boardcastCustomMessage(KxWebViewJSContext& context);
	void isShowTableStyleQhTab(KxWebViewJSContext& context);
	void isMoreSettingVisible(KxWebViewJSContext& context);
	void getCustomColor(KxWebViewJSContext& context);
	void getReplaceColorList(KxWebViewJSContext& context);
	void getThemeColorList(KxWebViewJSContext& context);
	void testInsertOnlineResource(KxWebViewJSContext& context);
	void rsaEncrypt(KxWebViewJSContext& context); 
	void getResourceFilePath(KxWebViewJSContext& context);

	void isHasMuiltiChildTable(KxWebViewJSContext& context);
	void onGetHasMultiChildTable(bool bSuccess, bool bHasMultiChildTable);

	//预览相关
	void showPreview(KxWebViewJSContext& context);
	void hidePreview(KxWebViewJSContext& context);
	void getShowPreviewStatus(KxWebViewJSContext& context);

	//系统皮肤切换接口
	void insertSystemResource(KxWebViewJSContext& context);
	void hoverSystemResource(KxWebViewJSContext& context);
	void leaveSystemResource(KxWebViewJSContext& context);
	void hasActiveTable(KxWebViewJSContext& context);
	

	void onSystemInsertFinished(int resourceId, OperatorErrorCode errorCode, const QString& errorDesc);
	void onSystemInsertSuccess(int resourceId);
	void onSystemInsertError(int resourceId, OperatorErrorCode errorCode, const QString& errorDesc);
	void onSelectionChange(KxMainWindow*);

	//美化相关
	void isBeautifyAvaliable(KxWebViewJSContext& context);
	void isVerticalEmphasizeAvaiable(KxWebViewJSContext& context);
	void getSkinInfo(KxWebViewJSContext& context);
	void getTableInfo(KxWebViewJSContext& context);
	void getEmphasizeInfo(KxWebViewJSContext& context);
	void onekeyStructBeautify(KxWebViewJSContext& context);
	void setSkin(KxWebViewJSContext& context);
	void setEmphasize(KxWebViewJSContext& context);
	void clearEmphasize(KxWebViewJSContext& context);
	void beginOpPreview(KxWebViewJSContext& context);
	void endOpPreview(KxWebViewJSContext& context);
	void getInitTabIndex(KxWebViewJSContext& context);
	void setNotAutoPopupWithinSevenDays(KxWebViewJSContext& contxt);
	void isNotAutoPopupWithinSevenDays(KxWebViewJSContext& context);
	void isTableAvailable(KxWebViewJSContext& context);
	void selectionChanged();
	void onekeyBeautifyFinished(bool success);
	void notifyOnekeyBeautifyFinished(bool success = true);
	QString getTableSelectedType() const;
#ifdef WPP_PROJECT
	void changeFont(KxWebViewJSContext& context);
	void onDownloadFontSuccess(const QString& id, const QString& fileName);
	void onDownloadFontProgress(const QString& id, int nPercent);
	void onDownloadFontError(const QString& id, const DownloadFailInfo& info);
	void onGetUploadFileUrl(const QString& url);
	void getRecommendTableStyle(KxWebViewJSContext& context);
	void getSupportSkinIdxRange(KxWebViewJSContext& context);
	void onDocChanged();
	void callbackToJS(const QString& callback, const QString& result, bool bBase64 = true) override;
	void notifyGetRecommendRes();
#endif
	void canHoverEx(KxWebViewJSContext& context);
	void applyEx(KxWebViewJSContext& context);
	void beginPreviewEx(KxWebViewJSContext& context);
	void endPreviewEx(KxWebViewJSContext& context);
	void getEntrance(KxWebViewJSContext& context);
	void switchAffectType(KxWebViewJSContext& context);
	void triggerGridLine(KxWebViewJSContext& context);
	void doTableAnalyze(KxWebViewJSContext& context);
	void cancelAnalyze(KxWebViewJSContext& context);
	void updateTableView(KxWebViewJSContext& context);

	//转图表
	void wpsInsertChart(KxWebViewJSContext& context);
	void isDsDataFitRule(KxWebViewJSContext& context);
	void getTableTextAsRule(KxWebViewJSContext& context);

	void getCurrentGridlineStatues(KxWebViewJSContext& context);

	void querySupportInterface(KxWebViewJSContext& context);
	void registerTablestyleThemeChange(KxWebViewJSContext& context);
protected:
	virtual ksolite::KxCommonWebDialog* createNewUrlWidget(QWidget* parent,
		int nWidth, int nHeight, bool bModal, bool bCloseBtn) override;
	virtual QString getSkinConfigPath() const override;
	virtual QString getModuleName() const override;
	virtual bool eventFilter(QObject* o, QEvent* e) override;
	//预览相关
	void initPreviewDlg();
	void initQuickBarPreviewDlg();
	bool isDsDataFitDataTypeRule(const QString& rule);
protected:
	void onInsertSuccess(QVariantMap& result, const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo);
	void onInsertError(QVariantMap& result, const KOTSResourceInfo& resourceInfo, OperatorErrorCode errorCode, const QString& errorDesc);
	void onInsertSkip(QVariantMap& result, const KOTSResourceInfo& resourceInfo, OperatorErrorCode errorCode);
	void onInsertBatchStart(QVariantMap& result, OperatorErrorCode errorCode);
	void onInsertBatchFinish(QVariantMap& result, const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode);
	void downloadResouce(const KOTSResourceInfo& info, const QString& strDownloadUrl, const QString& strSavePath, const ReportInfo& reportInfo);
	void removeDownloadTask(const QString& id);
	void clearAllDownloadTask();
	bool canHover();
	bool isInPreview();
	void beginPreview(LPCWSTR desc);
	void beginPreview(const QString& desc);
	void endPreview(bool isCommit = false);
	virtual QString getExternalName() override;
	virtual QWidget* widgetParent() override;
	bool isActiveWindow();
	docer::base::KDocerWidgetType getHostType();
	bool isPopupWidget();
	QString getPreviewNextOp();
#ifdef WPP_PROJECT
	bool downloadOnlineFont(const QStringList& urls);
	void applyRes(bool isFileExist);
	bool applyFont(const QString& name);
	void changeFontMessage(int fontId, const QString& message);
	void cancelDownloadFont();
	void releasedDownloadFontTask();
#endif

	bool isQuickBar();
#ifdef WPP_PROJECT
private:
	bool uploadToKS3(const QString& imgStr);
#endif

private slots:
	void onApplyTableStyle(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, const QString& filePath, bool hover, ksolite::KxCommonJsApi* jsApi);

private:
#ifdef WPP_PROJECT
	QPointer<KDocerDownloader> m_downloadFontTask;
	KOnlineFontData m_onlineFontData;
#endif
	QString m_hoverOnlineResId;
	KxWebViewContainer*	m_webView;
	QMap<QString, QList<QPair<KOTSResourceInfo, ReportInfo>>> m_downloadInfoMap;
	QMap<QString, QString> m_downloadId2ResId;
	QVector<KDocerDownloader*>	m_downloadTasks;
	KxOnlineTableStyleWidget*	m_onlineWidget;
	//预览相关
	KDocerCommonPreviewDlg* m_previewWidget;
	QPointer<KDocerCommonPreviewDlg> m_quickBarPreviewWidget;
	QString	 m_curPreivewPicId;
	bool m_bShowPreview;
	QString	 m_hidePreviewId;
	QMap<QString, QVariantMap> m_broadcastInfo;
	KMainWindow* m_originWnd;
	bool m_bRegisterTablestyleThemeChange = false;
#ifdef WPP_PROJECT
	int m_emphasizeRow;
	int m_emphasizeCol;
	QPointer<KQhPopupHelper> m_qhPopupHelper;
	QPointer<KTableOnekeyBeautifyHelper> m_onekeyBeautifyHelper;
	QTimer m_selChangeNotifyFilter;
#endif
};

class KxOnlineTableStyleJsApiHelper : public QObject, public ksolite::KxCommonJsApiHelper
{
	Q_OBJECT
public:
	static KxOnlineTableStyleJsApiHelper& Instance()
	{
		static KxOnlineTableStyleJsApiHelper _instance;
		return _instance;
	}

	QString getResSavePath(const QString& resUrl, const QString& resId);

private:
	int getResIdSum(const QString& resId);
	QString downloadPath();

private:
	explicit KxOnlineTableStyleJsApiHelper() {}
	~KxOnlineTableStyleJsApiHelper() {}

public slots:
	void onCallBackToJs(const QString& method, const QString& param);
};