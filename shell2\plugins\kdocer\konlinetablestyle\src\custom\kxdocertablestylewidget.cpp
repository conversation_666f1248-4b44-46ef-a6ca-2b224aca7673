﻿#include "stdafx.h"
#include "kxdocertablestylewidget.h"
#include "kxdocertablestylegalleryitem.h"
#include "kxonlinereshelper.h"
#include "ktablestyleinfocollect.h"
#include "kcomctl/kgallerymodel.h"
#include "ksolite/kdocer/kdocercoreitef.h"
#include "common/widget/kdocerwidgetutil.h"
#include <public_header/kliteui/uicontrol/klitelabel.h>
#include <public_header/kliteui/uicontrol/klitewidget.h>
#include <public_header/kliteui/uicontrol/kseparatorline.h>
#include "kcomctl/kpluginloadwidget.h"
#include "kdocertoolkit/kdocerutils.h"
#include "kdocertoolkit/docerfeature/kxdocertpfeatureutil.h"
#include "common/widget/docerlitetoast/kdocerlisttoasthelper.h"
#include <kcomctl/kappskin.h>
#include "kdocercorehelper.h"

#define TABLESTYLE_COLUMN_COUNT 4
KxDocerTableStyleWidget::KxDocerTableStyleWidget(QWidget* parent, KCommand* cmd, const QString& cmdName)
	:QWidget(parent),
	m_cmd(cmd),
	m_cmdName(cmdName),
	m_dwLastTick(0),
	m_dwLoadTick(0)
{
	auto docerCoreImpl = getIKDocerCore();
	if (docerCoreImpl)
		docerCoreImpl->applyFunc("registerExtendResource", QVariantMap());

	initWidget();
}

KxDocerTableStyleWidget::~KxDocerTableStyleWidget()
{
}

QSize KxDocerTableStyleWidget::sizeHint() const
{
	QSize fixSize = QSize(-1, -1);
	auto mainWnd = kxApp->findRelativeMainWindowX((KxDocerTableStyleWidget*)this);
	if (mainWnd)
		fixSize = KPluginLoadWidget::getDocerWidgetAdaptiveSize(mainWnd, adaptiveSizeName());
	return fixSize;
}

void KxDocerTableStyleWidget::customEvent(QEvent* e)
{
	if (e->type() == KEvent::Completed)
	{
		e->setAccepted(true);
	}
	QWidget::customEvent(e);
}

void KxDocerTableStyleWidget::showEvent(QShowEvent* event)
{
	QWidget::showEvent(event);
	QHash<QString, QString> args;
	args.insert("first_entry", "restab");
	args.insert("second_entry", KxOnlineTableResHelper::getSecondEntry(m_cmdName));
	KTableStyleInfoCollect::postDispalyInfo(args);
	m_dwLastTick = QDateTime::currentMSecsSinceEpoch();
}

void KxDocerTableStyleWidget::hideEvent(QHideEvent* event)
{
	QWidget::hideEvent(event);

	DWORD duration = QDateTime::currentMSecsSinceEpoch() - m_dwLastTick;

	QHash<QString, QString> args;
	args.insert("first_entry", "restab");
	args.insert("second_entry", KxOnlineTableResHelper::getSecondEntry(m_cmdName));
	KTableStyleInfoCollect::postStayInfo(args, duration);
	m_dwLastTick = 0;
}

int KxDocerTableStyleWidget::getActiveGalleryItemCount()
{
	auto policy = docer::widget::getMainWndHeightPolicy(this);
	if (policy == docer::widget::MainWndHeightPolicy::Between_1080_1440)
		return TABLESTYLE_COLUMN_COUNT * 5;
	else if (policy == docer::widget::MainWndHeightPolicy::Over_1440)
		return TABLESTYLE_COLUMN_COUNT * 7;
	return TABLESTYLE_COLUMN_COUNT * 2;
}

void KxDocerTableStyleWidget::initWidget()
{	
	m_resourceLayout = new QStackedLayout(this);
	m_resourceLayout->insertWidget(KxDocerTableStyleGalleryModel::State::Loading, createLoadingWidget(this));
	m_resourceLayout->insertWidget(KxDocerTableStyleGalleryModel::State::NoResource, createErrorWidget(this, tr("resource load failed")));
	m_resourceLayout->insertWidget(KxDocerTableStyleGalleryModel::State::NoInternet, createErrorWidget(this, tr("resource load failed")));
	m_resourceLayout->insertWidget(KxDocerTableStyleGalleryModel::State::InternetTimeout, createErrorWidget(this, tr("resource load timeout")));
	m_resourceLayout->insertWidget(KxDocerTableStyleGalleryModel::State::GalleryItem, createGalleryWidget());

	onStateChanged(KxDocerTableStyleGalleryModel::State::Loading);
	setLayout(m_resourceLayout);
}

void KxDocerTableStyleWidget::openDocerFeatureTp()
{
	closePopupWidget();

	QString secondEntry = KxOnlineTableResHelper::getSecondEntry(m_cmdName) % "_more";
	QVariantMap klmInfo;
	klmInfo["pluginName"] = "konlinetablestyle";
	klmInfo["targetId"] = "processTableStyle";
	klmInfo["func"] = "docer_infeature";
	klmInfo["second_func"] = "tablestyle";
	klmInfo["first_entry"] = "restab";
	klmInfo["second_entry"] = secondEntry;
	klmInfo["page_name"] = "table_dropdown_page";
	klmInfo["shotcutPar"] = QVariantMap({ {"tabName", "style"}, {"secondEntry", secondEntry} });
	klmInfo[DocerTpFeature::s_strAutoPop] = false;
	KxDocerTpFeatureUtil::showTpObjectBeautify(this, klmInfo);

	QHash<QString, QString> args;
	args.insert("first_entry", "restab");
	args.insert("second_entry", KxOnlineTableResHelper::getSecondEntry(m_cmdName));
	KTableStyleInfoCollect::postMoreBtnClicked(args);
}

QWidget* KxDocerTableStyleWidget::createGalleryWidget()
{
	QWidget* container = new QWidget(this);

	KSeparatorLine* separatorLine = new KSeparatorLine(container, Qt::Horizontal);
	separatorLine->setMargin(KLiteStyle::dpiScaledMargins(6, 2, 6, 2));
#ifdef Q_OS_MACOS	//临时兼容，兼容mac下中台预设样式往左偏移4px
	if (KDocerUtils::isEtApp(this))
		separatorLine->setMargin(KLiteStyle::dpiScaledMargins(2, 2, 6, 2));
#endif

	KLiteButton* moreButton = new KLiteButton(container);
	moreButton->applyThemeClass("KDocerTableStyleMoreWidget");
	moreButton->setContentsMargins(KLiteStyle::dpiScaledMargins(6, 4, 6, 4));
	moreButton->setFixedSize(KLiteStyle::dpiScaledSize(402, 32));
	moreButton->setText(tr("more tablestyles"));
	moreButton->setSpacing(8);
	QIcon customMoreIcon = kApp->loadIcon("custom_more");
	moreButton->setIcon(kApp->skin()->bindIconColor(moreButton, customMoreIcon));
	moreButton->setIconSize(KLiteStyle::dpiScaledSize(16, 16));
	moreButton->setIconPosition(KLiteButton::OnTextRight);
	moreButton->setAlignment(Qt::AlignmentFlag::AlignRight);
	moreButton->setCursor(Qt::PointingHandCursor);
	moreButton->setProperty("qtspyName", "MoreTablestyles");
	connect(moreButton, &KLiteButton::clicked, this, &KxDocerTableStyleWidget::openDocerFeatureTp);

	m_model = new KxDocerTableStyleGalleryModel(KDocerUtils::getCoreApplication(this), this, m_cmd, getActiveGalleryItemCount(), m_cmdName, false);
	m_model->setColumns(TABLESTYLE_COLUMN_COUNT);
	connect(m_model, &KxDocerTableStyleGalleryModel::stateChanged, this, &KxDocerTableStyleWidget::onStateChanged);
	connect(m_model, &KxDocerTableStyleGalleryModel::sigDownloadError, this, &KxDocerTableStyleWidget::onDownloadError);
	connect(m_cmd, SIGNAL(sendInsertInfo(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&))
		, this, SLOT(onInsertFinished(const KOTSResourceInfo&, const ReportInfo&, OperatorErrorCode, const QString&)));

	KxDocerTableStyleGalleryView* galleryView = new KxDocerTableStyleGalleryView(m_model, container);
	connect(galleryView, SIGNAL(leaveOnlineResource()), m_cmd, SIGNAL(sigLeaverHover()));

	QVBoxLayout* vLayout = new QVBoxLayout(container);
	vLayout->setSpacing(0);
	vLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(8, 0, 8, 0));
#ifdef Q_OS_MACOS	//临时兼容，兼容mac下中台预设样式往左偏移4px
	if (KDocerUtils::isEtApp(this))
		vLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(4, 0, 8, 0));
#endif
	vLayout->addWidget(separatorLine, Qt::AlignTop);
	vLayout->addSpacing(KLiteStyle::dpiScaled(6));
	vLayout->addWidget(galleryView);
	vLayout->addWidget(moreButton, Qt::AlignmentFlag::AlignBottom);
	if (KDocerUtils::isWpsApp(this))
		vLayout->addSpacing(KLiteStyle::dpiScaled(2));
	else
		vLayout->addSpacing(KLiteStyle::dpiScaled(6));

	return container;
}

QWidget* KxDocerTableStyleWidget::createLoadingWidget(QWidget* parent)
{
	return KPluginLoadWidget::createPluginLoadWidget(parent, adaptiveSizeName());
}

QWidget* KxDocerTableStyleWidget::createErrorWidget(QWidget* parent, const QString& text)
{
	KLitePluginLoadFailedWidget* widget = new KLitePluginLoadFailedWidget(parent, text);
	connect(widget, &KLitePluginLoadFailedWidget::retryDownload, this, &KxDocerTableStyleWidget::onRetryClicked);
	return widget;
}

QString KxDocerTableStyleWidget::adaptiveSizeName() const
{
	if (KDocerUtils::isWpsApp(this))
		return QLatin1String("konlinetablestyle_dp_wps_size");
	else
		return QLatin1String("konlinetablestyle_dp_size");
}

void KxDocerTableStyleWidget::onRetryClicked()
{
	m_model->setDirty(true);
	m_model->fetchItems();
	kxApp->ForceIdle();
}

void KxDocerTableStyleWidget::onStateChanged(int state)
{
	m_resourceLayout->setCurrentIndex(state);
	kxApp->ForceIdle();

	if (state == KxDocerTableStyleGalleryModel::State::Loading)
	{
		m_dwLoadTick = QDateTime::currentMSecsSinceEpoch();
	}
	else
	{
		QHash<QString, QString> args;
		args.insert("duration", QString::number(QDateTime::currentMSecsSinceEpoch() - m_dwLoadTick));
		args.insert("status", state == KxDocerTableStyleGalleryModel::State::GalleryItem ? "1" : "0");
		args.insert("first_entry", "restab");
		args.insert("second_entry", KxOnlineTableResHelper::getSecondEntry(m_cmdName));
		KTableStyleInfoCollect::postLoadInfo(args);
		m_dwLoadTick = 0;
	}
}

void KxDocerTableStyleWidget::onInsertFinished(const KOTSResourceInfo& resourceInfo, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc)
{
	if (errorCode == InsertOpCansel)
	{
		sigInsertFinished(resourceInfo, reportInfo, errorCode, errorDesc);
		return;
	}

	QWidget* toastHostWidget = nullptr;
	KPopupWidget* popupWidget = KTik::findParentByType<KPopupWidget>(parent());
	if (popupWidget && popupWidget->contentWidget())
	{
		KGallery* galleryWidget = popupWidget->contentWidget()->findChild<KGallery*>();
		if (galleryWidget && galleryWidget->getView())
			toastHostWidget = galleryWidget->getView()->galleryView();
	}

	KDocerLiteToastHelper::KDocerLiteToastParam toastParam;
	if(toastHostWidget)
	{
		toastParam.host = toastHostWidget;
		toastParam.align = Qt::AlignCenter | Qt::AlignTop;
		toastParam.bDefaultCloseBtnVisible = false;
		toastParam.stayDuration = 1500;

		switch (errorCode)
		{
		case Success:
			toastParam.msgType = KLiteToastWidget::MessageType_Success;
			toastParam.text = tr("Table recognize success");
			break;
		case TableRecognizeFailed:
			toastParam.msgType = KLiteToastWidget::MessageType_Error;
			toastParam.text = tr("Table recognize failed");
			break;
		case LastApplyItemChanged:
			toastParam.msgType = KLiteToastWidget::MessageType_Warning;
			toastParam.text = tr("The current volume of data is too large to collate");
			break;
		case InsertOpNoActiveTable:
			toastParam.msgType = KLiteToastWidget::MessageType_Warning;
			toastParam.text = tr("Please select the table area and try again");
			break;
		case DownloadError:
			toastParam.msgType = KLiteToastWidget::MessageType_Warning;
			toastParam.text = tr("Network exception, please try again later");
			break;
		default:
			toastParam.msgType = KLiteToastWidget::MessageType_Warning;
			toastParam.text = tr("Application failed. Please try again later");
		}
		KDocerLiteToastHelper::showTip(toastParam);
	}
	sigInsertFinished(resourceInfo, reportInfo, errorCode, errorDesc);
	if (errorCode == Success)
		closePopupWidget();
}

void KxDocerTableStyleWidget::onDownloadError(const KOTSResourceInfo& resourceInfo)
{
	onInsertFinished(resourceInfo, ReportInfo(), OperatorErrorCode::DownloadError, QString());
}

void KxDocerTableStyleWidget::closePopupWidget()
{
	KPopupWidget* popupWidget = nullptr;
	QWidget* widget = parentWidget();
	if (widget)
	{
		if (popupWidget = KTik::findParentByType<KPopupWidget>(widget))
		{
			KEvent event(KEvent::Completed);
			sendPropagatedEvent(popupWidget, &event);
		}
	}
}
