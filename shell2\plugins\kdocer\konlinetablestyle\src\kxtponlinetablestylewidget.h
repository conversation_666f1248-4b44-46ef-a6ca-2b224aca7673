﻿#pragma once

#include "kxonlinetablestyledefine.h"
#include <common/jsapi/widget/kdocercommonwebwidget.h>
#include <queue>

class KxDocerTpTableStyleCommand;

class KxOnlineTableStyleWidget : public KDocerCommonWebWidget
{
	Q_OBJECT
public:
	typedef std::function<void()> Closure;

	KxOnlineTableStyleWidget(QWidget* parent, KCommand* cmd, bool bIsInTaskPane, bool bIsQuickHelpWidget = false,
		bool bTopStyleShow = false, docer::base::KDocerWidgetType widgetType = docer::base::KDocerWidgetType::KDocerWidgetType_Default);
	~KxOnlineTableStyleWidget();

	void openTaskPane(const QString& url);
	bool isFromWorkBench();
	bool isFromQuickHelp();
	bool isFromBeautify();
	void notifyDisplay(bool show);
	void notifyUrlChanged(const QString& url);
	void setKLMinfo(const QVariantMap& params);

	bool getIsQuickHelpWidget() const { return m_bQuickHelpWidget; }
	bool getIsInTaskPane() const { return m_bIsTaskPaneWidget; }
	bool getIsTopstyleshow() const { return m_bTopStyleShow; }

	virtual QSize sizeHint() const override;
	void onParentReady();
protected:
	virtual ksolite::KxCommonJsApi* initJsObject() override;
	virtual void initWebView() override;
	virtual KxWebViewImplementationType webviewType() override;
	
	virtual QWidget* createWaitingWidget() override;
	QString getPaySource();
	void sendDisplayInfo();
	void sendStayInfo();
signals:
	void aboutToClose();
	void sigTabIndexChanged(int index);
	void sigCancelAnalyze();
public slots:
	void onHoverSystemResource();
	void onLeaveSystemResource();

	void onDrawingFinish();
	void pannelEnable(PanelEnableStatus status);
	void tabIndexChanged(int index);

	void onGridLineChecked(bool checked);
	void onIndentifyStatusChange(TableRecoStaus status, TableAnalyzeFrom from);
	void onArrangeApplyResult(bool bSuccess, TableArrangeResult errorType, qint64 totalTimes, TableApplyInfo tableApplyInfo);
	//信息上报
	void sendDownloadInfo(const QString& strResourceId, const ReportInfo& reportInfo, int errorCode, const QString& errorDesc);
	void sendInsertInfo(const KOTSResourceInfo& info, const ReportInfo& reportInfo, OperatorErrorCode errorCode, const QString& errorDesc);
protected:
	virtual bool event(QEvent* e) override;
	virtual bool eventFilter(QObject*o, QEvent*e) override;
	virtual void showEvent(QShowEvent* event) override;
	virtual void hideEvent(QHideEvent* e) override;
	virtual void closeWindow(CloseReason reason) override;
	virtual bool webViewEventFilter(QEvent* event) override;
	void callOnLoadFinish(Closure closure);
private:
	KCommand*					m_cmd;
	bool						m_bIsTaskPaneWidget;
	QPointer<KPopupWidget>		m_ancesor;
	TableInfo					m_lastTableInfo;
	bool						m_bSystemHovered;
	bool						m_bLoadFinish;
	DWORD						m_dwLastTick;
	std::queue<Closure>			m_delayClosures;
	bool						m_bQuickHelpWidget;
	bool						m_bTopStyleShow;
};